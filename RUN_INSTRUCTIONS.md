# How to Run the Minecraft Client/Server

If you're having trouble running the Minecraft client or server from IntelliJ IDEA, here are several methods you can try:

## Method 1: Using the Run Configurations in IntelliJ IDEA

1. Open your project in IntelliJ IDEA
2. Look for the run configurations dropdown in the top-right corner
3. Select one of the following configurations:
   - "Direct Minecraft Client" - to run the client
   - "Direct Minecraft Server" - to run the server
   - "Minecraft Client Gradle" - to run the client via Gradle
   - "Minecraft Server Gradle" - to run the server via Gradle

## Method 2: Using the Shell Scripts (Linux/macOS)

1. Open a terminal
2. Navigate to your project directory
3. Run one of the following commands:
   - `./run-client.sh` - to run the client
   - `./run-server.sh` - to run the server

## Method 3: Using the Batch Files (Windows)

1. Open File Explorer
2. Navigate to your project directory
3. Double-click one of the following files:
   - `run-client-direct.bat` - to run the client
   - `run-server-direct.bat` - to run the server

## Method 4: Using Gradle Directly

1. Open a terminal or command prompt
2. Navigate to your project directory
3. Run one of the following commands:
   - `./gradlew runClient` (Linux/macOS) or `gradlew runClient` (Windows) - to run the client
   - `./gradlew runServer` (Linux/macOS) or `gradlew runServer` (Windows) - to run the server

## Troubleshooting

If you're still having trouble:

1. Make sure Java is installed and properly configured
2. Try reimporting the project as a Gradle project
3. Check the Gradle console for any error messages
4. Make sure the Fabric Loom plugin is properly applied in your build.gradle file
