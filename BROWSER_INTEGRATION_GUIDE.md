# Browser Integration Implementation Guide

## Overview

This implementation adds browser engine integration to the PokeCobbleClaim Minecraft mod, allowing users to toggle between traditional Minecraft GUI and modern HTML-based interfaces.

## Features Implemented

### Core Browser System
- **BrowserEngine**: JCEF-based browser engine with async initialization
- **BrowserManager**: Manages browser instances and lifecycle
- **BrowserConfig**: Configuration system with JSON persistence
- **JavaScriptBridge**: Bidirectional communication between Java and JavaScript

### UI Toggle System
- **ScreenToggleManager**: Centralized screen switching between MC GUI and HTML
- **HtmlScreenBase**: Base class for HTML-based Minecraft screens
- **Command System**: `/pokecobbleclaim ui toggle` and related commands

### HTML Interface
- **Responsive Design**: Mobile-responsive with PC focus
- **Dark/Light Themes**: Toggleable theme system
- **Component System**: Reusable UI components (tooltips, modals, etc.)
- **Real-time Communication**: Live data sync with Minecraft server

## File Structure

```
src/main/java/com/pokecobble/
├── browser/
│   ├── core/
│   │   ├── BrowserEngine.java          # JCEF browser engine wrapper
│   │   ├── BrowserManager.java         # Browser instance management
│   │   └── BrowserConfig.java          # Configuration management
│   ├── bridge/
│   │   └── JavaScriptBridge.java       # Java ↔ JavaScript communication
│   ├── screens/
│   │   ├── HtmlScreenBase.java         # Base HTML screen class
│   │   └── HtmlTownScreen.java         # HTML town management screen
│   ├── ui/
│   │   └── ScreenToggleManager.java    # UI mode switching
│   └── command/
│       └── BrowserToggleCommand.java   # Toggle commands

src/main/resources/assets/pokecobbleclaim/html/
├── screens/
│   └── town_management.html            # Town management interface
├── css/
│   ├── main.css                        # Main styles
│   └── components.css                  # Component styles
└── js/
    ├── main.js                         # Main application logic
    ├── api.js                          # API communication layer
    ├── utils.js                        # Utility functions
    └── components.js                   # UI components
```

## Usage

### Commands
- `/pokecobbleclaim ui toggle` - Toggle between HTML and Minecraft GUI
- `/pokecobbleclaim ui mode html` - Force HTML mode
- `/pokecobbleclaim ui mode minecraft` - Force Minecraft GUI mode
- `/pokecobbleclaim ui status` - Show current UI status
- `/pokecobbleclaim browser init` - Initialize browser engine
- `/pokecobbleclaim browser debug` - Show debug information

### Configuration
Configuration is stored in `run/pokecobbleclaim/browser_config.json`:

```json
{
  "htmlUiEnabled": false,
  "enableDevTools": true,
  "enableFallback": true,
  "browserWidth": 1024,
  "browserHeight": 768,
  "theme": "dark",
  "maxBrowserInstances": 5
}
```

## Implementation Status

### ✅ Completed
- Core browser engine integration
- Configuration system
- JavaScript bridge
- Basic HTML town management screen
- Command system
- Theme support
- Responsive design foundation

### 🚧 In Progress
- Full browser rendering integration
- Input event forwarding
- Resource loading from mod assets

### 📋 Planned
- Convert remaining screens to HTML:
  - MyTownScreen → my_town.html
  - CreateTownScreen → create_town.html
  - PlayerManageScreen → player_management.html
  - ClaimHistoryScreen → claim_history.html
  - And 15+ other screens

## Development Notes

### Browser Engine Choice
- **Selected**: JCEF (Java Chromium Embedded Framework)
- **Reasoning**: Best compatibility with Minecraft Forge 1.20.1, mature Java bindings
- **Fallback**: JavaFX WebView for lighter alternative

### Architecture Decisions
1. **Singleton Pattern**: Used for core managers (BrowserEngine, BrowserManager, etc.)
2. **Async Initialization**: Browser engine initializes asynchronously to avoid blocking
3. **Graceful Fallback**: Always falls back to Minecraft GUI if HTML fails
4. **Component-Based**: Reusable HTML/JS components for consistency

### Performance Considerations
- Browser instance pooling (max 5 concurrent)
- Lazy loading of HTML resources
- Efficient data serialization for Java ↔ JavaScript communication
- Memory management for browser instances

## Testing

### Development Mode
The HTML interface includes a development mode that works without CEF:
- Simulated API responses
- Mock data for testing
- Console logging for debugging

### Browser Testing
1. Enable HTML UI: `/pokecobbleclaim ui mode html`
2. Initialize browser: `/pokecobbleclaim browser init`
3. Open town screen: `/town` command
4. Test functionality in HTML interface

## Troubleshooting

### Common Issues
1. **Browser fails to initialize**
   - Check JCEF dependencies in build.gradle
   - Verify Java version compatibility
   - Check logs for initialization errors

2. **HTML interface not loading**
   - Verify resource files exist in assets folder
   - Check browser console for JavaScript errors
   - Ensure fallback is enabled

3. **Communication errors**
   - Verify JavaScript bridge is properly initialized
   - Check for CEF query availability
   - Review network packet handling

### Debug Commands
- `/pokecobbleclaim ui debug` - Show browser and screen information
- `/pokecobbleclaim browser config` - Display current configuration
- Check mod logs for detailed error information

## Future Enhancements

### Phase 2 Features
- Advanced animations and transitions
- Custom CSS themes
- Drag-and-drop functionality
- Real-time notifications
- Multi-language support

### Phase 3 Features
- Plugin system for custom HTML screens
- Advanced data visualization
- Integration with external web services
- Mobile app companion

## Dependencies Added

```gradle
dependencies {
    // Browser engine integration
    implementation 'org.cef:jcef:122.1.10+gc902316+chromium-122.0.6261.112'
    
    // JSON processing
    implementation 'com.google.code.gson:gson:2.10.1'
    
    // JavaFX WebView fallback
    implementation 'org.openjfx:javafx-web:17.0.2'
    implementation 'org.openjfx:javafx-controls:17.0.2'
    implementation 'org.openjfx:javafx-fxml:17.0.2'
}
```

## Contributing

When adding new HTML screens:
1. Create HTML file in `src/main/resources/assets/pokecobbleclaim/html/screens/`
2. Extend `HtmlScreenBase` for the Java implementation
3. Register screen mapping in `ScreenToggleManager`
4. Add JavaScript handlers in the bridge
5. Test both HTML and fallback modes

## License

This browser integration follows the same license as the main PokeCobbleClaim mod.
