{"name": "github-authentication-sample", "displayName": "github-authentication-sample", "description": "Example of using the GitHub Authentication Provider", "version": "0.0.1", "publisher": "vscode-samples", "engines": {"vscode": "^1.74.0"}, "private": true, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Microsoft/vscode-extension-samples"}, "categories": ["Other"], "activationEvents": [], "main": "./out/extension.js", "contributes": {"commands": [{"command": "extension.getGitHubUser", "title": "Get GitHub User", "category": "GitHub Authentication Sample"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "lint": "eslint", "watch": "tsc -watch -p ./"}, "devDependencies": {"@eslint/js": "^9.13.0", "@stylistic/eslint-plugin": "^2.9.0", "@types/node": "^20", "@types/vscode": "1.74.0", "eslint": "^9.13.0", "typescript": "^5.8.2", "typescript-eslint": "^8.26.0"}, "dependencies": {"@octokit/rest": "^18.0.0"}}