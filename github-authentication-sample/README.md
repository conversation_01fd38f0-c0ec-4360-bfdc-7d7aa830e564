# GitHub Authentication Sample

This is a GitHub authentication example that shows you how to use the VS Code authentication API.

## Demo

![demo](demo.gif)

## VS Code API

### `vscode` module

- [`authentication.getSession`](https://code.visualstudio.com/api/references/vscode-api#authentication.getSession)

## Running the Sample

- Run `npm install` in terminal to install dependencies
- Run the `Run Extension` target in the Debug View. This will:
	- Start a task `npm: watch` to compile the code
	- Run the extension in a new VS Code window
