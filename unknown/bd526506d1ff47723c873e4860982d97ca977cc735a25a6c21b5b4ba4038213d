package com.pokecobble.town.claim;

import com.pokecobble.town.TownPlayerRank;

import java.util.EnumMap;
import java.util.Map;

/**
 * Handles permissions for different ranks in a more explicit way.
 * Each rank has its own set of permissions that are completely independent of other ranks.
 */
public class RankPermissions {
    // Maps each rank to a boolean array of permissions
    // The boolean array indices correspond to: [build, interact, containers, redstone, doors, crops, animals, villagers]
    private final Map<TownPlayerRank, boolean[]> rankPermissions = new EnumMap<>(TownPlayerRank.class);

    // Non-member permissions - all set to false by default
    private boolean allowBuild = false;
    private boolean allowInteract = false; // Explicitly set to false
    private boolean allowContainers = false;
    private boolean allowRedstone = false;
    private boolean allowDoors = false;
    private boolean allowCrops = false;
    private boolean allowAnimals = false;
    private boolean allowVillagers = false;

    /**
     * Creates a new RankPermissions instance with default permissions.
     * By default, only OWNER has all permissions, and all other ranks have no permissions.
     */
    public RankPermissions() {
        // Initialize permissions for each rank
        for (TownPlayerRank rank : TownPlayerRank.values()) {
            boolean[] permissions = new boolean[8];

            // Only OWNER has all permissions by default
            if (rank == TownPlayerRank.OWNER) {
                for (int i = 0; i < permissions.length; i++) {
                    permissions[i] = true;
                }
            }

            rankPermissions.put(rank, permissions);
        }
    }

    /**
     * Gets whether a specific rank has a specific permission.
     *
     * @param rank The rank to check
     * @param permissionIndex The index of the permission to check
     * @return Whether the rank has the permission
     */
    public boolean hasPermission(TownPlayerRank rank, int permissionIndex) {
        if (rank == null) {
            // Non-member permissions
            switch (permissionIndex) {
                case 0: return allowBuild;
                case 1: return allowInteract;
                case 2: return allowContainers;
                case 3: return allowRedstone;
                case 4: return allowDoors;
                case 5: return allowCrops;
                case 6: return allowAnimals;
                case 7: return allowVillagers;
                default: return false;
            }
        } else {
            // Town member permissions
            boolean[] permissions = rankPermissions.get(rank);
            if (permissions != null && permissionIndex >= 0 && permissionIndex < permissions.length) {
                return permissions[permissionIndex];
            }
            return false;
        }
    }

    /**
     * Sets whether a specific rank has a specific permission.
     *
     * @param rank The rank to set the permission for
     * @param permissionIndex The index of the permission to set
     * @param value Whether the rank should have the permission
     */
    public void setPermission(TownPlayerRank rank, int permissionIndex, boolean value) {
        if (rank == null) {
            // Non-member permissions
            switch (permissionIndex) {
                case 0: allowBuild = value; break;
                case 1: allowInteract = value; break;
                case 2: allowContainers = value; break;
                case 3: allowRedstone = value; break;
                case 4: allowDoors = value; break;
                case 5: allowCrops = value; break;
                case 6: allowAnimals = value; break;
                case 7: allowVillagers = value; break;
            }
        } else {
            // Town member permissions
            boolean[] permissions = rankPermissions.get(rank);
            if (permissions != null && permissionIndex >= 0 && permissionIndex < permissions.length) {
                permissions[permissionIndex] = value;
            }
        }
    }

    /**
     * Gets all permissions for a specific rank.
     *
     * @param rank The rank to get permissions for
     * @return An array of boolean values representing the permissions
     */
    public boolean[] getPermissions(TownPlayerRank rank) {
        if (rank == null) {
            // Non-member permissions
            boolean[] permissions = new boolean[8];
            permissions[0] = allowBuild;
            permissions[1] = allowInteract;
            permissions[2] = allowContainers;
            permissions[3] = allowRedstone;
            permissions[4] = allowDoors;
            permissions[5] = allowCrops;
            permissions[6] = allowAnimals;
            permissions[7] = allowVillagers;
            return permissions;
        } else {
            // Town member permissions
            boolean[] permissions = rankPermissions.get(rank);
            if (permissions != null) {
                // Return a copy to prevent modification
                boolean[] copy = new boolean[permissions.length];
                System.arraycopy(permissions, 0, copy, 0, permissions.length);
                return copy;
            }
            return new boolean[8]; // Default to all false
        }
    }

    /**
     * Updates the RankPermissions from a ClaimTag.
     * This is used to convert from the old permission system to the new one.
     *
     * @param tag The ClaimTag to get permissions from
     */
    public void updateFromClaimTag(ClaimTag tag) {
        // Non-member permissions
        allowBuild = tag.allowsBuild();
        allowInteract = tag.allowsInteract();
        allowContainers = tag.allowsContainers();
        allowRedstone = tag.allowsRedstone();
        allowDoors = tag.allowsDoors();
        allowCrops = tag.allowsCrops();
        allowAnimals = tag.allowsAnimals();
        allowVillagers = tag.allowsVillagers();

        // Town member permissions
        for (TownPlayerRank rank : TownPlayerRank.values()) {
            boolean[] permissions = rankPermissions.get(rank);
            if (permissions != null) {
                permissions[0] = rank == tag.getMinRankBuild() || rank == TownPlayerRank.OWNER;
                permissions[1] = rank == tag.getMinRankInteract() || rank == TownPlayerRank.OWNER;
                permissions[2] = rank == tag.getMinRankContainers() || rank == TownPlayerRank.OWNER;
                permissions[3] = rank == tag.getMinRankRedstone() || rank == TownPlayerRank.OWNER;
                permissions[4] = rank == tag.getMinRankDoors() || rank == TownPlayerRank.OWNER;
                permissions[5] = rank == tag.getMinRankCrops() || rank == TownPlayerRank.OWNER;
                permissions[6] = rank == tag.getMinRankAnimals() || rank == TownPlayerRank.OWNER;
                permissions[7] = rank == tag.getMinRankVillagers() || rank == TownPlayerRank.OWNER;
            }
        }
    }

    /**
     * Updates a ClaimTag with the permissions from this RankPermissions.
     * This is used to convert from the new permission system to the old one.
     *
     * @param tag The ClaimTag to update
     */
    public void updateClaimTag(ClaimTag tag) {
        // Non-member permissions
        tag.setAllowBuild(allowBuild);
        tag.setAllowInteract(allowInteract);
        tag.setAllowContainers(allowContainers);
        tag.setAllowRedstone(allowRedstone);
        tag.setAllowDoors(allowDoors);
        tag.setAllowCrops(allowCrops);
        tag.setAllowAnimals(allowAnimals);
        tag.setAllowVillagers(allowVillagers);

        // Town member permissions
        // For each permission, find the lowest rank that has it
        updateMinRankForPermission(tag, 0); // Build
        updateMinRankForPermission(tag, 1); // Interact
        updateMinRankForPermission(tag, 2); // Containers
        updateMinRankForPermission(tag, 3); // Redstone
        updateMinRankForPermission(tag, 4); // Doors
        updateMinRankForPermission(tag, 5); // Crops
        updateMinRankForPermission(tag, 6); // Animals
        updateMinRankForPermission(tag, 7); // Villagers
    }

    /**
     * Updates the minimum rank for a specific permission in a ClaimTag.
     *
     * @param tag The ClaimTag to update
     * @param permissionIndex The index of the permission to update
     */
    private void updateMinRankForPermission(ClaimTag tag, int permissionIndex) {
        // Find the lowest rank that has this permission
        TownPlayerRank minRank = null;

        // Check ranks in order from lowest to highest
        TownPlayerRank[] ranks = TownPlayerRank.values();
        for (int i = ranks.length - 1; i >= 0; i--) {
            TownPlayerRank rank = ranks[i];
            boolean[] permissions = rankPermissions.get(rank);
            if (permissions != null && permissions[permissionIndex]) {
                minRank = rank;
            }
        }

        // Update the tag
        switch (permissionIndex) {
            case 0: tag.setMinRankBuild(minRank); break;
            case 1: tag.setMinRankInteract(minRank); break;
            case 2: tag.setMinRankContainers(minRank); break;
            case 3: tag.setMinRankRedstone(minRank); break;
            case 4: tag.setMinRankDoors(minRank); break;
            case 5: tag.setMinRankCrops(minRank); break;
            case 6: tag.setMinRankAnimals(minRank); break;
            case 7: tag.setMinRankVillagers(minRank); break;
        }
    }
}
