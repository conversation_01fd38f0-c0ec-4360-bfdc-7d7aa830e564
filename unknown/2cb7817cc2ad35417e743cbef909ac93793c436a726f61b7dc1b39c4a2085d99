package com.pokecobble.town.network;

import net.minecraft.util.math.ChunkPos;

/**
 * Packet sent to the server to select or deselect a chunk.
 */
public class SelectChunkPacket {
    private final ChunkPos chunkPos;
    private final boolean selected;

    /**
     * Creates a new select chunk packet.
     *
     * @param chunkPos The chunk position
     * @param selected Whether the chunk is selected
     */
    public SelectChunkPacket(ChunkPos chunkPos, boolean selected) {
        this.chunkPos = chunkPos;
        this.selected = selected;
    }

    /**
     * Gets the chunk position.
     *
     * @return The chunk position
     */
    public ChunkPos getChunkPos() {
        return chunkPos;
    }

    /**
     * Checks if the chunk is selected.
     *
     * @return true if the chunk is selected, false otherwise
     */
    public boolean isSelected() {
        return selected;
    }
}
