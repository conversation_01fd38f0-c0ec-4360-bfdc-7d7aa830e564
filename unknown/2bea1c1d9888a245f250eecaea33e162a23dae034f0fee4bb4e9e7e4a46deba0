package com.pokecobble.town.claim;

import com.pokecobble.town.TownPlayerRank;

/**
 * Represents a preset for claim tags with predefined permission settings.
 * This allows for quick creation of common tag types.
 */
public class TagPreset {
    private final String name;
    private final int color;
    private final String description;
    
    // Permission settings
    private final TownPlayerRank minRankBuild;
    private final TownPlayerRank minRankInteract;
    private final TownPlayerRank minRankContainers;
    private final TownPlayerRank minRankRedstone;
    private final TownPlayerRank minRankDoors;
    private final TownPlayerRank minRankCrops;
    private final TownPlayerRank minRankAnimals;
    private final TownPlayerRank minRankVillagers;
    
    /**
     * Creates a new tag preset with the specified settings.
     *
     * @param name The name of the tag
     * @param color The color of the tag
     * @param description The description of the tag
     * @param minRankBuild The minimum rank required to build
     * @param minRankInteract The minimum rank required to interact
     * @param minRankContainers The minimum rank required to access containers
     * @param minRankRedstone The minimum rank required to use redstone
     * @param minRankDoors The minimum rank required to use doors
     * @param minRankCrops The minimum rank required to harvest crops
     * @param minRankAnimals The minimum rank required to interact with animals
     * @param minRankVillagers The minimum rank required to interact with villagers
     */
    public TagPreset(String name, int color, String description,
                    TownPlayerRank minRankBuild, TownPlayerRank minRankInteract,
                    TownPlayerRank minRankContainers, TownPlayerRank minRankRedstone,
                    TownPlayerRank minRankDoors, TownPlayerRank minRankCrops,
                    TownPlayerRank minRankAnimals, TownPlayerRank minRankVillagers) {
        this.name = name;
        this.color = color;
        this.description = description;
        this.minRankBuild = minRankBuild;
        this.minRankInteract = minRankInteract;
        this.minRankContainers = minRankContainers;
        this.minRankRedstone = minRankRedstone;
        this.minRankDoors = minRankDoors;
        this.minRankCrops = minRankCrops;
        this.minRankAnimals = minRankAnimals;
        this.minRankVillagers = minRankVillagers;
    }
    
    /**
     * Gets the name of the tag preset.
     *
     * @return The tag preset name
     */
    public String getName() {
        return name;
    }
    
    /**
     * Gets the color of the tag preset.
     *
     * @return The tag preset color
     */
    public int getColor() {
        return color;
    }
    
    /**
     * Gets the description of the tag preset.
     *
     * @return The tag preset description
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * Gets the minimum rank required to build.
     *
     * @return The minimum rank for building
     */
    public TownPlayerRank getMinRankBuild() {
        return minRankBuild;
    }
    
    /**
     * Gets the minimum rank required to interact.
     *
     * @return The minimum rank for interaction
     */
    public TownPlayerRank getMinRankInteract() {
        return minRankInteract;
    }
    
    /**
     * Gets the minimum rank required to access containers.
     *
     * @return The minimum rank for container access
     */
    public TownPlayerRank getMinRankContainers() {
        return minRankContainers;
    }
    
    /**
     * Gets the minimum rank required to use redstone.
     *
     * @return The minimum rank for redstone usage
     */
    public TownPlayerRank getMinRankRedstone() {
        return minRankRedstone;
    }
    
    /**
     * Gets the minimum rank required to use doors.
     *
     * @return The minimum rank for door usage
     */
    public TownPlayerRank getMinRankDoors() {
        return minRankDoors;
    }
    
    /**
     * Gets the minimum rank required to harvest crops.
     *
     * @return The minimum rank for crop harvesting
     */
    public TownPlayerRank getMinRankCrops() {
        return minRankCrops;
    }
    
    /**
     * Gets the minimum rank required to interact with animals.
     *
     * @return The minimum rank for animal interaction
     */
    public TownPlayerRank getMinRankAnimals() {
        return minRankAnimals;
    }
    
    /**
     * Gets the minimum rank required to interact with villagers.
     *
     * @return The minimum rank for villager interaction
     */
    public TownPlayerRank getMinRankVillagers() {
        return minRankVillagers;
    }
}
