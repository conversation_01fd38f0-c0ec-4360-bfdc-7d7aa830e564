package com.pokecobble.config;

import com.pokecobble.Pokecobbleclaim;
import net.fabricmc.loader.api.FabricLoader;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.Properties;

/**
 * Configuration for performance-related settings.
 * This class provides methods for loading and saving performance configuration.
 */
public class PerformanceConfig {
    private static final String CONFIG_FILE = "pokecobbleclaim-performance.properties";
    
    // Default values
    private static boolean enablePerformanceMonitoring = false;
    private static int chunkRenderDistance = 2;
    private static boolean enableChunkWalls = true;
    private static int chunkTrackerUpdateInterval = 250;
    private static boolean enablePacketMonitoring = false;
    
    /**
     * Loads the performance configuration from the config file.
     */
    public static void load() {
        File configFile = new File(FabricLoader.getInstance().getConfigDir().toFile(), CONFIG_FILE);
        
        if (!configFile.exists()) {
            save(); // Create default config
            return;
        }
        
        try (FileReader reader = new FileReader(configFile)) {
            Properties props = new Properties();
            props.load(reader);
            
            enablePerformanceMonitoring = Boolean.parseBoolean(props.getProperty("enablePerformanceMonitoring", "false"));
            chunkRenderDistance = Integer.parseInt(props.getProperty("chunkRenderDistance", "2"));
            enableChunkWalls = Boolean.parseBoolean(props.getProperty("enableChunkWalls", "true"));
            chunkTrackerUpdateInterval = Integer.parseInt(props.getProperty("chunkTrackerUpdateInterval", "250"));
            enablePacketMonitoring = Boolean.parseBoolean(props.getProperty("enablePacketMonitoring", "false"));
            
            Pokecobbleclaim.LOGGER.info("Loaded performance configuration");
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Failed to load performance configuration", e);
        }
    }
    
    /**
     * Saves the performance configuration to the config file.
     */
    public static void save() {
        File configFile = new File(FabricLoader.getInstance().getConfigDir().toFile(), CONFIG_FILE);
        
        try (FileWriter writer = new FileWriter(configFile)) {
            Properties props = new Properties();
            
            props.setProperty("enablePerformanceMonitoring", String.valueOf(enablePerformanceMonitoring));
            props.setProperty("chunkRenderDistance", String.valueOf(chunkRenderDistance));
            props.setProperty("enableChunkWalls", String.valueOf(enableChunkWalls));
            props.setProperty("chunkTrackerUpdateInterval", String.valueOf(chunkTrackerUpdateInterval));
            props.setProperty("enablePacketMonitoring", String.valueOf(enablePacketMonitoring));
            
            props.store(writer, "PokeCobbleClaim Performance Configuration");
            Pokecobbleclaim.LOGGER.info("Saved performance configuration");
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Failed to save performance configuration", e);
        }
    }
    
    /**
     * Gets whether performance monitoring is enabled.
     * 
     * @return Whether performance monitoring is enabled
     */
    public static boolean isPerformanceMonitoringEnabled() {
        return enablePerformanceMonitoring;
    }
    
    /**
     * Sets whether performance monitoring is enabled.
     * 
     * @param enabled Whether performance monitoring should be enabled
     */
    public static void setPerformanceMonitoringEnabled(boolean enabled) {
        enablePerformanceMonitoring = enabled;
        save();
    }
    
    /**
     * Gets the chunk render distance.
     * 
     * @return The chunk render distance
     */
    public static int getChunkRenderDistance() {
        return chunkRenderDistance;
    }
    
    /**
     * Sets the chunk render distance.
     * 
     * @param distance The chunk render distance
     */
    public static void setChunkRenderDistance(int distance) {
        chunkRenderDistance = Math.max(1, Math.min(5, distance)); // Clamp between 1 and 5
        save();
    }
    
    /**
     * Gets whether chunk walls are enabled.
     * 
     * @return Whether chunk walls are enabled
     */
    public static boolean areChunkWallsEnabled() {
        return enableChunkWalls;
    }
    
    /**
     * Sets whether chunk walls are enabled.
     * 
     * @param enabled Whether chunk walls should be enabled
     */
    public static void setChunkWallsEnabled(boolean enabled) {
        enableChunkWalls = enabled;
        save();
    }
    
    /**
     * Gets the chunk tracker update interval in milliseconds.
     * 
     * @return The chunk tracker update interval
     */
    public static int getChunkTrackerUpdateInterval() {
        return chunkTrackerUpdateInterval;
    }
    
    /**
     * Sets the chunk tracker update interval in milliseconds.
     * 
     * @param interval The chunk tracker update interval
     */
    public static void setChunkTrackerUpdateInterval(int interval) {
        chunkTrackerUpdateInterval = Math.max(50, Math.min(1000, interval)); // Clamp between 50ms and 1000ms
        save();
    }
    
    /**
     * Gets whether packet monitoring is enabled.
     * 
     * @return Whether packet monitoring is enabled
     */
    public static boolean isPacketMonitoringEnabled() {
        return enablePacketMonitoring;
    }
    
    /**
     * Sets whether packet monitoring is enabled.
     * 
     * @param enabled Whether packet monitoring should be enabled
     */
    public static void setPacketMonitoringEnabled(boolean enabled) {
        enablePacketMonitoring = enabled;
        save();
    }
}
