package com.pokecobble.town.client;

import com.pokecobble.town.claim.ClaimTool;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.minecraft.client.MinecraftClient;
import net.minecraft.text.Text;

/**
 * A test class for chunk rendering.
 * This class provides methods to test the chunk rendering functionality.
 */
public class ChunkRenderTest {
    private static final MinecraftClient client = MinecraftClient.getInstance();
    
    /**
     * Registers the test.
     */
    public static void register() {
        // Register a tick event to handle key presses for testing
        ClientTickEvents.END_CLIENT_TICK.register(client -> {
            // This is just for testing - in a real implementation, you would use proper keybindings
            if (client.player != null && client.options.debugEnabled) {
                // Log the current state of the claim tool
                boolean isActive = ClaimTool.getInstance().isActive();
                boolean isVisible = ClaimTool.getInstance().areChunksVisible();
                
                if (client.player.age % 100 == 0) { // Log every 5 seconds (100 ticks)
                    client.player.sendMessage(Text.literal("Claim tool active: " + isActive + ", chunks visible: " + isVisible), true);
                }
            }
        });
    }
    
    /**
     * Tests the chunk rendering.
     * This method can be called from the console or a command to test the chunk rendering.
     */
    public static void testChunkRendering() {
        if (client.player == null) return;
        
        // Get the claim tool
        ClaimTool claimTool = ClaimTool.getInstance();
        
        // Toggle chunk visibility
        claimTool.toggleChunkVisibility();
        
        // Log the current state
        boolean isVisible = claimTool.areChunksVisible();
        client.player.sendMessage(Text.literal("Chunk visibility toggled. Now " + (isVisible ? "visible" : "hidden")), false);
    }
}
