package com.pokecobble.debug.network;

import com.pokecobble.Pokecobbleclaim;
import net.minecraft.network.packet.Packet;

import javax.swing.*;
import javax.swing.text.DefaultCaret;
import java.awt.*;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * External window for monitoring network packets sent and received by the mod.
 * This is a debugging tool that runs in a separate window from Minecraft.
 */
public class PacketMonitor {
    private static JFrame frame;
    private static JTextArea logArea;
    private static boolean isInitialized = false;
    private static final AtomicBoolean isRunning = new AtomicBoolean(false);
    
    // Queue for packet logs to avoid blocking the game thread
    private static final ConcurrentLinkedQueue<String> logQueue = new ConcurrentLinkedQueue<>();
    
    // Thread for processing log queue
    private static Thread logProcessorThread;
    
    // Filters
    private static JTextField filterField;
    private static boolean filterEnabled = false;
    private static String currentFilter = "";
    
    // Statistics
    private static int sentPackets = 0;
    private static int receivedPackets = 0;
    private static JLabel statsLabel;
    
    /**
     * Initializes the packet monitor window.
     */
    public static void initialize() {
        if (isInitialized) {
            // If already initialized, just make visible again
            if (frame != null && !frame.isVisible()) {
                frame.setVisible(true);
            }
            return;
        }
        
        // Create the frame on a separate thread to avoid blocking Minecraft
        SwingUtilities.invokeLater(() -> {
            try {
                // Set system look and feel
                UIManager.setLookAndFeel(UIManager.getSystemLookAndFeelClassName());
                
                // Create the main frame
                frame = new JFrame("PokeCobbleClaim Packet Monitor");
                frame.setSize(900, 600);
                frame.setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);
                
                // Create components
                logArea = new JTextArea();
                logArea.setEditable(false);
                logArea.setFont(new Font(Font.MONOSPACED, Font.PLAIN, 12));
                
                // Auto-scroll to bottom
                DefaultCaret caret = (DefaultCaret) logArea.getCaret();
                caret.setUpdatePolicy(DefaultCaret.ALWAYS_UPDATE);
                
                JScrollPane scrollPane = new JScrollPane(logArea);
                
                // Create toolbar
                JToolBar toolBar = new JToolBar();
                toolBar.setFloatable(false);
                
                // Clear button
                JButton clearButton = new JButton("Clear");
                clearButton.addActionListener(e -> {
                    logArea.setText("");
                    sentPackets = 0;
                    receivedPackets = 0;
                    updateStats();
                });
                
                // Filter components
                JLabel filterLabel = new JLabel("Filter: ");
                filterField = new JTextField(20);
                JCheckBox enableFilterCheckbox = new JCheckBox("Enable");
                enableFilterCheckbox.addActionListener(e -> {
                    filterEnabled = enableFilterCheckbox.isSelected();
                    currentFilter = filterField.getText().toLowerCase();
                });
                
                filterField.addActionListener(e -> {
                    if (filterEnabled) {
                        currentFilter = filterField.getText().toLowerCase();
                    }
                });
                
                // Stats label
                statsLabel = new JLabel("Sent: 0 | Received: 0");
                
                // Add components to toolbar
                toolBar.add(clearButton);
                toolBar.addSeparator();
                toolBar.add(filterLabel);
                toolBar.add(filterField);
                toolBar.add(enableFilterCheckbox);
                toolBar.addSeparator();
                toolBar.add(statsLabel);
                
                // Add components to frame
                frame.getContentPane().add(toolBar, BorderLayout.NORTH);
                frame.getContentPane().add(scrollPane, BorderLayout.CENTER);
                
                // Add window listener to handle closing
                frame.addWindowListener(new WindowAdapter() {
                    @Override
                    public void windowClosing(WindowEvent e) {
                        // Just hide the window, don't terminate the monitor
                        frame.setVisible(false);
                    }
                });
                
                // Position the window on the right side of the screen
                Dimension screenSize = Toolkit.getDefaultToolkit().getScreenSize();
                frame.setLocation(screenSize.width - frame.getWidth(), 0);
                
                // Show the frame
                frame.setVisible(true);
                
                // Start the log processor thread
                startLogProcessor();
                
                isInitialized = true;
                isRunning.set(true);
                
                // Add initial message
                logQueue.add("[INFO] Packet monitor initialized. Press F7 to toggle visibility.");
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Failed to initialize packet monitor", e);
            }
        });
    }
    
    /**
     * Starts the log processor thread.
     */
    private static void startLogProcessor() {
        if (logProcessorThread != null && logProcessorThread.isAlive()) {
            return;
        }
        
        logProcessorThread = new Thread(() -> {
            while (isRunning.get()) {
                try {
                    // Process all queued logs
                    while (!logQueue.isEmpty()) {
                        String log = logQueue.poll();
                        if (log != null) {
                            // Apply filter if enabled
                            if (!filterEnabled || currentFilter.isEmpty() || 
                                log.toLowerCase().contains(currentFilter)) {
                                SwingUtilities.invokeLater(() -> {
                                    logArea.append(log + "\n");
                                });
                            }
                        }
                    }
                    
                    // Sleep to avoid high CPU usage
                    Thread.sleep(100);
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.error("Error in packet monitor log processor", e);
                }
            }
        });
        
        logProcessorThread.setName("PacketMonitor-LogProcessor");
        logProcessorThread.setDaemon(true);
        logProcessorThread.start();
    }
    
    /**
     * Updates the statistics display.
     */
    private static void updateStats() {
        if (statsLabel != null) {
            SwingUtilities.invokeLater(() -> {
                statsLabel.setText("Sent: " + sentPackets + " | Received: " + receivedPackets);
            });
        }
    }
    
    /**
     * Logs a sent packet.
     * 
     * @param packet The packet being sent
     */
    public static void logSentPacket(Packet<?> packet) {
        if (!isInitialized || !isRunning.get()) return;
        
        sentPackets++;
        updateStats();
        
        String timestamp = new SimpleDateFormat("HH:mm:ss.SSS").format(new Date());
        String packetName = packet.getClass().getSimpleName();
        String log = String.format("[%s] SENT: %s - %s", timestamp, packetName, packet.toString());
        
        logQueue.add(log);
    }
    
    /**
     * Logs a received packet.
     * 
     * @param packet The packet being received
     */
    public static void logReceivedPacket(Packet<?> packet) {
        if (!isInitialized || !isRunning.get()) return;
        
        receivedPackets++;
        updateStats();
        
        String timestamp = new SimpleDateFormat("HH:mm:ss.SSS").format(new Date());
        String packetName = packet.getClass().getSimpleName();
        String log = String.format("[%s] RECV: %s - %s", timestamp, packetName, packet.toString());
        
        logQueue.add(log);
    }
    
    /**
     * Toggles the visibility of the packet monitor window.
     */
    public static void toggleVisibility() {
        if (!isInitialized) {
            initialize();
            return;
        }
        
        if (frame != null) {
            SwingUtilities.invokeLater(() -> {
                frame.setVisible(!frame.isVisible());
            });
        }
    }
    
    /**
     * Shuts down the packet monitor.
     */
    public static void shutdown() {
        isRunning.set(false);
        
        if (frame != null) {
            SwingUtilities.invokeLater(() -> {
                frame.dispose();
            });
            frame = null;
        }
        
        isInitialized = false;
    }
}
