package com.pokecobble.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.BoolArgumentType;
import com.mojang.brigadier.arguments.IntegerArgumentType;
import com.pokecobble.config.PerformanceConfig;
import com.pokecobble.util.PerformanceMonitor;
import net.fabricmc.fabric.api.client.command.v2.ClientCommandManager;
import net.fabricmc.fabric.api.client.command.v2.ClientCommandRegistrationCallback;
import net.fabricmc.fabric.api.client.command.v2.FabricClientCommandSource;
import net.minecraft.text.Text;

/**
 * Command for managing performance settings.
 */
public class PerformanceCommand {
    
    /**
     * Registers the performance command.
     */
    public static void register() {
        ClientCommandRegistrationCallback.EVENT.register((dispatcher, registryAccess) -> registerClientCommands(dispatcher));
    }
    
    /**
     * Registers client-side commands.
     * 
     * @param dispatcher The command dispatcher
     */
    private static void registerClientCommands(CommandDispatcher<FabricClientCommandSource> dispatcher) {
        dispatcher.register(
            ClientCommandManager.literal("performance")
                .then(ClientCommandManager.literal("monitoring")
                    .then(ClientCommandManager.argument("enabled", BoolArgumentType.bool())
                        .executes(context -> {
                            boolean enabled = BoolArgumentType.getBool(context, "enabled");
                            PerformanceConfig.setPerformanceMonitoringEnabled(enabled);
                            PerformanceMonitor.setEnabled(enabled);
                            context.getSource().sendFeedback(Text.literal("Performance monitoring " + (enabled ? "enabled" : "disabled")));
                            return 1;
                        })
                    )
                )
                .then(ClientCommandManager.literal("chunkrender")
                    .then(ClientCommandManager.argument("distance", IntegerArgumentType.integer(1, 5))
                        .executes(context -> {
                            int distance = IntegerArgumentType.getInteger(context, "distance");
                            PerformanceConfig.setChunkRenderDistance(distance);
                            context.getSource().sendFeedback(Text.literal("Chunk render distance set to " + distance));
                            return 1;
                        })
                    )
                )
                .then(ClientCommandManager.literal("chunkwalls")
                    .then(ClientCommandManager.argument("enabled", BoolArgumentType.bool())
                        .executes(context -> {
                            boolean enabled = BoolArgumentType.getBool(context, "enabled");
                            PerformanceConfig.setChunkWallsEnabled(enabled);
                            context.getSource().sendFeedback(Text.literal("Chunk walls " + (enabled ? "enabled" : "disabled")));
                            return 1;
                        })
                    )
                )
                .then(ClientCommandManager.literal("chunktracker")
                    .then(ClientCommandManager.argument("interval", IntegerArgumentType.integer(50, 1000))
                        .executes(context -> {
                            int interval = IntegerArgumentType.getInteger(context, "interval");
                            PerformanceConfig.setChunkTrackerUpdateInterval(interval);
                            context.getSource().sendFeedback(Text.literal("Chunk tracker update interval set to " + interval + "ms"));
                            return 1;
                        })
                    )
                )
                .then(ClientCommandManager.literal("packetmonitor")
                    .then(ClientCommandManager.argument("enabled", BoolArgumentType.bool())
                        .executes(context -> {
                            boolean enabled = BoolArgumentType.getBool(context, "enabled");
                            PerformanceConfig.setPacketMonitoringEnabled(enabled);
                            context.getSource().sendFeedback(Text.literal("Packet monitoring " + (enabled ? "enabled" : "disabled") + " (restart required)"));
                            return 1;
                        })
                    )
                )
                .then(ClientCommandManager.literal("status")
                    .executes(context -> {
                        context.getSource().sendFeedback(Text.literal("Performance Settings:"));
                        context.getSource().sendFeedback(Text.literal("- Monitoring: " + (PerformanceConfig.isPerformanceMonitoringEnabled() ? "enabled" : "disabled")));
                        context.getSource().sendFeedback(Text.literal("- Chunk Render Distance: " + PerformanceConfig.getChunkRenderDistance()));
                        context.getSource().sendFeedback(Text.literal("- Chunk Walls: " + (PerformanceConfig.areChunkWallsEnabled() ? "enabled" : "disabled")));
                        context.getSource().sendFeedback(Text.literal("- Chunk Tracker Interval: " + PerformanceConfig.getChunkTrackerUpdateInterval() + "ms"));
                        context.getSource().sendFeedback(Text.literal("- Packet Monitoring: " + (PerformanceConfig.isPacketMonitoringEnabled() ? "enabled" : "disabled")));
                        return 1;
                    })
                )
        );
    }
}
