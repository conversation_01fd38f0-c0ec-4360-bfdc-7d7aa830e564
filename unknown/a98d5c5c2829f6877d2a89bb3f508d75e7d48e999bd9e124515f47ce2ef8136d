package com.pokecobble.town.gui;

/**
 * Represents a subcategory in the town management interface.
 */
public class TownSubcategory {
    private final String name;
    private final String icon;
    private final int color;
    private int x;
    private int y;
    
    /**
     * Creates a new town subcategory.
     *
     * @param name The name of the subcategory
     * @param icon The icon to display next to the name
     * @param color The color to use for the subcategory
     */
    public TownSubcategory(String name, String icon, int color) {
        this.name = name;
        this.icon = icon;
        this.color = color;
    }
    
    /**
     * Gets the name of the subcategory.
     *
     * @return The subcategory name
     */
    public String getName() {
        return name;
    }
    
    /**
     * Gets the icon of the subcategory.
     *
     * @return The subcategory icon
     */
    public String getIcon() {
        return icon;
    }
    
    /**
     * Gets the color of the subcategory.
     *
     * @return The subcategory color
     */
    public int getColor() {
        return color;
    }
    
    /**
     * Gets the X position of the subcategory button.
     *
     * @return The X position
     */
    public int getX() {
        return x;
    }
    
    /**
     * Gets the Y position of the subcategory button.
     *
     * @return The Y position
     */
    public int getY() {
        return y;
    }
    
    /**
     * Sets the position of the subcategory button.
     *
     * @param x The X position
     * @param y The Y position
     */
    public void setPosition(int x, int y) {
        this.x = x;
        this.y = y;
    }
}
