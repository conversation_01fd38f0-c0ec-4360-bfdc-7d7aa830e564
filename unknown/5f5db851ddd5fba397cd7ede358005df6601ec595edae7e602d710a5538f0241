package com.pokecobble.debug.network;

import com.pokecobble.Pokecobbleclaim;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import org.lwjgl.glfw.GLFW;

/**
 * Registers and handles keybindings for the packet monitor.
 */
public class PacketMonitorKeybind {
    private static KeyBinding toggleMonitorKey;
    
    /**
     * Registers the packet monitor keybinding.
     */
    public static void register() {
        Pokecobbleclaim.LOGGER.info("Registering packet monitor keybinding");
        
        // Register the keybinding (F7 key by default)
        toggleMonitorKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
                "key.pokecobbleclaim.toggle_packet_monitor",
                InputUtil.Type.KEYSYM,
                GLFW.GLFW_KEY_F7,
                "category.pokecobbleclaim.debug"
        ));
        
        // Register the tick event to check for key presses
        ClientTickEvents.END_CLIENT_TICK.register(client -> {
            if (toggleMonitorKey.wasPressed()) {
                Pokecobbleclaim.LOGGER.info("Toggling packet monitor visibility");
                PacketMonitor.toggleVisibility();
            }
        });
    }
}
