package com.pokecobble.mixin;

import com.pokecobble.town.claim.ClaimTool;
import net.minecraft.client.gui.hud.InGameHud;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

/**
 * Mixin to hide the vanilla HUD when the claim tool is active.
 */
@Mixin(InGameHud.class)
public class InGameHudMixin {

    /**
     * Cancels rendering of the hotbar when the claim tool is active.
     */
    @Inject(method = "renderHotbar", at = @At("HEAD"), cancellable = true)
    private void onRenderHotbar(float tickDelta, net.minecraft.client.gui.DrawContext context, CallbackInfo ci) {
        if (ClaimTool.getInstance().isActive()) {
            ci.cancel();
        }
    }

    /**
     * Cancels rendering of the crosshair when the claim tool is active.
     */
    @Inject(method = "renderCrosshair", at = @At("HEAD"), cancellable = true)
    private void onRenderCrosshair(net.minecraft.client.gui.DrawContext context, CallbackInfo ci) {
        if (ClaimTool.getInstance().isActive()) {
            ci.cancel();
        }
    }

    /**
     * Cancels rendering of the status bars (health, hunger, etc.) when the claim tool is active.
     */
    @Inject(method = "renderStatusBars", at = @At("HEAD"), cancellable = true)
    private void onRenderStatusBars(net.minecraft.client.gui.DrawContext context, CallbackInfo ci) {
        if (ClaimTool.getInstance().isActive()) {
            ci.cancel();
        }
    }

    /**
     * Cancels rendering of the experience bar when the claim tool is active.
     */
    @Inject(method = "renderExperienceBar", at = @At("HEAD"), cancellable = true)
    private void onRenderExperienceBar(net.minecraft.client.gui.DrawContext context, int x, CallbackInfo ci) {
        if (ClaimTool.getInstance().isActive()) {
            ci.cancel();
        }
    }

    /**
     * Cancels rendering of the mount health when the claim tool is active.
     */
    @Inject(method = "renderMountHealth", at = @At("HEAD"), cancellable = true)
    private void onRenderMountHealth(net.minecraft.client.gui.DrawContext context, CallbackInfo ci) {
        if (ClaimTool.getInstance().isActive()) {
            ci.cancel();
        }
    }

    /**
     * Cancels rendering of the scoreboard when the claim tool is active.
     */
    @Inject(method = "renderScoreboardSidebar", at = @At("HEAD"), cancellable = true)
    private void onRenderScoreboardSidebar(net.minecraft.client.gui.DrawContext context, net.minecraft.scoreboard.ScoreboardObjective objective, CallbackInfo ci) {
        if (ClaimTool.getInstance().isActive()) {
            ci.cancel();
        }
    }
}
