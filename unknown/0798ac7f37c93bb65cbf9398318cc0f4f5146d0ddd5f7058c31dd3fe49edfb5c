package com.pokecobble.mixin;

import com.pokecobble.town.gui.ModernTownScreen;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.network.packet.s2c.play.CommandSuggestionsS2CPacket;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(ClientPlayNetworkHandler.class)
public class CommandMixin {

    @Inject(method = "onGameMessage", at = @At("HEAD"))
    private void onGameMessage(net.minecraft.network.packet.s2c.play.GameMessageS2CPacket packet, CallbackInfo ci) {
        String message = packet.content().getString();

        // Check if this is our command feedback message
        if (message.equals("Opening town interface...")) {
            MinecraftClient client = MinecraftClient.getInstance();
            client.execute(() -> client.setScreen(new ModernTownScreen(client.currentScreen)));
        }
    }
}
