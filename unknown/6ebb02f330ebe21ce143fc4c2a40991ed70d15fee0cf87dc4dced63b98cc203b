package com.pokecobble.town.claim;

import java.util.ArrayList;
import java.util.List;

/**
 * Manages the selection of claim tags for the claim tool.
 */
public class TagSelector {
    private static TagSelector instance;

    private final List<ClaimTag> availableTags = new ArrayList<>();
    private ClaimTag currentTag = null;
    private int currentTagIndex = 0;

    private TagSelector() {
        // Private constructor for singleton
    }

    /**
     * Gets the singleton instance of the TagSelector.
     */
    public static TagSelector getInstance() {
        if (instance == null) {
            instance = new TagSelector();
        }
        return instance;
    }

    /**
     * Sets the available tags for selection.
     */
    public void setAvailableTags(List<ClaimTag> tags) {
        availableTags.clear();
        if (tags != null && !tags.isEmpty()) {
            availableTags.addAll(tags);
            currentTagIndex = 0;
            currentTag = availableTags.get(0);
        } else {
            currentTag = null;
        }
    }

    /**
     * Gets the current selected tag.
     */
    public ClaimTag getCurrentTag() {
        return currentTag;
    }

    /**
     * Cycles to the next tag.
     */
    public void nextTag() {
        if (availableTags.isEmpty()) return;

        currentTagIndex = (currentTagIndex + 1) % availableTags.size();
        currentTag = availableTags.get(currentTagIndex);
    }

    /**
     * Cycles to the previous tag.
     */
    public void previousTag() {
        if (availableTags.isEmpty()) return;

        currentTagIndex = (currentTagIndex - 1 + availableTags.size()) % availableTags.size();
        currentTag = availableTags.get(currentTagIndex);
    }

    /**
     * Gets the list of available tags.
     */
    public List<ClaimTag> getAvailableTags() {
        return new ArrayList<>(availableTags);
    }

    /**
     * Creates sample tags for testing.
     */
    public void createSampleTags() {
        // Define colors for tags
        int[] TAG_COLORS = {
            0xFF5555FF, // Blue
            0xFF55FF55, // Green
            0xFFFF5555, // Red
            0xFFFFAA00  // Orange
        };

        // Create sample tags with simple numbered names
        List<ClaimTag> tags = new ArrayList<>();

        ClaimTag tag1 = new ClaimTag("Tag1", TAG_COLORS[0]);
        ClaimTag tag2 = new ClaimTag("Tag2", TAG_COLORS[1]);
        ClaimTag tag3 = new ClaimTag("Tag3", TAG_COLORS[2]);
        ClaimTag tag4 = new ClaimTag("Tag4", TAG_COLORS[3]);

        tags.add(tag1);
        tags.add(tag2);
        tags.add(tag3);
        tags.add(tag4);

        setAvailableTags(tags);
    }
}
