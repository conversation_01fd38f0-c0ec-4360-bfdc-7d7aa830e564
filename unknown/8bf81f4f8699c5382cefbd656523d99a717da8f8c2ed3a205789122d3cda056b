package com.pokecobble.mixin;

import com.pokecobble.debug.network.PacketMonitor;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.network.packet.Packet;
import net.minecraft.network.packet.s2c.play.GameMessageS2CPacket;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

/**
 * Mixin to intercept network packets received by the client.
 */
@Mixin(ClientPlayNetworkHandler.class)
public class ClientPlayNetworkHandlerMixin {

    /**
     * This is just one example of intercepting a specific packet type.
     * We'll add more methods to intercept other packet types.
     */
    @Inject(method = "onGameMessage", at = @At("HEAD"))
    private void onGameMessage(GameMessageS2CPacket packet, CallbackInfo ci) {
        // Log the packet in the monitor
        PacketMonitor.logReceivedPacket(packet);
    }

    // Note: We can't intercept all packets with a single method
    // because there's no generic 'handlePacket' method in ClientPlayNetworkHandler.
    // Instead, we need to add specific methods for each packet type we want to monitor.
}
