package com.pokecobble.util;

import com.pokecobble.Pokecobbleclaim;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Utility for monitoring performance of different parts of the mod.
 * This class provides methods for timing operations and logging performance data.
 */
public class PerformanceMonitor {
    private static final Map<String, Long> startTimes = new ConcurrentHashMap<>();
    private static final Map<String, Long> totalTimes = new ConcurrentHashMap<>();
    private static final Map<String, Integer> callCounts = new ConcurrentHashMap<>();
    
    private static boolean enabled = false;
    
    /**
     * Enables or disables performance monitoring.
     * 
     * @param enabled Whether performance monitoring should be enabled
     */
    public static void setEnabled(boolean enabled) {
        PerformanceMonitor.enabled = enabled;
    }
    
    /**
     * Starts timing an operation.
     * 
     * @param operation The name of the operation to time
     */
    public static void startTiming(String operation) {
        if (!enabled) return;
        
        startTimes.put(operation, System.nanoTime());
    }
    
    /**
     * Ends timing an operation and records the time taken.
     * 
     * @param operation The name of the operation to stop timing
     */
    public static void endTiming(String operation) {
        if (!enabled) return;
        
        Long startTime = startTimes.remove(operation);
        if (startTime == null) {
            Pokecobbleclaim.LOGGER.warn("Tried to end timing for operation that wasn't started: " + operation);
            return;
        }
        
        long endTime = System.nanoTime();
        long duration = endTime - startTime;
        
        totalTimes.put(operation, totalTimes.getOrDefault(operation, 0L) + duration);
        callCounts.put(operation, callCounts.getOrDefault(operation, 0) + 1);
    }
    
    /**
     * Logs performance data for all operations.
     */
    public static void logPerformanceData() {
        if (!enabled) return;
        
        Pokecobbleclaim.LOGGER.info("Performance data:");
        
        for (String operation : totalTimes.keySet()) {
            long totalTime = totalTimes.get(operation);
            int count = callCounts.getOrDefault(operation, 0);
            
            if (count > 0) {
                double averageTimeMs = (totalTime / (double) count) / 1_000_000.0;
                Pokecobbleclaim.LOGGER.info(String.format("  %s: %d calls, avg %.3f ms", operation, count, averageTimeMs));
            }
        }
    }
    
    /**
     * Resets all performance data.
     */
    public static void reset() {
        startTimes.clear();
        totalTimes.clear();
        callCounts.clear();
    }
}
