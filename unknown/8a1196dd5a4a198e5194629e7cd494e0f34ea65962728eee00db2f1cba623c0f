package com.pokecobble.town.gui;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.election.Election;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Screen for displaying detailed election results.
 */
public class ElectionResultsScreen extends Screen {
    private final Screen parent;
    private final Town town;
    private final Election election;
    private ButtonWidget closeButton;
    
    // Scrolling
    private int scrollOffset = 0;
    private static final int SCROLL_AMOUNT = 15;
    
    // Colors
    private static final int BACKGROUND_COLOR = 0xFF101010; // Solid dark background
    private static final int PANEL_COLOR = 0xFF202030; // Solid panel background
    private static final int BORDER_COLOR = 0xFF5555FF; // Blue border
    private static final int TEXT_COLOR = 0xFFFFFFFF; // White text
    private static final int WINNER_COLOR = 0xFF4CAF50; // Green for winner
    private static final int LOSER_COLOR = 0xFFE53935; // Red for losers
    private static final int NEUTRAL_COLOR = 0xFFFFAA00; // Orange for neutral info
    
    // Parsed results
    private final List<String> resultLines = new ArrayList<>();

    public ElectionResultsScreen(Screen parent, Town town, Election election) {
        super(Text.literal("Election Results"));
        this.parent = parent;
        this.town = town;
        this.election = election;
        
        // Parse the election results into lines
        if (election != null && election.isCompleted()) {
            String results = election.getElectionResults();
            if (results != null && !results.isEmpty()) {
                String[] lines = results.split("\\n");
                for (String line : lines) {
                    resultLines.add(line);
                }
            }
        }
    }

    @Override
    protected void init() {
        super.init();
        
        int panelWidth = 600;
        int panelHeight = 400;
        int panelX = (this.width - panelWidth) / 2;
        int panelY = (this.height - panelHeight) / 2;
        int buttonWidth = 100;
        int buttonHeight = 20;
        int buttonsY = panelY + panelHeight - buttonHeight - 15;
        
        // Add close button
        closeButton = ButtonWidget.builder(Text.literal("Close"), button -> {
            // Close the screen
            this.client.setScreen(parent);
        })
        .dimensions(panelX + panelWidth / 2 - buttonWidth / 2, buttonsY, buttonWidth, buttonHeight)
        .build();
        this.addDrawableChild(closeButton);
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw a solid dark background over the entire screen
        context.fill(0, 0, this.width, this.height, BACKGROUND_COLOR);
        
        // Draw a solid panel
        int panelWidth = 600;
        int panelHeight = 400;
        int panelX = (this.width - panelWidth) / 2;
        int panelY = (this.height - panelHeight) / 2;
        
        // Draw panel background
        context.fill(panelX, panelY, panelX + panelWidth, panelY + panelHeight, PANEL_COLOR);
        
        // Draw panel border
        context.drawBorder(panelX, panelY, panelWidth, panelHeight, BORDER_COLOR);
        
        // Draw title
        context.drawCenteredTextWithShadow(this.textRenderer, Text.literal("Election Results").formatted(Formatting.BOLD),
            panelX + panelWidth / 2, panelY + 15, TEXT_COLOR);
        
        // Calculate content area dimensions
        int contentX = panelX + 20;
        int contentY = panelY + 40;
        int contentWidth = panelWidth - 40;
        int contentHeight = panelHeight - 80;
        
        // Draw content area background
        context.fill(contentX, contentY, contentX + contentWidth, contentY + contentHeight, 0x20000000);
        
        // Draw scrollbar if needed
        int lineHeight = 14;
        int totalHeight = resultLines.size() * lineHeight;
        int maxScroll = Math.max(0, totalHeight - contentHeight);
        
        if (maxScroll > 0) {
            // Draw scrollbar track
            context.fill(contentX + contentWidth - 5, contentY, contentX + contentWidth - 2, contentY + contentHeight, 0x20FFFFFF);
            
            // Calculate scrollbar height and position
            int scrollbarHeight = Math.max(20, contentHeight * contentHeight / totalHeight);
            int scrollbarY = contentY + (contentHeight - scrollbarHeight) * scrollOffset / maxScroll;
            
            // Draw scrollbar handle
            context.fill(contentX + contentWidth - 5, scrollbarY, contentX + contentWidth - 2, scrollbarY + scrollbarHeight, 0x80FFFFFF);
        }
        
        // Clamp scroll offset
        scrollOffset = Math.max(0, Math.min(scrollOffset, maxScroll));
        
        // Draw result lines with scrolling
        int lineY = contentY - scrollOffset;
        for (String line : resultLines) {
            // Skip lines that are outside the visible area
            if (lineY + lineHeight < contentY || lineY > contentY + contentHeight) {
                lineY += lineHeight;
                continue;
            }
            
            // Determine line color based on content
            int lineColor = TEXT_COLOR;
            if (line.contains("Winner:") || line.contains("New mayor")) {
                lineColor = WINNER_COLOR;
            } else if (line.contains("Tie detected") || line.contains("random")) {
                lineColor = NEUTRAL_COLOR;
            } else if (line.contains("votes")) {
                // This is a vote count line, but not the winner
                lineColor = 0xFFAAAAAA;
            }
            
            // Draw the line
            context.drawTextWithShadow(this.textRenderer, line, contentX + 5, lineY, lineColor);
            lineY += lineHeight;
        }
        
        // Draw buttons
        super.render(context, mouseX, mouseY, delta);
    }
    
    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        // Calculate content area dimensions
        int panelWidth = 600;
        int panelHeight = 400;
        int panelX = (this.width - panelWidth) / 2;
        int panelY = (this.height - panelHeight) / 2;
        int contentX = panelX + 20;
        int contentY = panelY + 40;
        int contentWidth = panelWidth - 40;
        int contentHeight = panelHeight - 80;
        
        // Check if mouse is over content area
        if (mouseX >= contentX && mouseX <= contentX + contentWidth &&
            mouseY >= contentY && mouseY <= contentY + contentHeight) {
            
            // Calculate max scroll
            int lineHeight = 14;
            int totalHeight = resultLines.size() * lineHeight;
            int maxScroll = Math.max(0, totalHeight - contentHeight);
            
            // Update scroll offset
            scrollOffset -= (int)(amount * SCROLL_AMOUNT);
            scrollOffset = Math.max(0, Math.min(scrollOffset, maxScroll));
            
            return true;
        }
        
        return super.mouseScrolled(mouseX, mouseY, amount);
    }
    
    @Override
    public boolean shouldPause() {
        return false;
    }
}
