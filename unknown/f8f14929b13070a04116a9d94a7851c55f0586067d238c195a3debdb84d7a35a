package com.pokecobble.mixin;

import com.pokecobble.debug.network.PacketMonitor;
import net.minecraft.network.ClientConnection;
import net.minecraft.network.packet.Packet;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

/**
 * Mixin to intercept network packets sent by the client.
 */
@Mixin(ClientConnection.class)
public class ClientConnectionMixin {
    
    /**
     * Intercepts packets being sent from the client to the server.
     */
    @Inject(method = "send(Lnet/minecraft/network/packet/Packet;)V", at = @At("HEAD"))
    private void onSendPacket(Packet<?> packet, CallbackInfo ci) {
        // Log the packet in the monitor
        PacketMonitor.logSentPacket(packet);
    }
}
