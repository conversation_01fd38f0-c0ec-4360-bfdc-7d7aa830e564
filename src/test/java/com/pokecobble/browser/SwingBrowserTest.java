package com.pokecobble.browser;

import com.pokecobble.browser.core.SwingBrowser;

import javax.swing.*;
import java.awt.image.BufferedImage;
import java.io.File;
import javax.imageio.ImageIO;

/**
 * Test class to verify SwingBrowser HTML rendering functionality.
 */
public class SwingBrowserTest {
    
    public static void main(String[] args) {
        try {
            System.out.println("Testing SwingBrowser HTML rendering...");
            
            // Create a simple HTML content
            String testHtml = "<html><body><h1>Test HTML</h1><p>This is a test paragraph.</p><p><b>Bold text</b> and <i>italic text</i>.</p></body></html>";
            
            // Create SwingBrowser instance
            SwingBrowser browser = new SwingBrowser(800, 600);
            
            // Load HTML content
            boolean loaded = browser.loadHtmlContent(testHtml);
            System.out.println("HTML loaded: " + loaded);
            
            if (loaded) {
                // Wait a moment for rendering
                Thread.sleep(1000);
                
                // Render to image
                BufferedImage image = browser.renderToImage();
                
                if (image != null) {
                    System.out.println("Image rendered: " + image.getWidth() + "x" + image.getHeight());
                    
                    // Save image to file
                    File outputFile = new File("test_output.png");
                    ImageIO.write(image, "PNG", outputFile);
                    System.out.println("Image saved to: " + outputFile.getAbsolutePath());
                    
                    // Check if image has content (not all white)
                    boolean hasContent = false;
                    for (int y = 0; y < image.getHeight() && !hasContent; y += 10) {
                        for (int x = 0; x < image.getWidth() && !hasContent; x += 10) {
                            int pixel = image.getRGB(x, y);
                            if (pixel != 0xFFFFFFFF) { // Not white
                                hasContent = true;
                            }
                        }
                    }
                    
                    System.out.println("Image has content: " + hasContent);
                    
                } else {
                    System.out.println("Failed to render image");
                }
            }
            
            // Clean up
            browser.dispose();
            
            System.out.println("Test completed.");
            
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        System.exit(0);
    }
}
