package net.sixik.sdmshoprework;

import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.server.network.ServerPlayerEntity;

/**
 * Mock implementation of the SDMShopR API.
 * This is a placeholder for the actual implementation from the SDMShopR mod.
 * In a production environment, this would be replaced by the actual mod dependency.
 */
public class SDMShopR {

    /**
     * Gets the player's money balance.
     *
     * @param player The player to check
     * @return The player's money balance
     */
    public static long getMoney(PlayerEntity player) {
        // Mock implementation - in a real environment, this would call the actual mod's API
        return 0; // Return 0 by default if SDMShopR is not available
    }

    /**
     * Sets the player's money balance.
     *
     * @param player The server player
     * @param amount The amount to set
     */
    public static void setMoney(ServerPlayerEntity player, long amount) {
        // Mock implementation - in a real environment, this would call the actual mod's API
        // Do nothing in this mock version
    }

    /**
     * Adds money to the player's balance.
     *
     * @param player The server player
     * @param amount The amount to add
     */
    public static void addMoney(ServerPlayerEntity player, long amount) {
        // Mock implementation - in a real environment, this would call the actual mod's API
        // Do nothing in this mock version
    }
}
