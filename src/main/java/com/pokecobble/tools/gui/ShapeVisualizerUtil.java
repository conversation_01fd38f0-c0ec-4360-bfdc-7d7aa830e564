package com.pokecobble.tools.gui;

import com.pokecobble.Pokecobbleclaim;
import net.minecraft.client.MinecraftClient;

/**
 * Utility class for working with the Shape Visualizer tool.
 * This class provides methods to convert between shape visualizer coordinates
 * and screen coordinates, ensuring UI elements are positioned correctly.
 */
public class ShapeVisualizerUtil {
    // Base screen dimensions used when capturing coordinates with the shape visualizer
    // These should match the screen resolution used when creating UI elements with the shape visualizer
    public static final int BASE_SCREEN_WIDTH = 1920;
    public static final int BASE_SCREEN_HEIGHT = 1080;

    /**
     * Converts shape visualizer X coordinate to screen X coordinate.
     * This method ensures that coordinates from the shape visualizer tool
     * are correctly positioned on the screen, regardless of screen size.
     *
     * @param x The X coordinate from the shape visualizer
     * @return The screen X coordinate
     */
    public static int getScreenX(int x) {
        // Scale the X coordinate based on the current screen width
        int currentWidth = getScreenWidth();

        // Log the scaling for debugging
        if (currentWidth != BASE_SCREEN_WIDTH) {
            Pokecobbleclaim.LOGGER.debug("Scaling X coordinate from " + x + " to " +
                getScaledCoordinate(x, BASE_SCREEN_WIDTH, currentWidth) +
                " (screen width: " + currentWidth + ")");
        }

        return getScaledCoordinate(x, BASE_SCREEN_WIDTH, currentWidth);
    }

    /**
     * Converts shape visualizer Y coordinate to screen Y coordinate.
     * This method ensures that coordinates from the shape visualizer tool
     * are correctly positioned on the screen, regardless of screen size.
     *
     * @param y The Y coordinate from the shape visualizer
     * @return The screen Y coordinate
     */
    public static int getScreenY(int y) {
        // Scale the Y coordinate based on the current screen height
        int currentHeight = getScreenHeight();

        // Log the scaling for debugging
        if (currentHeight != BASE_SCREEN_HEIGHT) {
            Pokecobbleclaim.LOGGER.debug("Scaling Y coordinate from " + y + " to " +
                getScaledCoordinate(y, BASE_SCREEN_HEIGHT, currentHeight) +
                " (screen height: " + currentHeight + ")");
        }

        return getScaledCoordinate(y, BASE_SCREEN_HEIGHT, currentHeight);
    }

    /**
     * Gets the current screen width.
     *
     * @return The current screen width
     */
    public static int getScreenWidth() {
        return MinecraftClient.getInstance().getWindow().getScaledWidth();
    }

    /**
     * Gets the current screen height.
     *
     * @return The current screen height
     */
    public static int getScreenHeight() {
        return MinecraftClient.getInstance().getWindow().getScaledHeight();
    }

    /**
     * Calculates a position that scales with screen size.
     * This is useful for positioning elements relative to screen edges.
     *
     * @param baseCoordinate The base coordinate (from shape visualizer)
     * @param baseScreenSize The screen size when the coordinate was captured
     * @param currentScreenSize The current screen size
     * @return The scaled coordinate
     */
    public static int getScaledCoordinate(int baseCoordinate, int baseScreenSize, int currentScreenSize) {
        // Use double for more precise calculations
        double scaleFactor = (double)currentScreenSize / (double)baseScreenSize;
        return (int)Math.round(baseCoordinate * scaleFactor);
    }

    /**
     * Gets the absolute coordinate without scaling.
     * This is useful when you want to position elements at exact pixel coordinates
     * regardless of screen size.
     *
     * @param coordinate The absolute coordinate
     * @return The same coordinate without scaling
     */
    public static int getAbsoluteCoordinate(int coordinate) {
        return coordinate;
    }

    /**
     * Calculates the position of a UI element relative to a phone texture.
     * This ensures that UI elements maintain their position relative to the phone
     * when the screen is resized.
     *
     * @param relativeX The X position relative to the phone's top-left corner
     * @param relativeY The Y position relative to the phone's top-left corner
     * @param phoneX The X position of the phone on the screen
     * @param phoneY The Y position of the phone on the screen
     * @return An array containing [absoluteX, absoluteY]
     */
    public static int[] getPhoneRelativePosition(int relativeX, int relativeY, int phoneX, int phoneY) {
        return new int[] { phoneX + relativeX, phoneY + relativeY };
    }

    /**
     * Converts screen coordinates to coordinates relative to the phone.
     * This is useful for the shape visualizer to capture positions relative to the phone.
     *
     * @param screenX The X position on the screen
     * @param screenY The Y position on the screen
     * @param phoneX The X position of the phone on the screen
     * @param phoneY The Y position of the phone on the screen
     * @return An array containing [relativeX, relativeY]
     */
    public static int[] getRelativeToPhonePosition(int screenX, int screenY, int phoneX, int phoneY) {
        return new int[] { screenX - phoneX, screenY - phoneY };
    }
}
