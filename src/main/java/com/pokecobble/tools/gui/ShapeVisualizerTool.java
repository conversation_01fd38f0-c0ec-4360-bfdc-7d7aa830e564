package com.pokecobble.tools.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.DevModeManager;
import com.pokecobble.phone.gui.PhoneScreen;
import com.pokecobble.phone.gui.BankAppScreen;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import net.minecraft.client.gui.screen.Screen;
import org.lwjgl.glfw.GLFW;
import net.minecraft.text.Text;

/**
 * Manages the Shape Visualizer tool.
 */
public class ShapeVisualizerTool {
    private static KeyBinding keyBinding;
    private static boolean isActive = false;
    private static Screen parentScreen = null;
    private static ShapeVisualizerTool instance;

    /**
     * Checks if dev mode is enabled for the current player.
     *
     * @return True if dev mode is enabled, false otherwise
     */
    private static boolean isDevModeEnabled() {
        MinecraftClient client = MinecraftClient.getInstance();
        return client.player != null && DevModeManager.getInstance().isDevModeEnabled(client.player);
    }

    /**
     * Gets the instance of the ShapeVisualizerTool.
     *
     * @return The ShapeVisualizerTool instance
     */
    public static ShapeVisualizerTool getInstance() {
        if (instance == null) {
            instance = new ShapeVisualizerTool();
        }
        return instance;
    }

    /**
     * Registers the Shape Visualizer tool.
     */
    public static void register() {
        Pokecobbleclaim.LOGGER.info("Registering Shape Visualizer Tool");

        // Register keybinding (V key)
        keyBinding = KeyBindingHelper.registerKeyBinding(new KeyBinding(
            "key.pokecobbleclaim.shape_visualizer",
            InputUtil.Type.KEYSYM,
            GLFW.GLFW_KEY_V,
            "category.pokecobbleclaim.tools"
        ));

        // Register tick event to check for keybinding
        ClientTickEvents.END_CLIENT_TICK.register(client -> {
            if (keyBinding.wasPressed() && isDevModeEnabled()) {
                toggleShapeVisualizer();
            }
        });
    }

    /**
     * Toggles the Shape Visualizer tool.
     * If the current screen is a phone-related screen, it will use the PhoneShapeVisualizerScreen.
     * Otherwise, it will use the standard EnhancedShapeVisualizerScreen.
     */
    public static void toggleShapeVisualizer() {
        MinecraftClient client = MinecraftClient.getInstance();
        Screen currentScreen = client.currentScreen;

        if (client.currentScreen instanceof EnhancedShapeVisualizerScreen) {
            // Close the screen if it's already open and restore parent screen
            client.setScreen(parentScreen);
            parentScreen = null;
            isActive = false;
        } else {
            // Check if the current screen is a phone-related screen
            if (currentScreen instanceof PhoneScreen || currentScreen instanceof BankAppScreen) {
                // Use the phone-specific shape visualizer
                openOnPhone();
            } else {
                // Store the current screen as parent
                parentScreen = currentScreen;

                // Open the standard shape visualizer screen
                client.setScreen(new EnhancedShapeVisualizerScreen());
                isActive = true;

                // Show a notification
                com.pokecobble.town.client.NotificationRenderer.addNotification(
                    "Shape Visualizer activated. Press ESC to close.");

                // Log for debugging
                Pokecobbleclaim.LOGGER.info("Opened Shape Visualizer. Parent screen: " +
                    (parentScreen != null ? parentScreen.getClass().getSimpleName() : "null"));
            }
        }
    }

    /**
     * Opens the Shape Visualizer on top of the phone screen or bank app screen.
     * This is called when the V key is pressed while a phone-related screen is open.
     */
    public static void openOnPhone() {
        MinecraftClient client = MinecraftClient.getInstance();
        Screen currentScreen = client.currentScreen;

        // Check if the current screen is a phone-related screen
        if (currentScreen instanceof PhoneScreen || currentScreen instanceof BankAppScreen) {
            // Store the current screen as parent
            parentScreen = currentScreen;

            // Open the phone shape visualizer screen which renders on top of the current screen
            client.setScreen(new PhoneShapeVisualizerScreen(currentScreen));
            isActive = true;

            // Show a notification
            com.pokecobble.town.client.NotificationRenderer.addNotification(
                "Shape Visualizer activated. Press ESC or V to return.");

            // Log for debugging
            Pokecobbleclaim.LOGGER.info("Opened Shape Visualizer on top of " +
                currentScreen.getClass().getSimpleName() + " screen.");
        } else {
            // Log that the current screen is not supported
            Pokecobbleclaim.LOGGER.warn("Cannot open Shape Visualizer on top of " +
                (currentScreen != null ? currentScreen.getClass().getSimpleName() : "null") +
                " screen. Only PhoneScreen and BankAppScreen are supported.");
        }
    }

    /**
     * Sets the active state of the Shape Visualizer tool.
     *
     * @param active Whether the tool should be active
     */
    public static void setActive(boolean active) {
        isActive = active;
    }

    /**
     * Handles key presses for the Shape Visualizer when it's active.
     *
     * @param key The key code
     * @param scanCode The scan code
     * @param modifiers The modifiers
     * @return True if the key press was handled, false otherwise
     */
    public boolean handleKeyPress(int key, int scanCode, int modifiers) {
        // Handle ESC key to close the visualizer
        if (key == GLFW.GLFW_KEY_ESCAPE) {
            toggleShapeVisualizer();
            return true;
        }
        return false;
    }

    /**
     * Handles mouse clicks for the Shape Visualizer when it's active.
     *
     * @param mouseX The mouse X coordinate
     * @param mouseY The mouse Y coordinate
     * @param button The mouse button
     * @return True if the mouse click was handled, false otherwise
     */
    public boolean handleMouseClick(double mouseX, double mouseY, int button) {
        // Pass the event to the current screen if it's any type of shape visualizer
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.currentScreen instanceof EnhancedShapeVisualizerScreen) {
            // The screen will handle the click
            return true;
        }
        return false;
    }

    /**
     * Handles mouse releases for the Shape Visualizer when it's active.
     *
     * @param mouseX The mouse X coordinate
     * @param mouseY The mouse Y coordinate
     * @param button The mouse button
     * @return True if the mouse release was handled, false otherwise
     */
    public boolean handleMouseRelease(double mouseX, double mouseY, int button) {
        // For now, just pass the event to the current screen if it's a shape visualizer
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.currentScreen instanceof EnhancedShapeVisualizerScreen) {
            // The screen will handle the release
            return true;
        }
        return false;
    }

    /**
     * Handles mouse drag events for the Shape Visualizer when it's active.
     *
     * @param mouseX The mouse X coordinate
     * @param mouseY The mouse Y coordinate
     * @param button The mouse button
     * @param deltaX The change in X coordinate
     * @param deltaY The change in Y coordinate
     * @return True if the mouse drag was handled, false otherwise
     */
    public boolean handleMouseDrag(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        // For now, just pass the event to the current screen if it's a shape visualizer
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.currentScreen instanceof EnhancedShapeVisualizerScreen) {
            // The screen will handle the drag
            return true;
        }
        return false;
    }

    /**
     * Handles mouse scroll events for the Shape Visualizer when it's active.
     *
     * @param mouseX The mouse X coordinate
     * @param mouseY The mouse Y coordinate
     * @param amount The scroll amount
     * @return True if the mouse scroll was handled, false otherwise
     */
    public boolean handleMouseScroll(double mouseX, double mouseY, double amount) {
        // For now, just pass the event to the current screen if it's a shape visualizer
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.currentScreen instanceof EnhancedShapeVisualizerScreen) {
            // The screen will handle the scroll
            return true;
        }
        return false;
    }

    /**
     * Checks if the Shape Visualizer tool is active.
     *
     * @return True if the tool is active, false otherwise
     */
    public static boolean isActive() {
        return isActive;
    }
}
