package com.pokecobble.tools.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.client.NotificationRenderer;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.SliderWidget;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.text.Text;
import net.minecraft.util.math.MathHelper;
import org.lwjgl.glfw.GLFW;

import java.util.ArrayList;
import java.util.List;

/**
 * A tool for visualizing and positioning UI shapes.
 * This screen overlays on top of everything and allows the user to create, modify,
 * and copy shape information for UI design.
 */
public class ShapeVisualizerScreen extends Screen {
    // Shape types
    public enum ShapeType {
        RECTANGLE("Rectangle"),
        ROUNDED_RECTANGLE("Rounded Rectangle"),
        CIR<PERSON><PERSON>("Circle"),
        LINE("Line");

        private final String name;

        ShapeType(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

    // Current shape properties
    private ShapeType currentShapeType = ShapeType.RECTANGLE;
    private int shapeX = 100;
    private int shapeY = 100;
    private int shapeWidth = 200;
    private int shapeHeight = 100;
    private int cornerRadius = 5;
    private int shapeColor = 0xAAFFFFFF; // Semi-transparent white

    // UI components
    private ButtonWidget shapeTypeButton;
    private SliderWidget cornerRadiusSlider;
    private ButtonWidget copyButton;
    private List<ButtonWidget> shapeButtons = new ArrayList<>();

    // Dragging state
    private boolean isDragging = false;
    private int dragStartX, dragStartY;
    private int dragStartShapeX, dragStartShapeY;

    // Control panel dimensions
    private static final int PANEL_WIDTH = 200;
    private static final int PANEL_HEIGHT = 250;
    private int panelX, panelY;

    public ShapeVisualizerScreen() {
        super(Text.literal("Shape Visualizer"));
    }

    @Override
    protected void init() {
        super.init();

        // Position the control panel in the top-right corner
        panelX = this.width - PANEL_WIDTH - 10;
        panelY = 10;

        // Add shape type buttons
        int buttonY = panelY + 30;
        int buttonHeight = 20;
        int buttonSpacing = 5;

        for (ShapeType type : ShapeType.values()) {
            ButtonWidget button = ButtonWidget.builder(Text.literal(type.getName()), btn -> {
                currentShapeType = type;
                updateControlsVisibility();
            })
            .dimensions(panelX + 10, buttonY, PANEL_WIDTH - 20, buttonHeight)
            .build();

            this.addDrawableChild(button);
            shapeButtons.add(button);
            buttonY += buttonHeight + buttonSpacing;
        }

        // Add corner radius slider
        cornerRadiusSlider = new SliderWidget(
            panelX + 10, buttonY, PANEL_WIDTH - 20, buttonHeight,
            Text.literal("Corner Radius: " + cornerRadius), cornerRadius / 20.0f
        ) {
            @Override
            protected void updateMessage() {
                setMessage(Text.literal("Corner Radius: " + cornerRadius));
            }

            @Override
            protected void applyValue() {
                cornerRadius = (int) (this.value * 20.0f);
                updateMessage();
            }
        };
        this.addDrawableChild(cornerRadiusSlider);
        buttonY += buttonHeight + buttonSpacing;

        // Add copy button
        copyButton = ButtonWidget.builder(Text.literal("Copy Shape Info"), btn -> {
            copyShapeInfo();
        })
        .dimensions(panelX + 10, buttonY, PANEL_WIDTH - 20, buttonHeight)
        .build();
        this.addDrawableChild(copyButton);

        // Update controls visibility based on current shape type
        updateControlsVisibility();
    }

    /**
     * Updates the visibility of controls based on the current shape type.
     */
    private void updateControlsVisibility() {
        // Show corner radius slider only for rounded rectangle
        cornerRadiusSlider.visible = (currentShapeType == ShapeType.ROUNDED_RECTANGLE);
    }

    /**
     * Copies the current shape information to the clipboard.
     */
    private void copyShapeInfo() {
        String shapeInfo = formatShapeInfo();
        MinecraftClient.getInstance().keyboard.setClipboard(shapeInfo);
        NotificationRenderer.addSuccessNotification("Shape info copied to clipboard!");
        Pokecobbleclaim.LOGGER.info("Copied shape info: " + shapeInfo);
    }

    /**
     * Formats the current shape information as a string.
     */
    private String formatShapeInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("Shape: ").append(currentShapeType.getName()).append("\n");
        sb.append("Position: x=").append(shapeX).append(", y=").append(shapeY).append("\n");
        sb.append("Size: width=").append(shapeWidth).append(", height=").append(shapeHeight).append("\n");

        if (currentShapeType == ShapeType.ROUNDED_RECTANGLE) {
            sb.append("Corner Radius: ").append(cornerRadius).append("\n");
        }

        // Format color as hex
        String colorHex = String.format("#%08X", shapeColor);
        sb.append("Color: ").append(colorHex);

        return sb.toString();
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Render the current shape
        renderShape(context);

        // Draw control panel background
        context.fill(panelX, panelY, panelX + PANEL_WIDTH, panelY + PANEL_HEIGHT, 0xE0101010);
        context.drawBorder(panelX, panelY, PANEL_WIDTH, PANEL_HEIGHT, 0xFFFFFFFF);

        // Draw title
        context.drawCenteredTextWithShadow(this.textRenderer, "Shape Visualizer",
            panelX + PANEL_WIDTH / 2, panelY + 10, 0xFFFFFF);

        // Draw shape info
        int infoY = panelY + PANEL_HEIGHT - 60;
        context.drawTextWithShadow(this.textRenderer, "X: " + shapeX + ", Y: " + shapeY,
            panelX + 10, infoY, 0xFFFFFF);
        context.drawTextWithShadow(this.textRenderer, "W: " + shapeWidth + ", H: " + shapeHeight,
            panelX + 10, infoY + 12, 0xFFFFFF);

        // Draw super (buttons, etc.)
        super.render(context, mouseX, mouseY, delta);
    }

    /**
     * Renders the current shape.
     */
    private void renderShape(DrawContext context) {
        switch (currentShapeType) {
            case RECTANGLE:
                context.fill(shapeX, shapeY, shapeX + shapeWidth, shapeY + shapeHeight, shapeColor);
                break;

            case ROUNDED_RECTANGLE:
                drawRoundedRect(context, shapeX, shapeY, shapeWidth, shapeHeight, cornerRadius, shapeColor);
                break;

            case CIRCLE:
                drawCircle(context, shapeX + shapeWidth / 2, shapeY + shapeHeight / 2,
                    Math.min(shapeWidth, shapeHeight) / 2, shapeColor);
                break;

            case LINE:
                context.drawHorizontalLine(shapeX, shapeX + shapeWidth, shapeY + shapeHeight / 2, shapeColor);
                break;
        }
    }

    /**
     * Draws a rounded rectangle.
     */
    private void drawRoundedRect(DrawContext context, int x, int y, int width, int height, int radius, int color) {
        // Clamp radius to half of the smallest dimension
        int maxRadius = Math.min(width, height) / 2;
        radius = Math.min(radius, maxRadius);

        // Main rectangle (excluding corners)
        context.fill(x + radius, y, x + width - radius, y + height, color);
        context.fill(x, y + radius, x + width, y + height - radius, color);

        // Draw the four corner quadrants
        drawCircleQuadrant(context, x + radius, y + radius, radius, color, 0);
        drawCircleQuadrant(context, x + width - radius, y + radius, radius, color, 1);
        drawCircleQuadrant(context, x + width - radius, y + height - radius, radius, color, 2);
        drawCircleQuadrant(context, x + radius, y + height - radius, radius, color, 3);
    }

    /**
     * Draws a quadrant of a circle.
     * @param quadrant 0=top-left, 1=top-right, 2=bottom-right, 3=bottom-left
     */
    private void drawCircleQuadrant(DrawContext context, int centerX, int centerY, int radius, int color, int quadrant) {
        for (int y = -radius; y <= 0; y++) {
            for (int x = -radius; x <= 0; x++) {
                if (x*x + y*y <= radius*radius) {
                    int drawX = 0, drawY = 0;

                    switch (quadrant) {
                        case 0: // Top-left
                            drawX = centerX + x;
                            drawY = centerY + y;
                            break;
                        case 1: // Top-right
                            drawX = centerX - x;
                            drawY = centerY + y;
                            break;
                        case 2: // Bottom-right
                            drawX = centerX - x;
                            drawY = centerY - y;
                            break;
                        case 3: // Bottom-left
                            drawX = centerX + x;
                            drawY = centerY - y;
                            break;
                    }

                    context.fill(drawX, drawY, drawX + 1, drawY + 1, color);
                }
            }
        }
    }

    /**
     * Draws a circle.
     */
    private void drawCircle(DrawContext context, int centerX, int centerY, int radius, int color) {
        int diameter = radius * 2;
        int startX = centerX - radius;
        int startY = centerY - radius;

        for (int y = 0; y < diameter; y++) {
            for (int x = 0; x < diameter; x++) {
                int dx = x - radius;
                int dy = y - radius;
                if (dx*dx + dy*dy <= radius*radius) {
                    context.fill(startX + x, startY + y, startX + x + 1, startY + y + 1, color);
                }
            }
        }
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Check if click is inside the shape
        if (button == 0 && isPointInShape((int)mouseX, (int)mouseY)) {
            isDragging = true;
            dragStartX = (int)mouseX;
            dragStartY = (int)mouseY;
            dragStartShapeX = shapeX;
            dragStartShapeY = shapeY;
            return true;
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        if (button == 0) {
            isDragging = false;
        }

        return super.mouseReleased(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        if (isDragging && button == 0) {
            // Update shape position
            shapeX = dragStartShapeX + (int)(mouseX - dragStartX);
            shapeY = dragStartShapeY + (int)(mouseY - dragStartY);
            return true;
        }

        return super.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        // Resize shape with scroll wheel
        if (isPointInShape((int)mouseX, (int)mouseY)) {
            if (GLFW.glfwGetKey(MinecraftClient.getInstance().getWindow().getHandle(), GLFW.GLFW_KEY_LEFT_SHIFT) == GLFW.GLFW_PRESS) {
                // Shift+scroll to adjust height
                shapeHeight = Math.max(10, shapeHeight + (int)(amount * 10));
            } else if (GLFW.glfwGetKey(MinecraftClient.getInstance().getWindow().getHandle(), GLFW.GLFW_KEY_LEFT_CONTROL) == GLFW.GLFW_PRESS) {
                // Ctrl+scroll to adjust corner radius
                if (currentShapeType == ShapeType.ROUNDED_RECTANGLE) {
                    cornerRadius = MathHelper.clamp(cornerRadius + (int)(amount * 2), 0, 20);
                    // Update the slider by recreating it
                    int index = this.children().indexOf(cornerRadiusSlider);
                    if (index >= 0) {
                        this.remove(cornerRadiusSlider);
                        cornerRadiusSlider = new SliderWidget(
                            cornerRadiusSlider.getX(), cornerRadiusSlider.getY(),
                            cornerRadiusSlider.getWidth(), cornerRadiusSlider.getHeight(),
                            Text.literal("Corner Radius: " + cornerRadius), cornerRadius / 20.0f
                        ) {
                            @Override
                            protected void updateMessage() {
                                setMessage(Text.literal("Corner Radius: " + cornerRadius));
                            }

                            @Override
                            protected void applyValue() {
                                cornerRadius = (int) (this.value * 20.0f);
                                updateMessage();
                            }
                        };
                        this.addDrawableChild(cornerRadiusSlider);
                    }
                }
            } else {
                // Normal scroll to adjust width
                shapeWidth = Math.max(10, shapeWidth + (int)(amount * 10));
            }
            return true;
        }

        return super.mouseScrolled(mouseX, mouseY, amount);
    }

    /**
     * Checks if a point is inside the current shape.
     */
    private boolean isPointInShape(int x, int y) {
        switch (currentShapeType) {
            case RECTANGLE:
            case ROUNDED_RECTANGLE:
                return x >= shapeX && x <= shapeX + shapeWidth &&
                       y >= shapeY && y <= shapeY + shapeHeight;

            case CIRCLE:
                int centerX = shapeX + shapeWidth / 2;
                int centerY = shapeY + shapeHeight / 2;
                int radius = Math.min(shapeWidth, shapeHeight) / 2;
                int dx = x - centerX;
                int dy = y - centerY;
                return dx*dx + dy*dy <= radius*radius;

            case LINE:
                return x >= shapeX && x <= shapeX + shapeWidth &&
                       y >= shapeY + shapeHeight / 2 - 2 && y <= shapeY + shapeHeight / 2 + 2;

            default:
                return false;
        }
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        // Arrow keys to move shape
        int moveAmount = 1;
        if ((modifiers & GLFW.GLFW_MOD_SHIFT) != 0) {
            moveAmount = 10;
        }

        switch (keyCode) {
            case GLFW.GLFW_KEY_LEFT:
                shapeX -= moveAmount;
                return true;

            case GLFW.GLFW_KEY_RIGHT:
                shapeX += moveAmount;
                return true;

            case GLFW.GLFW_KEY_UP:
                shapeY -= moveAmount;
                return true;

            case GLFW.GLFW_KEY_DOWN:
                shapeY += moveAmount;
                return true;

            case GLFW.GLFW_KEY_ESCAPE:
                this.close();
                return true;
        }

        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    @Override
    public boolean shouldPause() {
        // Don't pause the game when this screen is open
        return false;
    }
}
