package com.pokecobble.tools.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.client.NotificationRenderer;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.SliderWidget;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.text.Text;
import net.minecraft.util.math.MathHelper;
import org.lwjgl.glfw.GLFW;

import java.util.ArrayList;
import java.util.List;

/**
 * An enhanced tool for visualizing and positioning UI shapes.
 * This screen overlays on top of everything and allows the user to create, modify,
 * and copy shape information for UI design.
 */
public class EnhancedShapeVisualizerScreen extends Screen {
    // Shape types
    public enum ShapeType {
        RECTANGLE("Rectangle"),
        ROUNDED_RECTANGLE("Rounded Rectangle"),
        <PERSON><PERSON><PERSON><PERSON>("Circle"),
        <PERSON><PERSON><PERSON>("Line");

        private final String name;

        ShapeType(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

    // Current shape properties
    private ShapeType currentShapeType = ShapeType.RECTANGLE;
    private int shapeX = 100;
    private int shapeY = 100;
    private int shapeWidth = 200;
    private int shapeHeight = 100;
    private int cornerRadius = 5;
    private int shapeColor = DEFAULT_COLOR;
    // Removed border options to make the tool more compact

    // UI components
    private ButtonWidget shapeTypeButton;
    private SliderWidget cornerRadiusSlider;
    private SliderWidget alphaSlider;
    private ButtonWidget copyButton;
    // Removed toggle border button
    // Removed color buttons
    private List<ButtonWidget> shapeButtons = new ArrayList<>();
    private TextFieldWidget xField, yField, widthField, heightField;

    // Dragging state
    private boolean isDragging = false;
    private int dragStartX, dragStartY;
    private int dragStartShapeX, dragStartShapeY;

    // Resizing state
    private boolean isResizing = false;
    private int resizeStartX, resizeStartY;
    private int resizeStartWidth, resizeStartHeight;
    private int resizeHandle = -1; // 0=top-left, 1=top-right, 2=bottom-right, 3=bottom-left

    // Control panel dimensions - much more compact
    private static final int PANEL_WIDTH = 160;
    private static final int PANEL_HEIGHT = 220;
    private int panelX, panelY;

    // Panel dragging state
    private boolean isDraggingPanel = false;
    private int dragPanelStartX, dragPanelStartY;
    private int dragPanelOffsetX, dragPanelOffsetY;

    // UI colors
    private static final int PANEL_BG_COLOR = 0xE0101020;
    private static final int PANEL_HEADER_COLOR = 0xFF1E1E2E;
    private static final int PANEL_BORDER_COLOR = 0xFF3E3E5E;
    private static final int BUTTON_BG_COLOR = 0xFF2E2E4E;
    private static final int BUTTON_HOVER_COLOR = 0xFF3E3E5E;
    private static final int BUTTON_ACTIVE_COLOR = 0xFF4E4E6E;

    // Default color
    private static final int DEFAULT_COLOR = 0xAAFFFFFF; // Semi-transparent white

    public EnhancedShapeVisualizerScreen() {
        super(Text.literal("Shape Visualizer"));
    }

    @Override
    protected void init() {
        super.init();

        // Position the control panel in the top-left corner
        panelX = 10;
        panelY = 10;

        // Use a more compact layout with smaller buttons and spacing

        int buttonY = panelY + 28; // Start closer to header
        int buttonHeight = 16; // Even smaller button height
        int buttonSpacing = 2; // Minimal spacing

        // Add shape type buttons
        for (ShapeType type : ShapeType.values()) {
            ButtonWidget button = ButtonWidget.builder(Text.literal(type.getName()), btn -> {
                currentShapeType = type;
                updateControlsVisibility();
            })
            .dimensions(panelX + 10, buttonY, PANEL_WIDTH - 20, buttonHeight)
            .build();

            this.addDrawableChild(button);
            shapeButtons.add(button);
            buttonY += buttonHeight + buttonSpacing;
        }

        // Add position and size text fields
        int fieldWidth = (PANEL_WIDTH - 30) / 2;
        int labelWidth = 15;

        // X position field
        this.addDrawableChild(new TextFieldWidget(this.textRenderer, panelX + labelWidth + 10, buttonY, fieldWidth - labelWidth, buttonHeight, Text.literal("")));
        xField = (TextFieldWidget) this.children().get(this.children().size() - 1);
        xField.setText(String.valueOf(shapeX));
        xField.setChangedListener(this::onXChanged);

        // Y position field
        this.addDrawableChild(new TextFieldWidget(this.textRenderer, panelX + fieldWidth + labelWidth + 15, buttonY, fieldWidth - labelWidth, buttonHeight, Text.literal("")));
        yField = (TextFieldWidget) this.children().get(this.children().size() - 1);
        yField.setText(String.valueOf(shapeY));
        yField.setChangedListener(this::onYChanged);

        buttonY += buttonHeight + buttonSpacing;

        // Width field
        this.addDrawableChild(new TextFieldWidget(this.textRenderer, panelX + labelWidth + 10, buttonY, fieldWidth - labelWidth, buttonHeight, Text.literal("")));
        widthField = (TextFieldWidget) this.children().get(this.children().size() - 1);
        widthField.setText(String.valueOf(shapeWidth));
        widthField.setChangedListener(this::onWidthChanged);

        // Height field
        this.addDrawableChild(new TextFieldWidget(this.textRenderer, panelX + fieldWidth + labelWidth + 15, buttonY, fieldWidth - labelWidth, buttonHeight, Text.literal("")));
        heightField = (TextFieldWidget) this.children().get(this.children().size() - 1);
        heightField.setText(String.valueOf(shapeHeight));
        heightField.setChangedListener(this::onHeightChanged);

        buttonY += buttonHeight + buttonSpacing;

        // Add corner radius slider
        cornerRadiusSlider = new SliderWidget(
            panelX + 10, buttonY, PANEL_WIDTH - 20, buttonHeight,
            Text.literal("Corner Radius: " + cornerRadius), cornerRadius / 20.0f
        ) {
            @Override
            protected void updateMessage() {
                setMessage(Text.literal("Corner Radius: " + cornerRadius));
            }

            @Override
            protected void applyValue() {
                cornerRadius = (int) (this.value * 20.0f);
                updateMessage();
            }
        };
        this.addDrawableChild(cornerRadiusSlider);
        buttonY += buttonHeight + buttonSpacing;

        // Add alpha slider
        alphaSlider = new SliderWidget(
            panelX + 10, buttonY, PANEL_WIDTH - 20, buttonHeight,
            Text.literal("Opacity: " + ((shapeColor >> 24) & 0xFF)), ((shapeColor >> 24) & 0xFF) / 255.0f
        ) {
            @Override
            protected void updateMessage() {
                setMessage(Text.literal("Opacity: " + ((shapeColor >> 24) & 0xFF)));
            }

            @Override
            protected void applyValue() {
                int alpha = (int) (this.value * 255.0f);
                shapeColor = (alpha << 24) | (shapeColor & 0x00FFFFFF);
                updateMessage();
            }
        };
        this.addDrawableChild(alphaSlider);
        buttonY += buttonHeight + buttonSpacing;

        // Removed toggle border button to make the tool more compact

        // Removed color buttons to simplify the tool

        // Add copy button with a more prominent style
        buttonY += 5; // Add a bit more space before the copy button
        copyButton = ButtonWidget.builder(Text.literal("Copy Shape Info"), btn -> {
            copyShapeInfo();
        })
        .dimensions(panelX + 10, buttonY, PANEL_WIDTH - 20, buttonHeight + 2) // Slightly taller
        // We can't use tooltip directly with Text
        .build();
        this.addDrawableChild(copyButton);

        // Update controls visibility based on current shape type
        updateControlsVisibility();
    }

    /**
     * Updates the visibility of controls based on the current shape type.
     */
    private void updateControlsVisibility() {
        // Show corner radius slider only for rounded rectangle
        cornerRadiusSlider.visible = (currentShapeType == ShapeType.ROUNDED_RECTANGLE);
    }

    /**
     * Handles changes to the X position field.
     */
    private void onXChanged(String text) {
        try {
            shapeX = Integer.parseInt(text);
        } catch (NumberFormatException e) {
            // Ignore invalid input
        }
    }

    /**
     * Handles changes to the Y position field.
     */
    private void onYChanged(String text) {
        try {
            shapeY = Integer.parseInt(text);
        } catch (NumberFormatException e) {
            // Ignore invalid input
        }
    }

    /**
     * Handles changes to the width field.
     */
    private void onWidthChanged(String text) {
        try {
            shapeWidth = Integer.parseInt(text);
        } catch (NumberFormatException e) {
            // Ignore invalid input
        }
    }

    /**
     * Handles changes to the height field.
     */
    private void onHeightChanged(String text) {
        try {
            shapeHeight = Integer.parseInt(text);
        } catch (NumberFormatException e) {
            // Ignore invalid input
        }
    }

    /**
     * Updates the text fields with the current shape properties.
     */
    private void updateTextFields() {
        if (xField != null) xField.setText(String.valueOf(shapeX));
        if (yField != null) yField.setText(String.valueOf(shapeY));
        if (widthField != null) widthField.setText(String.valueOf(shapeWidth));
        if (heightField != null) heightField.setText(String.valueOf(shapeHeight));
    }

    /**
     * Copies the current shape information to the clipboard.
     */
    private void copyShapeInfo() {
        String shapeInfo = formatShapeInfo();
        MinecraftClient.getInstance().keyboard.setClipboard(shapeInfo);
        NotificationRenderer.addSuccessNotification("Shape info copied to clipboard!");
        Pokecobbleclaim.LOGGER.info("Copied shape info: " + shapeInfo);
    }

    /**
     * Formats the current shape information as a string.
     */
    private String formatShapeInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("Shape: ").append(currentShapeType.getName()).append("\n");
        sb.append("Position: x=").append(shapeX).append(", y=").append(shapeY).append("\n");
        sb.append("Size: width=").append(shapeWidth).append(", height=").append(shapeHeight).append("\n");

        if (currentShapeType == ShapeType.ROUNDED_RECTANGLE) {
            sb.append("Corner Radius: ").append(cornerRadius).append("\n");
        }

        // Format color as hex
        String colorHex = String.format("#%08X", shapeColor);
        sb.append("Color: ").append(colorHex);

        return sb.toString();
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Render the current shape
        renderShape(context);

        // Draw resize handles if the shape is hovered
        if (isPointInShape(mouseX, mouseY) || isDragging || isResizing) {
            drawResizeHandles(context);
        }

        // Draw control panel background with gradient
        context.fillGradient(panelX, panelY, panelX + PANEL_WIDTH, panelY + PANEL_HEIGHT, PANEL_BG_COLOR, PANEL_BG_COLOR & 0xDFFFFFFF);
        context.drawBorder(panelX, panelY, PANEL_WIDTH, PANEL_HEIGHT, PANEL_BORDER_COLOR);

        // Draw header with a grip pattern to indicate it can be dragged
        context.fill(panelX, panelY, panelX + PANEL_WIDTH, panelY + 25, PANEL_HEADER_COLOR);

        // Draw grip pattern (three horizontal lines)
        int gripX = panelX + 5;
        int gripY = panelY + 12;
        int gripWidth = 10;
        int gripColor = 0x80FFFFFF;

        for (int i = 0; i < 3; i++) {
            context.fill(gripX, gripY + i * 3, gripX + gripWidth, gripY + i * 3 + 1, gripColor);
        }

        // Draw title
        context.drawCenteredTextWithShadow(this.textRenderer, "Shape Visualizer",
            panelX + PANEL_WIDTH / 2, panelY + 8, 0xFFFFFF);

        // Draw field labels with more compact styling
        context.drawTextWithShadow(this.textRenderer, "X:", panelX + 10, xField.getY() + 6, 0xBBBBBB);
        context.drawTextWithShadow(this.textRenderer, "Y:", panelX + PANEL_WIDTH / 2 + 5, yField.getY() + 6, 0xBBBBBB);
        context.drawTextWithShadow(this.textRenderer, "W:", panelX + 10, widthField.getY() + 6, 0xBBBBBB);
        context.drawTextWithShadow(this.textRenderer, "H:", panelX + PANEL_WIDTH / 2 + 5, heightField.getY() + 6, 0xBBBBBB);

        // Removed color button rendering

        // Draw super (buttons, etc.)
        super.render(context, mouseX, mouseY, delta);
    }

    /**
     * Draws resize handles for the shape with a modern style.
     */
    private void drawResizeHandles(DrawContext context) {
        int handleSize = 8; // Slightly larger handles
        int halfHandle = handleSize / 2;

        // Draw the four corner handles
        int[] handleX = {
            shapeX,
            shapeX + shapeWidth,
            shapeX + shapeWidth,
            shapeX
        };

        int[] handleY = {
            shapeY,
            shapeY,
            shapeY + shapeHeight,
            shapeY + shapeHeight
        };

        for (int i = 0; i < 4; i++) {
            // Draw a semi-transparent background for better visibility
            context.fill(
                handleX[i] - halfHandle - 1,
                handleY[i] - halfHandle - 1,
                handleX[i] + halfHandle + 1,
                handleY[i] + halfHandle + 1,
                0x80000000
            );

            // Draw the handle with a modern look
            context.fill(
                handleX[i] - halfHandle,
                handleY[i] - halfHandle,
                handleX[i] + halfHandle,
                handleY[i] + halfHandle,
                0xFFFFFFFF
            );

            // Draw a subtle border
            context.drawBorder(
                handleX[i] - halfHandle,
                handleY[i] - halfHandle,
                handleSize,
                handleSize,
                0x80000000
            );
        }
    }

    /**
     * Renders the current shape.
     */
    private void renderShape(DrawContext context) {
        switch (currentShapeType) {
            case RECTANGLE:
                context.fill(shapeX, shapeY, shapeX + shapeWidth, shapeY + shapeHeight, shapeColor);
                break;

            case ROUNDED_RECTANGLE:
                drawRoundedRect(context, shapeX, shapeY, shapeWidth, shapeHeight, cornerRadius, shapeColor);
                break;

            case CIRCLE:
                drawCircle(context, shapeX + shapeWidth / 2, shapeY + shapeHeight / 2,
                    Math.min(shapeWidth, shapeHeight) / 2, shapeColor);
                break;

            case LINE:
                context.drawHorizontalLine(shapeX, shapeX + shapeWidth, shapeY + shapeHeight / 2, shapeColor);
                break;
        }
    }

    /**
     * Draws a rounded rectangle.
     */
    private void drawRoundedRect(DrawContext context, int x, int y, int width, int height, int radius, int color) {
        // Clamp radius to half of the smallest dimension
        int maxRadius = Math.min(width, height) / 2;
        radius = Math.min(radius, maxRadius);

        // Main rectangle (excluding corners)
        context.fill(x + radius, y, x + width - radius, y + height, color);
        context.fill(x, y + radius, x + width, y + height - radius, color);

        // Draw the four corner quadrants
        drawCircleQuadrant(context, x + radius, y + radius, radius, color, 0);
        drawCircleQuadrant(context, x + width - radius, y + radius, radius, color, 1);
        drawCircleQuadrant(context, x + width - radius, y + height - radius, radius, color, 2);
        drawCircleQuadrant(context, x + radius, y + height - radius, radius, color, 3);
    }

    /**
     * Draws a quadrant of a circle.
     * @param quadrant 0=top-left, 1=top-right, 2=bottom-right, 3=bottom-left
     */
    private void drawCircleQuadrant(DrawContext context, int centerX, int centerY, int radius, int color, int quadrant) {
        for (int y = -radius; y <= 0; y++) {
            for (int x = -radius; x <= 0; x++) {
                if (x*x + y*y <= radius*radius) {
                    int drawX = 0, drawY = 0;

                    switch (quadrant) {
                        case 0: // Top-left
                            drawX = centerX + x;
                            drawY = centerY + y;
                            break;
                        case 1: // Top-right
                            drawX = centerX - x;
                            drawY = centerY + y;
                            break;
                        case 2: // Bottom-right
                            drawX = centerX - x;
                            drawY = centerY - y;
                            break;
                        case 3: // Bottom-left
                            drawX = centerX + x;
                            drawY = centerY - y;
                            break;
                    }

                    context.fill(drawX, drawY, drawX + 1, drawY + 1, color);
                }
            }
        }
    }

    /**
     * Draws a circle.
     */
    private void drawCircle(DrawContext context, int centerX, int centerY, int radius, int color) {
        int diameter = radius * 2;
        int startX = centerX - radius;
        int startY = centerY - radius;

        for (int y = 0; y < diameter; y++) {
            for (int x = 0; x < diameter; x++) {
                int dx = x - radius;
                int dy = y - radius;
                if (dx*dx + dy*dy <= radius*radius) {
                    context.fill(startX + x, startY + y, startX + x + 1, startY + y + 1, color);
                }
            }
        }
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Check if click is in the panel header (for dragging)
        if (button == 0 && mouseX >= panelX && mouseX <= panelX + PANEL_WIDTH &&
            mouseY >= panelY && mouseY <= panelY + 25) {
            isDraggingPanel = true;
            dragPanelStartX = (int)mouseX;
            dragPanelStartY = (int)mouseY;
            dragPanelOffsetX = panelX;
            dragPanelOffsetY = panelY;
            return true;
        }

        // Check if click is on a resize handle
        int handleSize = 8; // Slightly larger than visual size for easier clicking
        int[] handleX = {
            shapeX,
            shapeX + shapeWidth,
            shapeX + shapeWidth,
            shapeX
        };

        int[] handleY = {
            shapeY,
            shapeY,
            shapeY + shapeHeight,
            shapeY + shapeHeight
        };

        for (int i = 0; i < 4; i++) {
            if (Math.abs(mouseX - handleX[i]) <= handleSize / 2 &&
                Math.abs(mouseY - handleY[i]) <= handleSize / 2) {
                isResizing = true;
                resizeHandle = i;
                resizeStartX = (int)mouseX;
                resizeStartY = (int)mouseY;
                resizeStartWidth = shapeWidth;
                resizeStartHeight = shapeHeight;
                return true;
            }
        }

        // Check if click is inside the shape
        if (button == 0 && isPointInShape((int)mouseX, (int)mouseY)) {
            isDragging = true;
            dragStartX = (int)mouseX;
            dragStartY = (int)mouseY;
            dragStartShapeX = shapeX;
            dragStartShapeY = shapeY;
            return true;
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        if (button == 0) {
            isDragging = false;
            isResizing = false;
            isDraggingPanel = false;
            resizeHandle = -1;
        }

        return super.mouseReleased(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        if (isDraggingPanel && button == 0) {
            // Update panel position
            panelX = dragPanelOffsetX + (int)(mouseX - dragPanelStartX);
            panelY = dragPanelOffsetY + (int)(mouseY - dragPanelStartY);

            // Keep panel within screen bounds
            panelX = Math.max(0, Math.min(this.width - PANEL_WIDTH, panelX));
            panelY = Math.max(0, Math.min(this.height - 50, panelY));

            // Reinitialize the UI components with the new panel position
            this.clearChildren();
            this.init();
            return true;
        } else if (isResizing && button == 0) {
            // Handle resizing based on which handle is being dragged
            int dx = (int)(mouseX - resizeStartX);
            int dy = (int)(mouseY - resizeStartY);

            switch (resizeHandle) {
                case 0: // Top-left
                    shapeX = dragStartShapeX + dx;
                    shapeY = dragStartShapeY + dy;
                    shapeWidth = resizeStartWidth - dx;
                    shapeHeight = resizeStartHeight - dy;
                    break;

                case 1: // Top-right
                    shapeY = dragStartShapeY + dy;
                    shapeWidth = resizeStartWidth + dx;
                    shapeHeight = resizeStartHeight - dy;
                    break;

                case 2: // Bottom-right
                    shapeWidth = resizeStartWidth + dx;
                    shapeHeight = resizeStartHeight + dy;
                    break;

                case 3: // Bottom-left
                    shapeX = dragStartShapeX + dx;
                    shapeWidth = resizeStartWidth - dx;
                    shapeHeight = resizeStartHeight + dy;
                    break;
            }

            // Ensure minimum size
            if (shapeWidth < 10) {
                shapeWidth = 10;
                if (resizeHandle == 0 || resizeHandle == 3) {
                    shapeX = dragStartShapeX + resizeStartWidth - 10;
                }
            }

            if (shapeHeight < 10) {
                shapeHeight = 10;
                if (resizeHandle == 0 || resizeHandle == 1) {
                    shapeY = dragStartShapeY + resizeStartHeight - 10;
                }
            }

            updateTextFields();
            return true;
        } else if (isDragging && button == 0) {
            // Update shape position
            shapeX = dragStartShapeX + (int)(mouseX - dragStartX);
            shapeY = dragStartShapeY + (int)(mouseY - dragStartY);
            updateTextFields();
            return true;
        }

        return super.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        // Resize shape with scroll wheel
        if (isPointInShape((int)mouseX, (int)mouseY)) {
            if (GLFW.glfwGetKey(MinecraftClient.getInstance().getWindow().getHandle(), GLFW.GLFW_KEY_LEFT_SHIFT) == GLFW.GLFW_PRESS) {
                // Shift+scroll to adjust height
                shapeHeight = Math.max(10, shapeHeight + (int)(amount * 10));
            } else if (GLFW.glfwGetKey(MinecraftClient.getInstance().getWindow().getHandle(), GLFW.GLFW_KEY_LEFT_CONTROL) == GLFW.GLFW_PRESS) {
                // Ctrl+scroll to adjust corner radius or opacity
                if (GLFW.glfwGetKey(MinecraftClient.getInstance().getWindow().getHandle(), GLFW.GLFW_KEY_LEFT_ALT) == GLFW.GLFW_PRESS) {
                    // Alt+Ctrl+scroll to adjust opacity
                    int alpha = (shapeColor >> 24) & 0xFF;
                    alpha = MathHelper.clamp(alpha + (int)(amount * 10), 0, 255);
                    shapeColor = (alpha << 24) | (shapeColor & 0x00FFFFFF);

                    // Update the alpha slider
                    int index = this.children().indexOf(alphaSlider);
                    if (index >= 0) {
                        this.remove(alphaSlider);
                        alphaSlider = new SliderWidget(
                            alphaSlider.getX(), alphaSlider.getY(),
                            alphaSlider.getWidth(), alphaSlider.getHeight(),
                            Text.literal("Opacity: " + alpha), alpha / 255.0f
                        ) {
                            @Override
                            protected void updateMessage() {
                                setMessage(Text.literal("Opacity: " + ((shapeColor >> 24) & 0xFF)));
                            }

                            @Override
                            protected void applyValue() {
                                int alpha = (int) (this.value * 255.0f);
                                shapeColor = (alpha << 24) | (shapeColor & 0x00FFFFFF);
                                updateMessage();
                            }
                        };
                        this.addDrawableChild(alphaSlider);
                    }
                } else if (currentShapeType == ShapeType.ROUNDED_RECTANGLE) {
                    // Ctrl+scroll to adjust corner radius
                    cornerRadius = MathHelper.clamp(cornerRadius + (int)(amount * 2), 0, 20);

                    // Update the slider by recreating it
                    int index = this.children().indexOf(cornerRadiusSlider);
                    if (index >= 0) {
                        this.remove(cornerRadiusSlider);
                        cornerRadiusSlider = new SliderWidget(
                            cornerRadiusSlider.getX(), cornerRadiusSlider.getY(),
                            cornerRadiusSlider.getWidth(), cornerRadiusSlider.getHeight(),
                            Text.literal("Corner Radius: " + cornerRadius), cornerRadius / 20.0f
                        ) {
                            @Override
                            protected void updateMessage() {
                                setMessage(Text.literal("Corner Radius: " + cornerRadius));
                            }

                            @Override
                            protected void applyValue() {
                                cornerRadius = (int) (this.value * 20.0f);
                                updateMessage();
                            }
                        };
                        this.addDrawableChild(cornerRadiusSlider);
                    }
                }
            } else {
                // Normal scroll to adjust width
                shapeWidth = Math.max(10, shapeWidth + (int)(amount * 10));
            }
            updateTextFields();
            return true;
        }

        return super.mouseScrolled(mouseX, mouseY, amount);
    }

    /**
     * Checks if a point is inside the current shape.
     */
    private boolean isPointInShape(int x, int y) {
        switch (currentShapeType) {
            case RECTANGLE:
            case ROUNDED_RECTANGLE:
                return x >= shapeX && x <= shapeX + shapeWidth &&
                       y >= shapeY && y <= shapeY + shapeHeight;

            case CIRCLE:
                int centerX = shapeX + shapeWidth / 2;
                int centerY = shapeY + shapeHeight / 2;
                int radius = Math.min(shapeWidth, shapeHeight) / 2;
                int dx = x - centerX;
                int dy = y - centerY;
                return dx*dx + dy*dy <= radius*radius;

            case LINE:
                return x >= shapeX && x <= shapeX + shapeWidth &&
                       y >= shapeY + shapeHeight / 2 - 2 && y <= shapeY + shapeHeight / 2 + 2;

            default:
                return false;
        }
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        // Arrow keys to move shape
        int moveAmount = 1;
        if ((modifiers & GLFW.GLFW_MOD_SHIFT) != 0) {
            moveAmount = 10;
        }

        switch (keyCode) {
            case GLFW.GLFW_KEY_LEFT:
                shapeX -= moveAmount;
                updateTextFields();
                return true;

            case GLFW.GLFW_KEY_RIGHT:
                shapeX += moveAmount;
                updateTextFields();
                return true;

            case GLFW.GLFW_KEY_UP:
                shapeY -= moveAmount;
                updateTextFields();
                return true;

            case GLFW.GLFW_KEY_DOWN:
                shapeY += moveAmount;
                updateTextFields();
                return true;

            case GLFW.GLFW_KEY_ESCAPE:
                // Use the ShapeVisualizerTool to close properly and restore parent screen
                ShapeVisualizerTool.toggleShapeVisualizer();
                return true;
        }

        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    @Override
    public boolean shouldPause() {
        // Don't pause the game when this screen is open
        return false;
    }

    // Removed getColorName method
}
