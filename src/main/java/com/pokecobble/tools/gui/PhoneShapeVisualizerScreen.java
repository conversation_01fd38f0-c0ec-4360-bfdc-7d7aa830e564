package com.pokecobble.tools.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.gui.PhoneScreen;
import com.pokecobble.phone.gui.BankAppScreen;
import com.pokecobble.town.client.NotificationRenderer;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.Element;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.text.Text;
import org.lwjgl.glfw.GLFW;
import java.util.HashMap;

/**
 * A specialized version of the EnhancedShapeVisualizerScreen that is designed to work
 * with the phone interface. This screen renders on top of the phone screen without replacing it.
 * It also provides utilities for positioning UI elements relative to the phone texture.
 */
public class PhoneShapeVisualizerScreen extends EnhancedShapeVisualizerScreen {
    private final Screen parentScreen;

    // Phone dimensions - these should match the texture dimensions
    private static final int PHONE_WIDTH = 100;
    private static final int PHONE_HEIGHT = 160;

    // Store phone position for relative positioning
    private int phoneX;
    private int phoneY;

    // Map to store relative positions of UI elements
    private HashMap<String, int[]> relativePositions = new HashMap<>();

    /**
     * Creates a new PhoneShapeVisualizerScreen.
     *
     * @param parentScreen The screen that this visualizer is overlaying
     */
    public PhoneShapeVisualizerScreen(Screen parentScreen) {
        super();
        this.parentScreen = parentScreen;

        // Initialize phone position based on screen size
        initPhonePosition();
    }

    /**
     * Creates a new PhoneShapeVisualizerScreen specifically for PhoneScreen.
     * This constructor is kept for backward compatibility.
     *
     * @param phoneScreen The phone screen that this visualizer is overlaying
     */
    public PhoneShapeVisualizerScreen(PhoneScreen phoneScreen) {
        this((Screen) phoneScreen);
    }

    /**
     * Initializes the phone position based on the current screen size and parent screen.
     * This detects the actual phone position from the parent screen if possible,
     * or calculates it based on screen size as a fallback.
     */
    private void initPhonePosition() {
        // Try to get the phone position from the parent screen
        if (parentScreen instanceof PhoneScreen phoneScreen) {
            try {
                // Use reflection to access the phoneX and phoneY fields
                java.lang.reflect.Field phoneXField = PhoneScreen.class.getDeclaredField("phoneX");
                java.lang.reflect.Field phoneYField = PhoneScreen.class.getDeclaredField("phoneY");
                phoneXField.setAccessible(true);
                phoneYField.setAccessible(true);
                phoneX = (int) phoneXField.get(phoneScreen);
                phoneY = (int) phoneYField.get(phoneScreen);
                Pokecobbleclaim.LOGGER.debug("Got phone position from PhoneScreen: x=" + phoneX + ", y=" + phoneY);
                return;
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Failed to get phone position from PhoneScreen", e);
            }
        } else if (parentScreen instanceof BankAppScreen bankAppScreen) {
            try {
                // Use reflection to access the phoneX and phoneY fields
                java.lang.reflect.Field phoneXField = BankAppScreen.class.getDeclaredField("phoneX");
                java.lang.reflect.Field phoneYField = BankAppScreen.class.getDeclaredField("phoneY");
                phoneXField.setAccessible(true);
                phoneYField.setAccessible(true);
                phoneX = (int) phoneXField.get(bankAppScreen);
                phoneY = (int) phoneYField.get(bankAppScreen);
                Pokecobbleclaim.LOGGER.debug("Got phone position from BankAppScreen: x=" + phoneX + ", y=" + phoneY);
                return;
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Failed to get phone position from BankAppScreen", e);
            }
        }

        // Fallback: Calculate phone position (bottom right)
        int screenWidth = MinecraftClient.getInstance().getWindow().getScaledWidth();
        int screenHeight = MinecraftClient.getInstance().getWindow().getScaledHeight();

        phoneX = screenWidth - PHONE_WIDTH - 10;
        phoneY = screenHeight - PHONE_HEIGHT;

        Pokecobbleclaim.LOGGER.debug("Calculated phone position in shape visualizer: x=" + phoneX + ", y=" + phoneY);
    }

    @Override
    protected void init() {
        super.init();

        // Update phone position when the screen is resized
        initPhonePosition();

        // Override the copy button functionality to include phone-relative coordinates
        overrideCopyButton();

        // Add a button to toggle phone-relative coordinate display
        addPhoneRelativeButton();
    }

    // Flag to control whether to show phone-relative coordinates
    private boolean showPhoneRelativeCoordinates = true;

    /**
     * Adds a button to toggle the display of phone-relative coordinates.
     */
    private void addPhoneRelativeButton() {
        // Find the copy button to position our new button relative to it
        ButtonWidget copyButton = null;
        for (Element child : this.children()) {
            if (child instanceof ButtonWidget button && button.getMessage().getString().equals("Copy Shape Info")) {
                copyButton = button;
                break;
            }
        }

        if (copyButton != null) {
            // Add a button to toggle phone-relative coordinate display
            ButtonWidget toggleButton = ButtonWidget.builder(
                Text.literal(showPhoneRelativeCoordinates ? "Hide Relative" : "Show Relative"),
                btn -> {
                    showPhoneRelativeCoordinates = !showPhoneRelativeCoordinates;
                    btn.setMessage(Text.literal(showPhoneRelativeCoordinates ? "Hide Relative" : "Show Relative"));
                })
                .dimensions(copyButton.getX(), copyButton.getY() + copyButton.getHeight() + 4, copyButton.getWidth(), copyButton.getHeight())
                .build();

            this.addDrawableChild(toggleButton);

            // Add a note about the panel position
            Pokecobbleclaim.LOGGER.debug("Added toggle button at x=" + toggleButton.getX() + ", y=" + toggleButton.getY() +
                                       " (panel is now on the left side)");
        }
    }

    /**
     * Overrides the copy button functionality to include phone-relative coordinates.
     * This makes it easier to position UI elements relative to the phone texture.
     */
    private void overrideCopyButton() {
        // Find the copy button in the children list
        for (Element child : this.children()) {
            if (child instanceof ButtonWidget button && button.getMessage().getString().equals("Copy Shape Info")) {
                // Remove the original button
                this.remove(button);

                // Add a new button with enhanced functionality
                ButtonWidget enhancedCopyButton = ButtonWidget.builder(Text.literal("Copy Shape Info"), btn -> {
                    copyEnhancedShapeInfo();
                })
                .dimensions(button.getX(), button.getY(), button.getWidth(), button.getHeight())
                .build();

                this.addDrawableChild(enhancedCopyButton);
                break;
            }
        }
    }

    @Override
    public void renderBackground(DrawContext context) {
        // Don't render the standard dark background
        // This allows the phone screen to be visible underneath
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // First, render the parent screen underneath
        if (parentScreen != null) {
            parentScreen.render(context, mouseX, mouseY, delta);
        }

        // Draw a visual indicator of the phone boundaries
        drawPhoneBoundaries(context);

        // Then render our control panel and shape
        super.render(context, mouseX, mouseY, delta);

        // Draw phone-relative coordinates if enabled
        if (showPhoneRelativeCoordinates) {
            drawPhoneRelativeCoordinates(context);
        }
    }

    /**
     * Draws the phone-relative coordinates of the current shape.
     * This helps users see the exact position relative to the phone.
     */
    private void drawPhoneRelativeCoordinates(DrawContext context) {
        // Get the shape position
        int shapeX = getShapeX();
        int shapeY = getShapeY();

        // Calculate phone-relative coordinates
        int[] relativePos = getRelativePosition(shapeX, shapeY);
        int relativeX = relativePos[0];
        int relativeY = relativePos[1];

        // Format the coordinates
        String coordsText = String.format("Phone-Relative: x=%d, y=%d", relativeX, relativeY);

        // Determine the best position for the coordinates text
        int textWidth = this.textRenderer.getWidth(coordsText);
        int textHeight = 10; // Approximate text height
        int padding = 2;

        // Default position (above the shape)
        int textX = shapeX;
        int textY = shapeY - textHeight - padding * 2;

        // Check if the text would be too close to the left edge (where our UI panel is)
        if (textX < 180) { // Avoid the UI panel width (160) plus some margin
            // Position to the right of the shape instead
            textX = shapeX + 10;
            textY = shapeY;
        }

        // Check if the text would be off the top of the screen
        if (textY < 5) {
            // Position below the shape instead
            textY = shapeY + 10;
        }

        // Draw a background for better visibility
        context.fill(textX - padding, textY - padding,
                   textX + textWidth + padding, textY + textHeight + padding,
                   0x80000000); // Semi-transparent black background

        // Draw the text
        context.drawText(this.textRenderer, coordsText, textX, textY, 0xFFFFFFFF, false);
    }

    /**
     * Draws a visual indicator of the phone boundaries.
     * This helps users see where the phone is positioned for accurate relative positioning.
     */
    private void drawPhoneBoundaries(DrawContext context) {
        // Draw a semi-transparent outline around the phone
        int outlineColor = 0x40FFFFFF; // Semi-transparent white

        // Draw the outline slightly larger than the phone to make it visible
        int outlineThickness = 2;
        int outlineX = phoneX - outlineThickness;
        int outlineY = phoneY - outlineThickness;
        int outlineWidth = PHONE_WIDTH + (outlineThickness * 2);
        int outlineHeight = PHONE_HEIGHT + (outlineThickness * 2);

        // Draw the outline
        context.drawBorder(outlineX, outlineY, outlineWidth, outlineHeight, outlineColor);

        // Draw a label to indicate this is the phone boundary
        String label = "Phone Boundary";
        int labelWidth = this.textRenderer.getWidth(label);
        int labelX = phoneX + (PHONE_WIDTH - labelWidth) / 2;

        // Position the label below the phone if it's near the top of the screen
        // to avoid overlapping with the UI panel
        int labelY;
        if (phoneY < 100) {
            // Position below the phone
            labelY = phoneY + PHONE_HEIGHT + 4;
        } else {
            // Position above the phone
            labelY = phoneY - 12;
        }

        // Draw the label with a shadow for better visibility
        context.drawTextWithShadow(this.textRenderer, label, labelX, labelY, 0xFFFFFFFF);
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        // Handle ESC key to close the visualizer and return to the parent screen
        if (keyCode == GLFW.GLFW_KEY_ESCAPE) {
            MinecraftClient.getInstance().setScreen(parentScreen);
            ShapeVisualizerTool.setActive(false);
            return true;
        }

        // Handle V key to toggle the visualizer off
        if (keyCode == GLFW.GLFW_KEY_V) {
            MinecraftClient.getInstance().setScreen(parentScreen);
            ShapeVisualizerTool.setActive(false);
            return true;
        }

        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    @Override
    public boolean shouldPause() {
        // Don't pause the game when the visualizer is open
        return false;
    }

    /**
     * Gets the absolute position of a UI element based on its position relative to the phone.
     * This ensures that UI elements maintain their position relative to the phone
     * when the screen is resized.
     *
     * @param relativeX The X position relative to the phone's top-left corner
     * @param relativeY The Y position relative to the phone's top-left corner
     * @return An array containing [absoluteX, absoluteY]
     */
    public int[] getAbsolutePosition(int relativeX, int relativeY) {
        return ShapeVisualizerUtil.getPhoneRelativePosition(relativeX, relativeY, phoneX, phoneY);
    }

    /**
     * Converts absolute screen coordinates to coordinates relative to the phone.
     * This is useful for capturing positions that should be relative to the phone.
     *
     * @param screenX The X position on the screen
     * @param screenY The Y position on the screen
     * @return An array containing [relativeX, relativeY]
     */
    public int[] getRelativePosition(int screenX, int screenY) {
        return ShapeVisualizerUtil.getRelativeToPhonePosition(screenX, screenY, phoneX, phoneY);
    }

    /**
     * Stores a UI element's position relative to the phone.
     * This allows the position to be retrieved later and scaled appropriately.
     *
     * @param elementId A unique identifier for the UI element
     * @param relativeX The X position relative to the phone's top-left corner
     * @param relativeY The Y position relative to the phone's top-left corner
     */
    public void storeRelativePosition(String elementId, int relativeX, int relativeY) {
        relativePositions.put(elementId, new int[] { relativeX, relativeY });
        Pokecobbleclaim.LOGGER.debug("Stored relative position for " + elementId + ": x=" + relativeX + ", y=" + relativeY);
    }

    /**
     * Gets a UI element's position relative to the phone.
     *
     * @param elementId A unique identifier for the UI element
     * @return An array containing [relativeX, relativeY], or null if not found
     */
    public int[] getStoredRelativePosition(String elementId) {
        return relativePositions.get(elementId);
    }

    /**
     * Gets a UI element's absolute position based on its stored relative position.
     *
     * @param elementId A unique identifier for the UI element
     * @return An array containing [absoluteX, absoluteY], or null if not found
     */
    public int[] getStoredAbsolutePosition(String elementId) {
        int[] relativePos = relativePositions.get(elementId);
        if (relativePos != null) {
            return getAbsolutePosition(relativePos[0], relativePos[1]);
        }
        return null;
    }

    /**
     * Copies enhanced shape information to the clipboard, including phone-relative coordinates.
     * This makes it easier to position UI elements relative to the phone texture.
     */
    private void copyEnhancedShapeInfo() {
        String shapeInfo = formatEnhancedShapeInfo();
        MinecraftClient.getInstance().keyboard.setClipboard(shapeInfo);
        NotificationRenderer.addSuccessNotification("Enhanced shape info copied to clipboard!");
        Pokecobbleclaim.LOGGER.info("Copied enhanced shape info: " + shapeInfo);
    }

    /**
     * Formats the current shape information as a string, including phone-relative coordinates.
     * This makes it easier to position UI elements relative to the phone texture.
     */
    private String formatEnhancedShapeInfo() {
        // Get the shape information from the parent class
        StringBuilder sb = new StringBuilder();

        // Get the shape type, position, and size from the parent class
        ShapeType currentShapeType = getCurrentShapeType();
        int shapeX = getShapeX();
        int shapeY = getShapeY();
        int shapeWidth = getShapeWidth();
        int shapeHeight = getShapeHeight();

        // Calculate phone-relative coordinates
        int[] relativePos = getRelativePosition(shapeX, shapeY);
        int relativeX = relativePos[0];
        int relativeY = relativePos[1];

        // Get screen dimensions
        int screenWidth = MinecraftClient.getInstance().getWindow().getScaledWidth();
        int screenHeight = MinecraftClient.getInstance().getWindow().getScaledHeight();

        // Get parent screen class name
        String parentScreenName = parentScreen != null ? parentScreen.getClass().getSimpleName() : "null";

        sb.append("Shape: ").append(currentShapeType.getName()).append("\n");
        sb.append("Position: x=").append(shapeX).append(", y=").append(shapeY).append("\n");
        sb.append("Size: width=").append(shapeWidth).append(", height=").append(shapeHeight).append("\n");
        sb.append("\n");
        sb.append("Phone-Relative Position: x=").append(relativeX).append(", y=").append(relativeY).append("\n");
        sb.append("Phone Position: x=").append(phoneX).append(", y=").append(phoneY).append("\n");
        sb.append("Screen Size: ").append(screenWidth).append("x").append(screenHeight).append("\n");
        sb.append("Parent Screen: ").append(parentScreenName).append("\n");
        sb.append("\n");

        // Add code snippet for BankAppScreen
        sb.append("// To position this element relative to the phone texture, use:\n");
        sb.append("private static final int ELEMENT_RELATIVE_X = ").append(relativeX).append("; // X position relative to phone\n");
        sb.append("private static final int ELEMENT_RELATIVE_Y = ").append(relativeY).append("; // Y position relative to phone\n");
        sb.append("private static final int ELEMENT_WIDTH = ").append(shapeWidth).append("; // Width of the element\n");
        sb.append("private static final int ELEMENT_HEIGHT = ").append(shapeHeight).append("; // Height of the element\n");
        sb.append("\n");

        // Add code snippet for reference phone position
        sb.append("// Reference phone position when these coordinates were captured:\n");
        sb.append("private static final int REFERENCE_PHONE_X = ").append(phoneX).append("; // Reference phone X position\n");
        sb.append("private static final int REFERENCE_PHONE_Y = ").append(phoneY).append("; // Reference phone Y position\n");
        sb.append("\n");

        // Add code snippet for absolute position
        sb.append("// Absolute position on screen:\n");
        sb.append("private static final int ABSOLUTE_BUTTON_X = ").append(shapeX).append("; // Absolute X position on screen\n");
        sb.append("private static final int ABSOLUTE_BUTTON_Y = ").append(shapeY).append("; // Absolute Y position on screen\n");

        return sb.toString();
    }

    /**
     * Gets the current shape type from the parent class.
     */
    private ShapeType getCurrentShapeType() {
        try {
            java.lang.reflect.Field field = EnhancedShapeVisualizerScreen.class.getDeclaredField("currentShapeType");
            field.setAccessible(true);
            return (ShapeType) field.get(this);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to get current shape type", e);
            return ShapeType.RECTANGLE;
        }
    }

    /**
     * Gets the shape X position from the parent class.
     */
    private int getShapeX() {
        try {
            java.lang.reflect.Field field = EnhancedShapeVisualizerScreen.class.getDeclaredField("shapeX");
            field.setAccessible(true);
            return (int) field.get(this);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to get shape X", e);
            return 0;
        }
    }

    /**
     * Gets the shape Y position from the parent class.
     */
    private int getShapeY() {
        try {
            java.lang.reflect.Field field = EnhancedShapeVisualizerScreen.class.getDeclaredField("shapeY");
            field.setAccessible(true);
            return (int) field.get(this);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to get shape Y", e);
            return 0;
        }
    }

    /**
     * Gets the shape width from the parent class.
     */
    private int getShapeWidth() {
        try {
            java.lang.reflect.Field field = EnhancedShapeVisualizerScreen.class.getDeclaredField("shapeWidth");
            field.setAccessible(true);
            return (int) field.get(this);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to get shape width", e);
            return 0;
        }
    }

    /**
     * Gets the shape height from the parent class.
     */
    private int getShapeHeight() {
        try {
            java.lang.reflect.Field field = EnhancedShapeVisualizerScreen.class.getDeclaredField("shapeHeight");
            field.setAccessible(true);
            return (int) field.get(this);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to get shape height", e);
            return 0;
        }
    }
}
