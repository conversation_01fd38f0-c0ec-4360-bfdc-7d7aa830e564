package com.pokecobble.phone.app;

import net.minecraft.nbt.NbtCompound;

/**
 * Stores the position of an app in the phone grid.
 */
public class AppPosition {
    private String appId;
    private int gridX;
    private int gridY;
    
    /**
     * Creates a new app position.
     *
     * @param appId The app ID
     * @param gridX The grid X position
     * @param gridY The grid Y position
     */
    public AppPosition(String appId, int gridX, int gridY) {
        this.appId = appId;
        this.gridX = gridX;
        this.gridY = gridY;
    }
    
    /**
     * Gets the app ID.
     *
     * @return The app ID
     */
    public String getAppId() {
        return appId;
    }
    
    /**
     * Gets the grid X position.
     *
     * @return The grid X position
     */
    public int getGridX() {
        return gridX;
    }
    
    /**
     * Gets the grid Y position.
     *
     * @return The grid Y position
     */
    public int getGridY() {
        return gridY;
    }
    
    /**
     * Sets the grid position.
     *
     * @param gridX The grid X position
     * @param gridY The grid Y position
     */
    public void setGridPosition(int gridX, int gridY) {
        this.gridX = gridX;
        this.gridY = gridY;
    }
    
    /**
     * Saves the app position to NBT.
     *
     * @return The NBT compound
     */
    public NbtCompound toNbt() {
        NbtCompound nbt = new NbtCompound();
        nbt.putString("appId", appId);
        nbt.putInt("gridX", gridX);
        nbt.putInt("gridY", gridY);
        return nbt;
    }
    
    /**
     * Loads an app position from NBT.
     *
     * @param nbt The NBT compound
     * @return The app position
     */
    public static AppPosition fromNbt(NbtCompound nbt) {
        String appId = nbt.getString("appId");
        int gridX = nbt.getInt("gridX");
        int gridY = nbt.getInt("gridY");
        return new AppPosition(appId, gridX, gridY);
    }
}
