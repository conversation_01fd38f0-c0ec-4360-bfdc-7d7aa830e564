package com.pokecobble.phone.app;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.client.NotificationRenderer;
import net.minecraft.client.MinecraftClient;
import net.minecraft.text.Text;
import net.minecraft.util.Identifier;

/**
 * A placeholder app that doesn't do anything yet.
 */
public class PlaceholderApp extends App {
    /**
     * Creates a new placeholder app.
     *
     * @param id The app ID
     * @param name The app name
     * @param iconTexture The app icon texture
     */
    public PlaceholderApp(String id, String name, Identifier iconTexture) {
        super(id, name, iconTexture);
    }

    @Override
    public void open(MinecraftClient client) {
        Pokecobbleclaim.LOGGER.info("Opening placeholder app: " + getName());

        // Show a notification that this app is not implemented yet
        NotificationRenderer.addNotification("The " + getName() + " app is not implemented yet");

        // Close the phone screen
        client.setScreen(null);
    }
}
