package com.pokecobble.phone.app;

import net.minecraft.client.MinecraftClient;
import net.minecraft.util.Identifier;

/**
 * Represents an app on the phone.
 */
public abstract class App {
    private final String id;
    private final String name;
    private final Identifier iconTexture;

    /**
     * Creates a new app.
     *
     * @param id The app ID
     * @param name The app name
     * @param iconTexture The app icon texture
     */
    public App(String id, String name, Identifier iconTexture) {
        this.id = id;
        this.name = name;
        this.iconTexture = iconTexture;
    }

    /**
     * Gets the app ID.
     *
     * @return The app ID
     */
    public String getId() {
        return id;
    }

    /**
     * Gets the app name.
     *
     * @return The app name
     */
    public String getName() {
        return name;
    }

    /**
     * Gets the app icon texture.
     *
     * @return The app icon texture
     */
    public Identifier getIconTexture() {
        return iconTexture;
    }

    /**
     * Opens the app.
     *
     * @param client The Minecraft client
     */
    public abstract void open(MinecraftClient client);
}
