package com.pokecobble.phone.app;

import com.pokecobble.Pokecobbleclaim;
import net.minecraft.util.Identifier;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Registry for phone apps.
 */
public class AppRegistry {
    private static final AppRegistry INSTANCE = new AppRegistry();
    private final Map<String, App> apps = new HashMap<>();
    private final List<App> appList = new ArrayList<>();

    /**
     * Gets the app registry instance.
     *
     * @return The app registry instance
     */
    public static AppRegistry getInstance() {
        return INSTANCE;
    }

    /**
     * Initializes the app registry with default apps.
     */
    public void initialize() {
        Pokecobbleclaim.LOGGER.info("Initializing app registry");

        // Register the Town app
        registerApp(new TownApp());

        // Register the Bank app
        registerApp(new BankApp());

        // Register the Settings app
        registerApp(new SettingsApp());

        // Register placeholder apps that will be implemented later
        registerPlaceholderApp("shop", "Shop", "textures/phone/icon/shop.png");
        registerPlaceholderApp("trading", "Trading", "textures/phone/icon/trading.png");

        // No longer registering numbered placeholder apps (app1, app2, etc.)

        Pokecobbleclaim.LOGGER.info("Registered " + apps.size() + " apps");
    }

    /**
     * Registers a placeholder app.
     *
     * @param id The app ID
     * @param name The app name
     * @param iconPath The app icon path
     */
    private void registerPlaceholderApp(String id, String name, String iconPath) {
        Identifier iconTexture = new Identifier("pokecobbleclaim", iconPath);
        registerApp(new PlaceholderApp(id, name, iconTexture));
    }

    /**
     * Registers an app.
     *
     * @param app The app to register
     */
    public void registerApp(App app) {
        apps.put(app.getId(), app);
        appList.add(app);
    }

    /**
     * Gets an app by ID.
     *
     * @param id The app ID
     * @return The app, or null if not found
     */
    public App getApp(String id) {
        return apps.get(id);
    }

    /**
     * Gets all registered apps.
     *
     * @return A list of all registered apps
     */
    public List<App> getAllApps() {
        return appList;
    }
}
