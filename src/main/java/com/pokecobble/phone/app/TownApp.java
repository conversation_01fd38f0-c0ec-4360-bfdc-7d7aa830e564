package com.pokecobble.phone.app;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.gui.ModernTownScreen;
import net.minecraft.client.MinecraftClient;
import net.minecraft.util.Identifier;

/**
 * The Town app that opens the ModernTownScreen.
 */
public class TownApp extends App {
    private static final Identifier ICON_TEXTURE = new Identifier("pokecobbleclaim", "textures/phone/icon/town.png");

    /**
     * Creates a new Town app.
     */
    public TownApp() {
        super("town", "Town", ICON_TEXTURE);
    }

    @Override
    public void open(MinecraftClient client) {
        Pokecobbleclaim.LOGGER.info("Opening Town app");

        // Open the ModernTownScreen
        client.execute(() -> client.setScreen(new ModernTownScreen(client.currentScreen)));
    }
}
