package com.pokecobble.phone.app;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.PhoneTextureManager;
import com.pokecobble.phone.gui.BankAppScreen;
import net.minecraft.client.MinecraftClient;
import net.minecraft.util.Identifier;

/**
 * The Bank app that changes the phone interface to the bank app interface.
 */
public class BankApp extends App {
    private static final Identifier ICON_TEXTURE = new Identifier("pokecobbleclaim", "textures/phone/icon/bank.png");

    /**
     * Creates a new Bank app.
     */
    public BankApp() {
        super("bank", "Bank", ICON_TEXTURE);
    }

    @Override
    public void open(MinecraftClient client) {
        Pokecobbleclaim.LOGGER.debug("Opening Bank app"); // Changed to debug level to reduce console spam

        // Change the phone texture to the bank app interface
        PhoneTextureManager.getInstance().openBankApp();

        // Open the BankAppScreen directly without any intermediate screens
        client.setScreen(new BankAppScreen());
    }
}
