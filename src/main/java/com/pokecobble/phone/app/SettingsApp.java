package com.pokecobble.phone.app;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.PhoneTextureManager;
import com.pokecobble.phone.gui.SettingsAppScreen;
import net.minecraft.client.MinecraftClient;
import net.minecraft.util.Identifier;

/**
 * The Settings app that changes the phone interface to the settings app interface.
 */
public class SettingsApp extends App {
    private static final Identifier ICON_TEXTURE = new Identifier("pokecobbleclaim", "textures/phone/icon/settings.png");

    /**
     * Creates a new Settings app.
     */
    public SettingsApp() {
        super("settings", "Settings", ICON_TEXTURE);
    }

    @Override
    public void open(MinecraftClient client) {
        Pokecobbleclaim.LOGGER.debug("Opening Settings app");

        // Change the phone texture to the settings app interface
        PhoneTextureManager.getInstance().openSettingsApp();

        // Open the SettingsAppScreen directly without any intermediate screens
        client.setScreen(new SettingsAppScreen());
    }
}
