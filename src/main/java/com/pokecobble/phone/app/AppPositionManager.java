package com.pokecobble.phone.app;

import com.pokecobble.Pokecobbleclaim;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayConnectionEvents;
import net.minecraft.client.MinecraftClient;
import net.minecraft.nbt.NbtCompound;
import net.minecraft.nbt.NbtElement;
import net.minecraft.nbt.NbtIo;
import net.minecraft.nbt.NbtList;
import net.minecraft.util.WorldSavePath;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * Manages app positions and persistence.
 */
public class AppPositionManager {
    private static final AppPositionManager INSTANCE = new AppPositionManager();

    // Map of app ID to position
    private final Map<String, AppPosition> appPositions = new HashMap<>();

    // Default positions for apps
    private final Map<String, AppPosition> defaultPositions = new HashMap<>();

    // Flag to track if positions have been loaded
    private boolean positionsLoaded = false;

    /**
     * Gets the app position manager instance.
     *
     * @return The app position manager instance
     */
    public static AppPositionManager getInstance() {
        return INSTANCE;
    }

    /**
     * Initializes the app position manager.
     */
    public void initialize() {
        Pokecobbleclaim.LOGGER.info("Initializing app position manager");

        // Set up default positions
        setupDefaultPositions();

        // Register connection events to load/save positions
        registerConnectionEvents();
    }

    /**
     * Sets up default positions for apps.
     */
    private void setupDefaultPositions() {
        Pokecobbleclaim.LOGGER.info("Setting up default app positions");

        // Real apps
        defaultPositions.put("town", new AppPosition("town", 0, 0));
        defaultPositions.put("bank", new AppPosition("bank", 1, 0));
        defaultPositions.put("shop", new AppPosition("shop", 2, 0));
        defaultPositions.put("trading", new AppPosition("trading", 3, 0));
        defaultPositions.put("settings", new AppPosition("settings", 0, 1));

        // We still need to maintain positions for placeholder apps even though we don't display them
        // This ensures the drag and drop functionality works correctly
        int appIndex = 1;

        // Start from row 0, col 0 and fill the entire grid
        for (int row = 0; row < 6; row++) {
            for (int col = 0; col < 4; col++) {
                // Skip positions already assigned to real apps
                if ((row == 0 && (col == 0 || col == 1 || col == 2 || col == 3)) ||
                    (row == 1 && col == 0)) {
                    continue;
                }

                String appId = "app" + appIndex;
                defaultPositions.put(appId, new AppPosition(appId, col, row));
                appIndex++;
            }
        }

        Pokecobbleclaim.LOGGER.info("Initialized " + defaultPositions.size() + " default app positions");
    }

    /**
     * Registers connection events to load/save positions.
     */
    private void registerConnectionEvents() {
        // Load positions when joining a world
        ClientPlayConnectionEvents.JOIN.register((handler, sender, client) -> {
            loadPositions();
        });

        // Save positions when leaving a world
        ClientPlayConnectionEvents.DISCONNECT.register((handler, client) -> {
            savePositions();
        });
    }

    /**
     * Gets the position of an app.
     *
     * @param appId The app ID
     * @return The app position
     */
    public AppPosition getAppPosition(String appId) {
        // Load positions if not loaded yet
        if (!positionsLoaded) {
            loadPositions();
        }

        // Get the position from the positions map or default positions
        AppPosition position;
        if (appPositions.containsKey(appId)) {
            position = appPositions.get(appId);
        } else if (defaultPositions.containsKey(appId)) {
            position = defaultPositions.get(appId);
        } else {
            position = new AppPosition(appId, 0, 0);
        }

        return position;
    }

    /**
     * Sets the position of an app.
     *
     * @param appId The app ID
     * @param gridX The grid X position
     * @param gridY The grid Y position
     */
    public void setAppPosition(String appId, int gridX, int gridY) {
        // Ensure positions are loaded before modifying
        if (!positionsLoaded) {
            loadPositions();
        }

        try {
            // Create or update the position
            AppPosition position = appPositions.get(appId);
            if (position == null) {
                position = new AppPosition(appId, gridX, gridY);
                appPositions.put(appId, position);
            } else {
                position.setGridPosition(gridX, gridY);
            }

            // Save positions when an app is moved
            savePositions();
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error setting app position: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Loads app positions from disk.
     */
    public void loadPositions() {
        try {
            // Get the player's UUID
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player == null) {
                Pokecobbleclaim.LOGGER.info("Player is null, using default positions");
                // Use default positions if player is null
                initializeWithDefaultPositions();
                return;
            }

            UUID playerId = client.player.getUuid();
            Pokecobbleclaim.LOGGER.info("Loading positions for player: " + playerId);

            // Get the save directory
            File saveDir = getSaveDirectory();
            if (saveDir == null) {
                Pokecobbleclaim.LOGGER.info("Save directory is null, using default positions");
                // Use default positions if save directory is null
                initializeWithDefaultPositions();
                return;
            }

            // Create the phone directory if it doesn't exist
            File phoneDir = new File(saveDir, "phone");
            if (!phoneDir.exists()) {
                phoneDir.mkdirs();
                Pokecobbleclaim.LOGGER.info("Created phone directory: " + phoneDir.getAbsolutePath());
            }

            // Get the player's app positions file
            File positionsFile = new File(phoneDir, playerId + ".dat");
            if (!positionsFile.exists()) {
                Pokecobbleclaim.LOGGER.info("Positions file doesn't exist, using default positions");
                // Use default positions if the file doesn't exist
                initializeWithDefaultPositions();
                return;
            }

            // Load the positions from the file
            try {
                NbtCompound nbt = NbtIo.read(positionsFile);
                if (nbt != null) {
                    // Start with default positions
                    appPositions.clear();
                    appPositions.putAll(defaultPositions);
                    Pokecobbleclaim.LOGGER.info("Initialized with default positions: " + defaultPositions.size() + " entries");

                    // Initialize with default positions

                    // Load positions from NBT, overriding defaults
                    NbtList positionsList = nbt.getList("positions", NbtElement.COMPOUND_TYPE);
                    Pokecobbleclaim.LOGGER.info("Found " + positionsList.size() + " saved positions in NBT");

                    // Track which apps were loaded from NBT
                    Set<String> loadedAppIds = new HashSet<>();

                    for (int i = 0; i < positionsList.size(); i++) {
                        NbtCompound positionNbt = positionsList.getCompound(i);
                        AppPosition position = AppPosition.fromNbt(positionNbt);
                        String appId = position.getAppId();
                        int gridX = position.getGridX();
                        int gridY = position.getGridY();

                        // Store the position
                        appPositions.put(appId, position);
                        loadedAppIds.add(appId);
                        // Position loaded from NBT
                    }

                    // Apps not found in NBT will use their default positions
                } else {
                    Pokecobbleclaim.LOGGER.info("NBT is null, using default positions");
                    // Use default positions if NBT is null
                    initializeWithDefaultPositions();
                }

                positionsLoaded = true;
                Pokecobbleclaim.LOGGER.info("Loaded " + appPositions.size() + " app positions for player " + playerId);
            } catch (IOException e) {
                Pokecobbleclaim.LOGGER.error("Failed to load app positions: " + e.getMessage());
                e.printStackTrace();

                // Use default positions if loading fails
                initializeWithDefaultPositions();
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to load app positions: " + e.getMessage());
            e.printStackTrace();

            // Use default positions if loading fails
            initializeWithDefaultPositions();
        }
    }

    /**
     * Initializes the positions map with default positions.
     */
    private void initializeWithDefaultPositions() {
        appPositions.clear();
        appPositions.putAll(defaultPositions);
        positionsLoaded = true;
        Pokecobbleclaim.LOGGER.info("Initialized with default positions");

        // Default positions initialized
    }

    /**
     * Saves app positions to disk.
     */
    public void savePositions() {
        try {
            // Get the player's UUID
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player == null) {
                Pokecobbleclaim.LOGGER.info("Player is null, cannot save positions");
                return;
            }

            UUID playerId = client.player.getUuid();
            Pokecobbleclaim.LOGGER.info("Saving positions for player: " + playerId);

            // Get the save directory
            File saveDir = getSaveDirectory();
            if (saveDir == null) {
                Pokecobbleclaim.LOGGER.info("Save directory is null, cannot save positions");
                return;
            }

            // Create the phone directory if it doesn't exist
            File phoneDir = new File(saveDir, "phone");
            if (!phoneDir.exists()) {
                phoneDir.mkdirs();
                Pokecobbleclaim.LOGGER.info("Created phone directory: " + phoneDir.getAbsolutePath());
            }

            // Create the NBT data
            NbtCompound nbt = new NbtCompound();
            NbtList positionsList = new NbtList();

            // Log the number of positions being saved
            Pokecobbleclaim.LOGGER.info("Saving " + appPositions.size() + " app positions");

            // Add all positions to the list
            for (Map.Entry<String, AppPosition> entry : appPositions.entrySet()) {
                String appId = entry.getKey();
                AppPosition position = entry.getValue();
                int gridX = position.getGridX();
                int gridY = position.getGridY();

                NbtCompound posNbt = position.toNbt();
                positionsList.add(posNbt);
                // Saving position to NBT
            }

            nbt.put("positions", positionsList);
            Pokecobbleclaim.LOGGER.info("Added " + positionsList.size() + " positions to NBT data");

            // Save the NBT data to the file
            File positionsFile = new File(phoneDir, playerId + ".dat");
            try {
                NbtIo.write(nbt, positionsFile);
                Pokecobbleclaim.LOGGER.info("Saved " + appPositions.size() + " app positions to file: " + positionsFile.getAbsolutePath());

                // Verify the file was created
                if (!positionsFile.exists()) {
                    Pokecobbleclaim.LOGGER.error("Failed to verify positions file exists: " + positionsFile.getAbsolutePath());
                }
            } catch (IOException e) {
                Pokecobbleclaim.LOGGER.error("Failed to save app positions: " + e.getMessage());
                e.printStackTrace();
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to save app positions: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Gets the save directory.
     *
     * @return The save directory
     */
    private File getSaveDirectory() {
        MinecraftClient client = MinecraftClient.getInstance();

        // Check if we're in a singleplayer world
        if (client.isIntegratedServerRunning() && client.getServer() != null) {
            // Get the world save directory
            try {
                Path savePath = client.getServer().getSavePath(WorldSavePath.ROOT);
                File saveDir = savePath.toFile();

                // Create the mod directory if it doesn't exist
                File modDir = new File(saveDir, "pokecobbleclaim");
                if (!modDir.exists()) {
                    modDir.mkdirs();
                }

                return modDir;
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Failed to get save directory: " + e.getMessage());
                e.printStackTrace();
            }
        } else {
            // For multiplayer, use the client config directory
            try {
                File configDir = new File("config/pokecobbleclaim");
                if (!configDir.exists()) {
                    configDir.mkdirs();
                }

                return configDir;
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Failed to get config directory: " + e.getMessage());
                e.printStackTrace();
            }
        }

        return null;
    }

    /**
     * Resets app positions to defaults.
     */
    public void resetPositions() {
        appPositions.clear();
        appPositions.putAll(defaultPositions);
        savePositions();
    }

    /**
     * Checks if a grid position is occupied.
     *
     * @param gridX The grid X position
     * @param gridY The grid Y position
     * @param excludeAppId The app ID to exclude from the check
     * @return True if the position is occupied, false otherwise
     */
    public boolean isPositionOccupied(int gridX, int gridY, String excludeAppId) {
        // Ensure positions are loaded before checking
        if (!positionsLoaded) {
            loadPositions();
        }

        // Check if position is occupied by any app except the excluded one

        // Check if any app (except the excluded one) is at this position
        for (AppPosition position : appPositions.values()) {
            String appId = position.getAppId();
            int posX = position.getGridX();
            int posY = position.getGridY();

            if (!appId.equals(excludeAppId) && posX == gridX && posY == gridY) {
                return true;
            }
        }
        return false;
    }

    /**
     * Finds the nearest available grid position.
     *
     * @param gridX The target grid X position
     * @param gridY The target grid Y position
     * @param excludeAppId The app ID to exclude from the check
     * @param maxCols The maximum number of columns
     * @param maxRows The maximum number of rows
     * @return The nearest available grid position as an int array [x, y]
     */
    public int[] findNearestAvailablePosition(int gridX, int gridY, String excludeAppId, int maxCols, int maxRows) {
        // Ensure positions are loaded before checking
        if (!positionsLoaded) {
            loadPositions();
        }

        // Check if the target position is available
        if (!isPositionOccupied(gridX, gridY, excludeAppId) &&
            gridX >= 0 && gridX < maxCols && gridY >= 0 && gridY < maxRows) {
            return new int[] { gridX, gridY };
        }

        // Search in expanding rings around the target position
        for (int radius = 1; radius < Math.max(maxCols, maxRows); radius++) {

            // Check positions in a square around the target
            for (int dx = -radius; dx <= radius; dx++) {
                for (int dy = -radius; dy <= radius; dy++) {
                    // Skip positions that aren't on the perimeter of the square
                    if (Math.abs(dx) != radius && Math.abs(dy) != radius) {
                        continue;
                    }

                    int x = gridX + dx;
                    int y = gridY + dy;

                    // Check if the position is valid and available
                    if (x >= 0 && x < maxCols && y >= 0 && y < maxRows) {
                        boolean occupied = isPositionOccupied(x, y, excludeAppId);

                        if (!occupied) {
                            return new int[] { x, y };
                        }
                    }
                }
            }
        }

        // If no position is found, return the original position
        return new int[] { gridX, gridY };
    }
}
