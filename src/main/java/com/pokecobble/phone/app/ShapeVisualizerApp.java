package com.pokecobble.phone.app;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.tools.gui.ShapeVisualizerTool;
import net.minecraft.client.MinecraftClient;
import net.minecraft.util.Identifier;

/**
 * The Shape Visualizer app that opens the shape visualizer tool on top of the phone screen.
 */
public class ShapeVisualizerApp extends App {
    private static final Identifier ICON_TEXTURE = new Identifier("pokecobbleclaim", "textures/phone/icon/design.png");

    /**
     * Creates a new Shape Visualizer app.
     */
    public ShapeVisualizerApp() {
        super("shape_visualizer", "UI Designer", ICON_TEXTURE);
    }

    @Override
    public void open(MinecraftClient client) {
        Pokecobbleclaim.LOGGER.info("Opening Shape Visualizer app");

        // Open the shape visualizer on top of the phone screen
        ShapeVisualizerTool.openOnPhone();
    }
}
