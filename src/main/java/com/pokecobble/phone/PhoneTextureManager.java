package com.pokecobble.phone;

import com.pokecobble.Pokecobbleclaim;
import net.minecraft.util.Identifier;

/**
 * Manages the phone textures.
 */
public class PhoneTextureManager {
    private static final PhoneTextureManager INSTANCE = new PhoneTextureManager();

    // Default phone texture
    private static final Identifier DEFAULT_PHONE_TEXTURE = new Identifier("pokecobbleclaim", "textures/phone/textures/phonetexture.png");

    // Bank app textures
    public static final Identifier BANK_APP_HOME_TEXTURE = new Identifier("pokecobbleclaim", "textures/phone/bankapp/bankapp_eng/homemenu.png");
    public static final Identifier BANK_APP_TRANSFER_TEXTURE = new Identifier("pokecobbleclaim", "textures/phone/bankapp/bankapp_eng/transfermoney.png");

    // Bank app result textures
    public static final Identifier BANK_APP_FINISH_PAYMENT_TEXTURE = new Identifier("pokecobbleclaim", "textures/phone/bankapp/bankapp_eng/finishpayment.png");
    public static final Identifier BANK_APP_TRANSFER_FAILED_TEXTURE = new Identifier("pokecobbleclaim", "textures/phone/bankapp/bankapp_eng/transferfailed.png");
    public static final Identifier BANK_APP_ERROR_TEXTURE = new Identifier("pokecobbleclaim", "textures/phone/bankapp/bankapp_eng/error.png");

    // Settings app texture
    public static final Identifier SETTINGS_APP_TEXTURE = new Identifier("pokecobbleclaim", "textures/phone/settingapp/settings.png");

    // Settings toggle switch textures
    public static final Identifier SETTINGS_TOGGLE_ON_TEXTURE = new Identifier("pokecobbleclaim", "textures/phone/settingapp/toggle_on.png");
    public static final Identifier SETTINGS_TOGGLE_OFF_TEXTURE = new Identifier("pokecobbleclaim", "textures/phone/settingapp/toggle_off.png");

    // Notification screen texture
    public static final Identifier NOTIFICATION_SCREEN_TEXTURE = new Identifier("pokecobbleclaim", "textures/phone/textures/notif.png");

    // Exit bar texture
    public static final Identifier EXIT_BAR_TEXTURE = new Identifier("pokecobbleclaim", "textures/phone/exitappbar/exitbar.png");

    // Current texture
    private Identifier currentTexture = DEFAULT_PHONE_TEXTURE;

    // App state
    private boolean inAppMode = false;
    private String currentAppId = null;

    /**
     * Gets the phone texture manager instance.
     *
     * @return The phone texture manager instance
     */
    public static PhoneTextureManager getInstance() {
        return INSTANCE;
    }

    /**
     * Gets the current phone texture.
     *
     * @return The current phone texture
     */
    public Identifier getCurrentTexture() {
        // Make sure we never return null
        if (currentTexture == null) {
            Pokecobbleclaim.LOGGER.error("Current texture is null, using default texture");
            currentTexture = DEFAULT_PHONE_TEXTURE;
        }
        return currentTexture;
    }

    /**
     * Sets the phone to display the bank app.
     */
    public void openBankApp() {
        Pokecobbleclaim.LOGGER.debug("Opening bank app interface"); // Changed to debug level to reduce console spam
        currentTexture = BANK_APP_HOME_TEXTURE;
        inAppMode = true;
        currentAppId = "bank";
    }

    /**
     * Sets the phone to display the bank transfer page.
     */
    public void openBankTransferPage() {
        Pokecobbleclaim.LOGGER.debug("Opening bank transfer page"); // Changed to debug level to reduce console spam
        currentTexture = BANK_APP_TRANSFER_TEXTURE;
        inAppMode = true;
        currentAppId = "bank";
    }

    /**
     * Sets the phone to display the transfer success page.
     */
    public void openBankTransferSuccessPage() {
        Pokecobbleclaim.LOGGER.debug("Opening bank transfer success page");
        currentTexture = BANK_APP_FINISH_PAYMENT_TEXTURE;
        inAppMode = true;
        currentAppId = "bank";
    }

    /**
     * Sets the phone to display the transfer failed page.
     */
    public void openBankTransferFailedPage() {
        Pokecobbleclaim.LOGGER.debug("Opening bank transfer failed page");
        currentTexture = BANK_APP_TRANSFER_FAILED_TEXTURE;
        inAppMode = true;
        currentAppId = "bank";
    }

    /**
     * Sets the phone to display the transfer error page.
     */
    public void openBankTransferErrorPage() {
        Pokecobbleclaim.LOGGER.debug("Opening bank transfer error page");
        currentTexture = BANK_APP_ERROR_TEXTURE;
        inAppMode = true;
        currentAppId = "bank";
    }

    /**
     * Sets the phone to display the settings app.
     */
    public void openSettingsApp() {
        Pokecobbleclaim.LOGGER.debug("Opening settings app interface");
        currentTexture = SETTINGS_APP_TEXTURE;
        inAppMode = true;
        currentAppId = "settings";
    }

    /**
     * Sets the phone to display the notification screen.
     */
    public void openNotificationScreen() {
        Pokecobbleclaim.LOGGER.debug("Opening notification screen interface");
        currentTexture = NOTIFICATION_SCREEN_TEXTURE;
        inAppMode = true;
        currentAppId = "notification";
    }

    /**
     * Resets the phone to the default texture.
     */
    public void resetToDefaultTexture() {
        Pokecobbleclaim.LOGGER.debug("Resetting phone to default texture"); // Changed to debug level to reduce console spam
        currentTexture = DEFAULT_PHONE_TEXTURE;
        inAppMode = false;
        currentAppId = null;
    }

    /**
     * Checks if the phone is in app mode.
     *
     * @return true if the phone is in app mode, false otherwise
     */
    public boolean isInAppMode() {
        return inAppMode;
    }

    /**
     * Gets the current app ID.
     *
     * @return The current app ID, or null if not in app mode
     */
    public String getCurrentAppId() {
        return currentAppId;
    }
}
