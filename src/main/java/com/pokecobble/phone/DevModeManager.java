package com.pokecobble.phone;

import com.pokecobble.Pokecobbleclaim;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

/**
 * Manages the developer mode state for the phone system.
 * Dev mode enables additional debugging features and visualizations.
 */
public class DevModeManager {
    private static final DevModeManager INSTANCE = new DevModeManager();
    
    // Set of player UUIDs who have dev mode enabled
    private final Set<UUID> devModeEnabled = new HashSet<>();
    
    // Whether global dev mode is enabled (affects all players)
    private boolean globalDevMode = false;
    
    private DevModeManager() {
        // Private constructor for singleton
    }
    
    /**
     * Gets the singleton instance of the DevModeManager.
     * 
     * @return The DevModeManager instance
     */
    public static DevModeManager getInstance() {
        return INSTANCE;
    }
    
    /**
     * Toggles dev mode for a specific player.
     * 
     * @param player The player to toggle dev mode for
     * @return True if dev mode is now enabled, false if it's now disabled
     */
    public boolean toggleDevMode(PlayerEntity player) {
        UUID playerId = player.getUuid();
        
        if (devModeEnabled.contains(playerId)) {
            devModeEnabled.remove(playerId);
            Pokecobbleclaim.LOGGER.info("Dev mode disabled for player: " + player.getName().getString());
            return false;
        } else {
            devModeEnabled.add(playerId);
            Pokecobbleclaim.LOGGER.info("Dev mode enabled for player: " + player.getName().getString());
            return true;
        }
    }
    
    /**
     * Toggles global dev mode (affects all players).
     * 
     * @return True if global dev mode is now enabled, false if it's now disabled
     */
    public boolean toggleGlobalDevMode() {
        globalDevMode = !globalDevMode;
        Pokecobbleclaim.LOGGER.info("Global dev mode " + (globalDevMode ? "enabled" : "disabled"));
        return globalDevMode;
    }
    
    /**
     * Checks if dev mode is enabled for a specific player.
     * 
     * @param player The player to check
     * @return True if dev mode is enabled for the player or globally
     */
    public boolean isDevModeEnabled(PlayerEntity player) {
        return globalDevMode || devModeEnabled.contains(player.getUuid());
    }
    
    /**
     * Checks if a player is allowed to use dev mode.
     * Currently only operators are allowed to use dev mode.
     * 
     * @param player The player to check
     * @return True if the player is allowed to use dev mode
     */
    public boolean canUseDevMode(PlayerEntity player) {
        if (player instanceof ServerPlayerEntity serverPlayer) {
            // Check if the player is an operator
            return serverPlayer.hasPermissionLevel(2); // Level 2 is operator
        }
        return false;
    }
    
    /**
     * Handles the /phone devmode command.
     * 
     * @param player The player who executed the command
     * @return True if the command was handled successfully
     */
    public boolean handleDevModeCommand(PlayerEntity player) {
        if (!canUseDevMode(player)) {
            player.sendMessage(Text.literal("§cYou don't have permission to use dev mode."), false);
            return false;
        }
        
        boolean enabled = toggleDevMode(player);
        player.sendMessage(Text.literal("§6Phone dev mode " + (enabled ? "§aenabled" : "§cdisabled") + "§6."), false);
        return true;
    }
}
