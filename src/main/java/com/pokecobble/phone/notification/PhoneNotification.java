package com.pokecobble.phone.notification;

/**
 * Represents a phone notification.
 */
public class PhoneNotification {
    
    /**
     * Types of phone notifications.
     */
    public enum Type {
        TOWN_INVITE(0xFF4CAF50),    // Green
        INFO(0xFF2196F3),           // Blue  
        WARNING(0xFFFF9800),        // Orange
        ERROR(0xFFF44336),          // Red
        SUCCESS(0xFF8BC34A);        // Light green
        
        private final int color;
        
        Type(int color) {
            this.color = color;
        }
        
        public int getColor() {
            return color;
        }
    }
    
    // Notification properties
    private final String title;
    private final String message;
    private final Type type;
    private final int maxDurationTicks;
    
    // Animation properties
    private int remainingTicks;
    private long creationTime;
    
    // Town invite specific data
    private String townName;
    private String townId;
    
    // Animation constants
    private static final int FADE_IN_TICKS = 10;
    private static final int FADE_OUT_TICKS = 20;
    
    /**
     * Creates a new phone notification.
     *
     * @param title The notification title
     * @param message The notification message
     * @param type The notification type
     * @param durationTicks How long to display the notification
     */
    public PhoneNotification(String title, String message, Type type, int durationTicks) {
        this.title = title;
        this.message = message;
        this.type = type;
        this.maxDurationTicks = durationTicks;
        this.remainingTicks = durationTicks;
        this.creationTime = System.currentTimeMillis();
    }
    
    /**
     * Updates the notification (called each tick).
     */
    public void update() {
        if (remainingTicks > 0) {
            remainingTicks--;
        }
    }
    
    /**
     * Checks if the notification has expired.
     */
    public boolean isExpired() {
        return remainingTicks <= 0;
    }
    
    /**
     * Gets the notification title.
     */
    public String getTitle() {
        return title;
    }
    
    /**
     * Gets the notification message.
     */
    public String getMessage() {
        return message;
    }
    
    /**
     * Gets the notification type.
     */
    public Type getType() {
        return type;
    }
    
    /**
     * Gets the remaining ticks.
     */
    public int getRemainingTicks() {
        return remainingTicks;
    }
    
    /**
     * Gets the maximum duration in ticks.
     */
    public int getMaxDurationTicks() {
        return maxDurationTicks;
    }
    
    /**
     * Gets the creation time.
     */
    public long getCreationTime() {
        return creationTime;
    }
    
    /**
     * Calculates the alpha value for fade in/out animation.
     */
    public float getAlpha() {
        int elapsedTicks = maxDurationTicks - remainingTicks;
        
        if (elapsedTicks < FADE_IN_TICKS) {
            // Fade in
            return (float) elapsedTicks / FADE_IN_TICKS;
        } else if (remainingTicks < FADE_OUT_TICKS) {
            // Fade out
            return (float) remainingTicks / FADE_OUT_TICKS;
        } else {
            // Fully visible
            return 1.0f;
        }
    }
    
    /**
     * Gets the notification color with alpha applied.
     */
    public int getColorWithAlpha() {
        float alpha = getAlpha();
        int alphaInt = Math.max(0, Math.min(255, (int)(alpha * 255)));
        
        // Remove existing alpha and apply new alpha
        int color = type.getColor() & 0x00FFFFFF;
        return (alphaInt << 24) | color;
    }
    
    /**
     * Sets the town name (for town invite notifications).
     */
    public void setTownName(String townName) {
        this.townName = townName;
    }
    
    /**
     * Gets the town name.
     */
    public String getTownName() {
        return townName;
    }
    
    /**
     * Sets the town ID (for town invite notifications).
     */
    public void setTownId(String townId) {
        this.townId = townId;
    }
    
    /**
     * Gets the town ID.
     */
    public String getTownId() {
        return townId;
    }
    
    /**
     * Checks if this is a town invite notification.
     */
    public boolean isTownInvite() {
        return type == Type.TOWN_INVITE;
    }
    
    /**
     * Forces the notification to expire immediately.
     */
    public void expire() {
        remainingTicks = 0;
    }
    
    /**
     * Extends the notification duration.
     */
    public void extend(int additionalTicks) {
        remainingTicks += additionalTicks;
    }
}
