package com.pokecobble.phone.notification;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.gui.PhoneScreen;
import com.pokecobble.phone.gui.NotificationScreen;
import com.pokecobble.phone.gui.PhoneRenderer;
import com.pokecobble.phone.PhoneTextureManager;
import com.pokecobble.town.sound.SoundRegistry;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.sound.PositionedSoundInstance;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Manages phone notifications that appear on top of the phone.
 */
public class PhoneNotificationManager {
    private static final PhoneNotificationManager INSTANCE = new PhoneNotificationManager();

    // Notification settings
    private static final int DEFAULT_DURATION_TICKS = 100; // 5 seconds
    private static final int FADE_IN_TICKS = 10;
    private static final int FADE_OUT_TICKS = 20;

    // Active notifications
    private final List<PhoneNotification> notifications = new ArrayList<>();

    // Auto-open phone setting
    private boolean autoOpenPhone = true;

    /**
     * Gets the phone notification manager instance.
     */
    public static PhoneNotificationManager getInstance() {
        return INSTANCE;
    }

    /**
     * Adds a town invite notification.
     *
     * @param townName The name of the town
     * @param townId The ID of the town (for accepting/declining)
     */
    public void addTownInviteNotification(String townName, String townId) {
        // Create notification
        PhoneNotification notification = new PhoneNotification(
            "Town Invite",
            "You've been invited to join " + townName,
            PhoneNotification.Type.TOWN_INVITE,
            DEFAULT_DURATION_TICKS
        );

        // Set town-specific data
        notification.setTownName(townName);
        notification.setTownId(townId);

        // Add to notifications list
        notifications.add(notification);

        // Play notification sound
        playNotificationSound();

        // Auto-open phone if enabled (this will open the notification screen)
        if (autoOpenPhone) {
            openPhone();
        }

        Pokecobbleclaim.LOGGER.info("Added town invite notification for: " + townName + " - showed phone with overlay");
    }

    /**
     * Adds a general notification.
     *
     * @param title The notification title
     * @param message The notification message
     * @param type The notification type
     */
    public void addNotification(String title, String message, PhoneNotification.Type type) {
        PhoneNotification notification = new PhoneNotification(title, message, type, DEFAULT_DURATION_TICKS);
        notifications.add(notification);

        // Play sound for important notifications
        if (type == PhoneNotification.Type.TOWN_INVITE || type == PhoneNotification.Type.ERROR) {
            playNotificationSound();
        }

        Pokecobbleclaim.LOGGER.debug("Added phone notification: " + title);
    }

    /**
     * Updates all notifications, removing expired ones.
     */
    public void update() {
        Iterator<PhoneNotification> iterator = notifications.iterator();
        while (iterator.hasNext()) {
            PhoneNotification notification = iterator.next();
            notification.update();

            if (notification.isExpired()) {
                iterator.remove();
            }
        }
    }

    /**
     * Gets all active notifications.
     */
    public List<PhoneNotification> getNotifications() {
        return new ArrayList<>(notifications);
    }

    /**
     * Checks if there are any active notifications.
     */
    public boolean hasNotifications() {
        return !notifications.isEmpty();
    }

    /**
     * Checks if there are any active notifications (alias for hasNotifications).
     */
    public boolean hasActiveNotifications() {
        return hasNotifications();
    }

    /**
     * Removes a specific notification.
     */
    public void removeNotification(PhoneNotification notification) {
        notifications.remove(notification);
    }

    /**
     * Clears all notifications.
     */
    public void clearAllNotifications() {
        notifications.clear();
    }

    /**
     * Plays the notification sound.
     */
    private void playNotificationSound() {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client != null && client.getSoundManager() != null) {
            try {
                client.getSoundManager().play(
                    PositionedSoundInstance.master(SoundRegistry.INVITE_NOTIFICATION, 1.0F, 1.0F)
                );
                Pokecobbleclaim.LOGGER.debug("Playing phone notification sound");
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error playing notification sound: " + e.getMessage());
            }
        }
    }

    /**
     * Shows the phone with notification overlay if no screen is open.
     */
    private void openPhone() {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client != null) {
            client.execute(() -> {
                // Only show phone overlay if no screen is currently open
                if (client.currentScreen == null) {
                    Pokecobbleclaim.LOGGER.info("Showing phone with notification overlay for notification");
                    // Make phone visible
                    PhoneRenderer.getInstance().setVisible(true);
                    // Show notification overlay
                    PhoneNotificationOverlay.getInstance().showOverlay();
                    Pokecobbleclaim.LOGGER.info("Phone with notification overlay shown successfully");
                } else {
                    Pokecobbleclaim.LOGGER.info("Screen is open, not showing phone overlay");
                }
            });
        }
    }

    /**
     * Sets whether the phone should auto-open when notifications are received.
     */
    public void setAutoOpenPhone(boolean autoOpen) {
        this.autoOpenPhone = autoOpen;
    }

    /**
     * Gets whether the phone auto-opens for notifications.
     */
    public boolean isAutoOpenPhone() {
        return autoOpenPhone;
    }
}
