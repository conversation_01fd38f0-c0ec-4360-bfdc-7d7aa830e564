package com.pokecobble.phone.notification;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.PhoneTextureManager;
import com.pokecobble.phone.gui.PhoneRenderer;
import com.pokecobble.phone.gui.PhoneScreen;
import net.fabricmc.fabric.api.client.rendering.v1.HudRenderCallback;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.util.Identifier;

import java.util.List;

/**
 * Renders phone notifications on top of the phone.
 */
public class PhoneNotificationRenderer {

    // Phone dimensions (should match phone texture)
    private static final int PHONE_WIDTH = 100;
    private static final int PHONE_HEIGHT = 160;

    // Notification dimensions and positioning
    private static final int NOTIFICATION_WIDTH = 80;
    private static final int NOTIFICATION_HEIGHT = 30;
    private static final int NOTIFICATION_MARGIN = 5;
    private static final int NOTIFICATION_PADDING = 4;

    // Notification icon texture
    private static final Identifier NOTIFICATION_ICON_TEXTURE = new Identifier("pokecobbleclaim", "textures/phone/textures/notif.png");

    /**
     * Registers the phone notification renderer.
     */
    public static void register() {
        HudRenderCallback.EVENT.register((context, tickDelta) -> {
            // Update notifications
            PhoneNotificationManager.getInstance().update();

            // Render notifications
            renderNotifications(context);
        });

        Pokecobbleclaim.LOGGER.info("Registered phone notification renderer");
    }

    /**
     * Renders all active phone notifications.
     * Note: Notifications are now handled as phone screens, so this mainly handles cleanup.
     */
    private static void renderNotifications(DrawContext context) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client == null) return;

        // Just update notifications (for cleanup of expired ones)
        PhoneNotificationManager.getInstance().update();

        // The actual notification display is now handled by the phone texture system
        // When a notification is received, it switches the phone to the notification screen texture
    }

    /**
     * Gets the current phone position on screen.
     */
    private static int[] getPhonePosition(MinecraftClient client) {
        int screenWidth = client.getWindow().getScaledWidth();
        int screenHeight = client.getWindow().getScaledHeight();

        // Phone position (bottom right)
        int phoneX = screenWidth - PHONE_WIDTH - 10;
        int phoneY = screenHeight - PHONE_HEIGHT;

        return new int[]{phoneX, phoneY};
    }

    /**
     * Renders the phone background.
     */
    private static void renderPhone(DrawContext context, int phoneX, int phoneY) {
        // Get the current phone texture
        Identifier phoneTexture = PhoneTextureManager.getInstance().getCurrentTexture();

        if (phoneTexture != null) {
            // Draw phone background
            context.drawTexture(phoneTexture, phoneX, phoneY, 0, 0,
                              PHONE_WIDTH, PHONE_HEIGHT, PHONE_WIDTH, PHONE_HEIGHT);
        }
    }

    /**
     * Renders the notification icon on top of the phone.
     */
    private static void renderNotificationIcon(DrawContext context, int phoneX, int phoneY) {
        // The notif.png appears to be a full phone overlay (442x610), so we need to scale it to match our phone size
        // Draw the notification overlay to match the phone dimensions
        context.drawTexture(NOTIFICATION_ICON_TEXTURE, phoneX, phoneY, 0, 0,
                          PHONE_WIDTH, PHONE_HEIGHT, 442, 610);
    }

    /**
     * Renders notifications on top of the phone.
     */
    private static void renderNotificationsOnPhone(DrawContext context, int phoneX, int phoneY,
                                                  List<PhoneNotification> notifications) {
        MinecraftClient client = MinecraftClient.getInstance();

        // Start from the top of the phone
        int notificationY = phoneY + NOTIFICATION_MARGIN;

        for (PhoneNotification notification : notifications) {
            // Skip if notification is fully transparent
            if (notification.getAlpha() <= 0.01f) continue;

            // Calculate notification position (centered on phone)
            int notificationX = phoneX + (PHONE_WIDTH - NOTIFICATION_WIDTH) / 2;

            // Render notification background
            renderNotificationBackground(context, notificationX, notificationY, notification);

            // Render notification content
            renderNotificationContent(context, client, notificationX, notificationY, notification);

            // Move down for next notification
            notificationY += NOTIFICATION_HEIGHT + NOTIFICATION_MARGIN;

            // Stop if we've reached the bottom of the phone
            if (notificationY + NOTIFICATION_HEIGHT > phoneY + PHONE_HEIGHT - NOTIFICATION_MARGIN) {
                break;
            }
        }
    }

    /**
     * Renders the background of a notification.
     */
    private static void renderNotificationBackground(DrawContext context, int x, int y,
                                                   PhoneNotification notification) {
        float alpha = notification.getAlpha();

        // Background color (semi-transparent black)
        int bgAlpha = (int)(alpha * 180); // 70% opacity when fully visible
        int backgroundColor = (bgAlpha << 24) | 0x000000;

        // Border color (notification type color)
        int borderColor = notification.getColorWithAlpha();

        // Draw background
        context.fill(x, y, x + NOTIFICATION_WIDTH, y + NOTIFICATION_HEIGHT, backgroundColor);

        // Draw border (2 pixel thick)
        context.fill(x, y, x + NOTIFICATION_WIDTH, y + 2, borderColor); // Top
        context.fill(x, y + NOTIFICATION_HEIGHT - 2, x + NOTIFICATION_WIDTH, y + NOTIFICATION_HEIGHT, borderColor); // Bottom
        context.fill(x, y, x + 2, y + NOTIFICATION_HEIGHT, borderColor); // Left
        context.fill(x + NOTIFICATION_WIDTH - 2, y, x + NOTIFICATION_WIDTH, y + NOTIFICATION_HEIGHT, borderColor); // Right
    }

    /**
     * Renders the content of a notification.
     */
    private static void renderNotificationContent(DrawContext context, MinecraftClient client,
                                                int x, int y, PhoneNotification notification) {
        float alpha = notification.getAlpha();
        int textAlpha = (int)(alpha * 255);

        // Text colors
        int titleColor = (textAlpha << 24) | 0xFFFFFF; // White
        int messageColor = (textAlpha << 24) | 0xCCCCCC; // Light gray

        // Text positioning
        int textX = x + NOTIFICATION_PADDING;
        int textY = y + NOTIFICATION_PADDING;

        // Scale for smaller text
        float scale = 0.6f;

        // Draw title
        context.getMatrices().push();
        context.getMatrices().scale(scale, scale, 1.0f);

        int scaledTextX = (int)(textX / scale);
        int scaledTextY = (int)(textY / scale);

        // Title (first line)
        context.drawText(client.textRenderer, notification.getTitle(),
                        scaledTextX, scaledTextY, titleColor, false);

        // Message (second line)
        String message = notification.getMessage();
        int maxWidth = (int)((NOTIFICATION_WIDTH - NOTIFICATION_PADDING * 2) / scale);

        // Truncate message if too long
        if (client.textRenderer.getWidth(message) > maxWidth) {
            message = client.textRenderer.trimToWidth(message, maxWidth - 10) + "...";
        }

        context.drawText(client.textRenderer, message,
                        scaledTextX, scaledTextY + 10, messageColor, false);

        context.getMatrices().pop();
    }
}
