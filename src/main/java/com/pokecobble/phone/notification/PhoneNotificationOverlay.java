package com.pokecobble.phone.notification;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.PhoneTextureManager;
import com.pokecobble.phone.gui.PhoneRenderer;
import net.fabricmc.fabric.api.client.rendering.v1.HudRenderCallback;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.util.Identifier;

/**
 * Handles rendering notification overlays on top of the phone screen.
 * This creates the illusion that notifications appear within the phone interface.
 */
public class PhoneNotificationOverlay {
    private static final PhoneNotificationOverlay INSTANCE = new PhoneNotificationOverlay();

    // Phone dimensions (should match PhoneRenderer)
    private static final int PHONE_WIDTH = 100;
    private static final int PHONE_HEIGHT = 160;

    // Notification overlay texture
    private static final Identifier NOTIFICATION_OVERLAY_TEXTURE =
        new Identifier("pokecobbleclaim", "textures/phone/textures/notif.png");

    // State tracking
    private boolean overlayVisible = false;
    private boolean hasActiveNotifications = false;
    private boolean lastLoggedState = false;

    private PhoneNotificationOverlay() {}

    public static PhoneNotificationOverlay getInstance() {
        return INSTANCE;
    }

    /**
     * Registers the notification overlay renderer.
     */
    public static void register() {
        Pokecobbleclaim.LOGGER.info("Registering phone notification overlay renderer");

        // Register the HUD render callback for the overlay
        HudRenderCallback.EVENT.register((context, tickDelta) -> {
            getInstance().renderOverlayHUD(context);
        });
    }

    /**
     * Updates the overlay state based on active notifications.
     */
    public void update() {
        // Check if there are any active notifications
        hasActiveNotifications = PhoneNotificationManager.getInstance().hasActiveNotifications();

        // If no active notifications, hide the overlay
        if (!hasActiveNotifications) {
            overlayVisible = false;
        }
    }

    /**
     * Renders the notification overlay on top of the phone if visible.
     *
     * @param context The draw context
     * @param phoneX The phone's X position
     * @param phoneY The phone's Y position
     */
    public void renderOverlay(DrawContext context, int phoneX, int phoneY) {
        // Only render if overlay is visible and we have active notifications
        if (!overlayVisible || !hasActiveNotifications) {
            return;
        }

        Pokecobbleclaim.LOGGER.info("Attempting to render notification overlay at " + phoneX + "," + phoneY +
                                   " - visible: " + overlayVisible + ", hasNotifications: " + hasActiveNotifications);

        try {
            // Draw the notification overlay texture scaled to phone dimensions
            // The notif.png is 442x610, we scale it to match our phone size (100x160)
            context.drawTexture(NOTIFICATION_OVERLAY_TEXTURE, phoneX, phoneY, 0, 0,
                              PHONE_WIDTH, PHONE_HEIGHT, 442, 610);

            Pokecobbleclaim.LOGGER.info("Successfully rendered notification overlay at position: " + phoneX + "," + phoneY);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to render notification overlay: " + e.getMessage());
            e.printStackTrace();

            // Fallback: draw a semi-transparent red overlay to indicate notification presence
            context.fill(phoneX, phoneY, phoneX + PHONE_WIDTH, phoneY + PHONE_HEIGHT, 0x80FF0000);
            Pokecobbleclaim.LOGGER.info("Drew fallback red overlay at " + phoneX + "," + phoneY);
        }

        // Also draw a test border to verify rendering is working
        context.drawBorder(phoneX - 2, phoneY - 2, PHONE_WIDTH + 4, PHONE_HEIGHT + 4, 0xFF00FF00);
        Pokecobbleclaim.LOGGER.info("Drew test green border around phone at " + phoneX + "," + phoneY);
    }

    /**
     * Shows the notification overlay if there are active notifications.
     */
    public void showOverlay() {
        if (hasActiveNotifications) {
            overlayVisible = true;
            Pokecobbleclaim.LOGGER.info("Notification overlay shown");
        } else {
            Pokecobbleclaim.LOGGER.debug("Cannot show overlay - no active notifications");
        }
    }

    /**
     * Hides the notification overlay.
     */
    public void hideOverlay() {
        overlayVisible = false;
        Pokecobbleclaim.LOGGER.info("Notification overlay hidden");
    }

    /**
     * Toggles the notification overlay visibility.
     * Only shows if there are active notifications.
     */
    public void toggleOverlay() {
        if (overlayVisible) {
            hideOverlay();
        } else {
            showOverlay();
        }
    }

    /**
     * Checks if the overlay is currently visible.
     */
    public boolean isOverlayVisible() {
        return overlayVisible;
    }

    /**
     * Checks if there are active notifications that can trigger the overlay.
     */
    public boolean hasActiveNotifications() {
        return hasActiveNotifications;
    }

    /**
     * Forces the overlay to show (for testing purposes).
     */
    public void forceShowOverlay() {
        overlayVisible = true;
        Pokecobbleclaim.LOGGER.info("Notification overlay force shown");
    }

    /**
     * Renders the notification overlay on the HUD if the phone is visible.
     */
    public void renderOverlayHUD(DrawContext context) {
        // Update state first
        update();

        // Debug logging
        PhoneRenderer phoneRenderer = PhoneRenderer.getInstance();
        boolean phoneVisible = phoneRenderer.isVisible();

        // Only log when state changes to avoid spam
        boolean shouldLog = (overlayVisible || hasActiveNotifications) && !lastLoggedState;
        if (shouldLog) {
            Pokecobbleclaim.LOGGER.info("HUD Render - Phone visible: " + phoneVisible +
                                       ", Overlay visible: " + overlayVisible +
                                       ", Has notifications: " + hasActiveNotifications);
            lastLoggedState = true;
        } else if (!overlayVisible && !hasActiveNotifications) {
            lastLoggedState = false;
        }

        // Only render if phone is visible and overlay should be shown
        if (!phoneVisible || !overlayVisible || !hasActiveNotifications) {
            return;
        }

        // Calculate phone position (same logic as PhoneRenderer)
        MinecraftClient client = MinecraftClient.getInstance();
        int screenWidth = client.getWindow().getScaledWidth();
        int screenHeight = client.getWindow().getScaledHeight();

        int phoneX = screenWidth - PHONE_WIDTH - 10;
        int phoneY = screenHeight - PHONE_HEIGHT;

        Pokecobbleclaim.LOGGER.info("HUD Render - Calling renderOverlay at " + phoneX + "," + phoneY);

        // Render the overlay
        renderOverlay(context, phoneX, phoneY);
    }
}
