package com.pokecobble.phone;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.gui.PhoneScreen;
import com.pokecobble.phone.gui.NotificationScreen;
import com.pokecobble.phone.gui.PhoneRenderer;
import com.pokecobble.phone.notification.PhoneNotificationOverlay;
import com.pokecobble.phone.notification.PhoneNotificationManager;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import org.lwjgl.glfw.GLFW;

/**
 * Handles the phone keybinding.
 */
public class PhoneKeybind {
    private static KeyBinding phoneKey;

    /**
     * Registers the phone keybinding.
     */
    public static void register() {
        Pokecobbleclaim.LOGGER.info("Registering phone keybinding");

        // Register the keybinding (F key by default)
        phoneKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
                "key.pokecobbleclaim.toggle_phone",
                InputUtil.Type.KEYSYM,
                GLFW.GLFW_KEY_F,
                "category.pokecobbleclaim.phone"
        ));

        // Register the tick event to check for key presses
        ClientTickEvents.END_CLIENT_TICK.register(client -> {
            if (phoneKey.wasPressed()) {
                handlePhoneKeyPress(client);
            }
        });
    }

    /**
     * Handles phone key press with notification overlay support.
     */
    private static void handlePhoneKeyPress(MinecraftClient client) {
        PhoneNotificationOverlay overlay = PhoneNotificationOverlay.getInstance();
        PhoneNotificationManager notificationManager = PhoneNotificationManager.getInstance();

        // Priority 1: If notification overlay is visible, hide it
        if (overlay.isOverlayVisible()) {
            Pokecobbleclaim.LOGGER.info("Phone key pressed, hiding notification overlay");
            overlay.hideOverlay();
            return;
        }

        // Priority 2: If there are active notifications and no screen is open, show overlay on phone
        if (notificationManager.hasActiveNotifications() && client.currentScreen == null) {
            Pokecobbleclaim.LOGGER.info("Phone key pressed, showing notification overlay");
            // Make sure phone is visible first
            PhoneRenderer.getInstance().setVisible(true);
            overlay.showOverlay();
            return;
        }

        // Priority 3: Handle normal phone screen behavior
        if (client.currentScreen instanceof PhoneScreen) {
            Pokecobbleclaim.LOGGER.info("Phone key pressed, closing phone screen");
            client.setScreen(null); // Close the phone screen
        } else if (client.currentScreen instanceof NotificationScreen) {
            Pokecobbleclaim.LOGGER.info("Phone key pressed, but notification screen is open - ignoring");
            // Don't close notification screen with phone key - let it handle its own closing
        } else if (client.currentScreen == null) {
            Pokecobbleclaim.LOGGER.info("Phone key pressed, opening phone screen");
            client.setScreen(new PhoneScreen()); // Open the phone screen
        } else {
            Pokecobbleclaim.LOGGER.info("Phone key pressed, but another screen is open - ignoring");
        }
    }
}
