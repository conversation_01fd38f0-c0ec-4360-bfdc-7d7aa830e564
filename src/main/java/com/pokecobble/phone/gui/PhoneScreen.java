package com.pokecobble.phone.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.DevModeManager;
import com.pokecobble.phone.app.App;
import com.pokecobble.phone.app.AppPosition;
import com.pokecobble.phone.app.AppPositionManager;
import com.pokecobble.phone.app.AppRegistry;
import com.pokecobble.phone.PhoneTextureManager;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;
import net.minecraft.util.Identifier;

import java.util.List;

/**
 * The phone screen.
 */
public class PhoneScreen extends Screen {
    // Phone dimensions - these should match the texture dimensions
    private static final int PHONE_WIDTH = 100;
    private static final int PHONE_HEIGHT = 160;

    // App grid dimensions
    private static final int GRID_COLS = 4;
    private static final int GRID_ROWS = 6;
    private static final int APP_SIZE = 16;
    private static final int APP_SPACING_HORIZONTAL = 6; // More horizontal spacing
    private static final int APP_SPACING_VERTICAL = 2;
    private static final int GRID_START_X = 10; // Adjusted for centering
    private static final int GRID_START_Y = 30;

    // Phone position
    private int phoneX;
    private int phoneY;

    // Dragging state
    private boolean isDragging = false;
    private String draggingAppId = null;
    private int dragStartX;
    private int dragStartY;
    private int dragOffsetX;
    private int dragOffsetY;
    private int dragCurrentX;
    private int dragCurrentY;

    // Long-click tracking
    private long clickStartTime = 0;
    private String clickedAppId = null;
    private int clickedAppX = 0;
    private int clickedAppY = 0;
    private boolean isLongClickInProgress = false;
    private static final long LONG_CLICK_TIME = 500; // milliseconds

    /**
     * Creates a new phone screen.
     */
    public PhoneScreen() {
        super(Text.literal("Phone"));
    }

    @Override
    protected void init() {
        super.init();

        // Calculate phone position (bottom right)
        phoneX = this.width - PHONE_WIDTH - 10;
        phoneY = this.height - PHONE_HEIGHT;
    }

    @Override
    public void renderBackground(DrawContext context) {
        // Don't render the standard dark background
    }

    /**
     * Checks if dev mode is enabled for the current player.
     *
     * @return True if dev mode is enabled, false otherwise
     */
    private boolean isDevModeEnabled() {
        return this.client.player != null && DevModeManager.getInstance().isDevModeEnabled(this.client.player);
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Skip calling super.render() to avoid the dark background
        // Instead, just render our phone UI

        // Get the current phone texture from the texture manager
        Identifier phoneTexture = PhoneTextureManager.getInstance().getCurrentTexture();

        // Make sure the texture is not null before drawing
        if (phoneTexture != null) {
            // Draw phone background
            context.drawTexture(phoneTexture, phoneX, phoneY, 0, 0, PHONE_WIDTH, PHONE_HEIGHT, PHONE_WIDTH, PHONE_HEIGHT);
        } else {
            Pokecobbleclaim.LOGGER.error("Phone texture is null, cannot render phone");
        }

        // Only draw app grid if not in app mode
        if (!PhoneTextureManager.getInstance().isInAppMode()) {
            drawAppGrid(context, mouseX, mouseY);
        }

        // Check for long click and draw indicator if needed
        checkForLongClick();

        // Draw long click progress indicator if a click is in progress
        if (clickedAppId != null && !isDragging && !isLongClickInProgress) {
            long currentTime = System.currentTimeMillis();
            float progress = Math.min(1.0f, (float)(currentTime - clickStartTime) / LONG_CLICK_TIME);

            // Draw a progress indicator around the app
            if (progress > 0.1f) { // Only start showing after a small delay
                int indicatorSize = APP_SIZE + 6;
                int indicatorX = clickedAppX - 3;
                int indicatorY = clickedAppY - 3;

                // Draw a border that gets brighter as the long click progresses
                int alpha = (int)(255 * progress);
                int color = 0x00FFFFFF | (alpha << 24); // White with increasing alpha

                // Draw a border around the app that gets thicker as progress increases
                int borderThickness = Math.max(1, (int)(3 * progress));
                for (int i = 0; i < borderThickness; i++) {
                    context.drawBorder(indicatorX - i, indicatorY - i,
                                      indicatorSize + i*2, indicatorSize + i*2,
                                      color);
                }
            }
        }
    }

    /**
     * Checks if a long click has occurred and starts dragging if it has.
     */
    private void checkForLongClick() {
        // If we have a click in progress but aren't dragging yet
        if (clickedAppId != null && !isDragging && !isLongClickInProgress) {
            long currentTime = System.currentTimeMillis();

            // Check if enough time has passed for a long click
            if (currentTime - clickStartTime > LONG_CLICK_TIME) {
                // Start dragging
                isLongClickInProgress = true;
                isDragging = true;
                draggingAppId = clickedAppId;

                // Long click detected, starting drag
            }
        }
    }

    /**
     * Draws the app grid.
     *
     * @param context The draw context
     * @param mouseX The mouse X position
     * @param mouseY The mouse Y position
     */
    private void drawAppGrid(DrawContext context, int mouseX, int mouseY) {
        List<App> apps = AppRegistry.getInstance().getAllApps();

        // Calculate total grid width to center it
        int totalGridWidth = GRID_COLS * APP_SIZE + (GRID_COLS - 1) * APP_SPACING_HORIZONTAL;
        int gridStartX = phoneX + (PHONE_WIDTH - totalGridWidth) / 2;

        // Draw grid cells (subtle background for each cell)
        for (int row = 0; row < GRID_ROWS; row++) {
            for (int col = 0; col < GRID_COLS; col++) {
                int cellX = gridStartX + col * (APP_SIZE + APP_SPACING_HORIZONTAL);
                int cellY = phoneY + GRID_START_Y + row * (APP_SIZE + APP_SPACING_VERTICAL + 8);

                // Draw a more visible cell background with a border to make grid positions more visible
                // since we no longer have placeholder apps filling the grid
                context.fill(cellX, cellY, cellX + APP_SIZE, cellY + APP_SIZE, 0x15FFFFFF);
                context.drawBorder(cellX, cellY, APP_SIZE, APP_SIZE, 0x30FFFFFF);
            }
        }

        // Draw apps based on their positions
        for (App app : apps) {
            // Skip the app being dragged (we'll draw it separately)
            if (isDragging && app.getId().equals(draggingAppId)) {
                continue;
            }

            // Skip placeholder apps with IDs starting with "app"
            if (app.getId().startsWith("app")) {
                continue;
            }

            // Get the app's position
            com.pokecobble.phone.app.AppPosition position =
                com.pokecobble.phone.app.AppPositionManager.getInstance().getAppPosition(app.getId());

            int col = position.getGridX();
            int row = position.getGridY();

            // Skip if outside the grid
            if (col < 0 || col >= GRID_COLS || row < 0 || row >= GRID_ROWS) {
                continue;
            }

            // Calculate app position
            int appX = gridStartX + col * (APP_SIZE + APP_SPACING_HORIZONTAL);
            int appY = phoneY + GRID_START_Y + row * (APP_SIZE + APP_SPACING_VERTICAL + 8); // Extra space for app name

            // Draw app icon
            context.drawTexture(app.getIconTexture(), appX, appY, 0, 0, APP_SIZE, APP_SIZE, APP_SIZE, APP_SIZE);

            // Draw app name - with even smaller font
            String appName = app.getName();

            // Calculate text width at scale for proper centering
            float scale = 0.33f;
            int textWidth = (int)(this.textRenderer.getWidth(appName) * scale);

            // Center text under the app icon
            int textX = appX + (APP_SIZE - textWidth) / 2;
            int textY = appY + APP_SIZE + 1;

            // Draw text at one-third scale
            context.getMatrices().push();
            context.getMatrices().scale(scale, scale, 1.0f);
            context.drawText(this.textRenderer, appName, (int)(textX / scale), (int)(textY / scale), 0xFFFFFFFF, true);
            context.getMatrices().pop();
        }

        // Draw the app being dragged on top of everything else
        if (isDragging && draggingAppId != null) {
            App app = AppRegistry.getInstance().getApp(draggingAppId);
            if (app != null) {
                // Draw app icon at drag position
                context.drawTexture(app.getIconTexture(), dragCurrentX - dragOffsetX, dragCurrentY - dragOffsetY,
                                  0, 0, APP_SIZE, APP_SIZE, APP_SIZE, APP_SIZE);

                // Draw app name - with even smaller font
                String appName = app.getName();

                // Calculate text width at scale for proper centering
                float scale = 0.33f;
                int textWidth = (int)(this.textRenderer.getWidth(appName) * scale);

                // Center text under the app icon
                int textX = (dragCurrentX - dragOffsetX) + (APP_SIZE - textWidth) / 2;
                int textY = (dragCurrentY - dragOffsetY) + APP_SIZE + 1;

                // Draw text at one-third scale
                context.getMatrices().push();
                context.getMatrices().scale(scale, scale, 1.0f);
                context.drawText(this.textRenderer, appName, (int)(textX / scale), (int)(textY / scale), 0xFFFFFFFF, true);
                context.getMatrices().pop();

                // Draw a highlight around the cell where the app would be dropped
                int[] gridPos = getGridPositionFromMouse(mouseX, mouseY);
                if (gridPos[0] >= 0 && gridPos[0] < GRID_COLS && gridPos[1] >= 0 && gridPos[1] < GRID_ROWS) {
                    int cellX = gridStartX + gridPos[0] * (APP_SIZE + APP_SPACING_HORIZONTAL);
                    int cellY = phoneY + GRID_START_Y + gridPos[1] * (APP_SIZE + APP_SPACING_VERTICAL + 8);

                    // Draw a highlight around the cell
                    context.drawBorder(cellX - 1, cellY - 1, APP_SIZE + 2, APP_SIZE + 2, 0xFFFFFFFF);
                }
            }
        }
    }

    /**
     * Gets the grid position from mouse coordinates.
     *
     * @param mouseX The mouse X coordinate
     * @param mouseY The mouse Y coordinate
     * @return The grid position as an int array [x, y]
     */
    private int[] getGridPositionFromMouse(int mouseX, int mouseY) {
        // Calculate total grid width to center it
        int totalGridWidth = GRID_COLS * APP_SIZE + (GRID_COLS - 1) * APP_SPACING_HORIZONTAL;
        int gridStartX = phoneX + (PHONE_WIDTH - totalGridWidth) / 2;

        // Calculate the relative position within the grid
        int relativeX = mouseX - gridStartX;
        int relativeY = mouseY - (phoneY + GRID_START_Y);

        // Calculate the cell size including spacing
        int cellWidth = APP_SIZE + APP_SPACING_HORIZONTAL;
        int cellHeight = APP_SIZE + APP_SPACING_VERTICAL + 8; // Extra space for app name

        // Calculate the grid position
        int gridX = relativeX / cellWidth;
        int gridY = relativeY / cellHeight;

        // Check if the position is within the grid bounds
        if (gridX < 0 || gridX >= GRID_COLS || gridY < 0 || gridY >= GRID_ROWS) {
            return new int[] { -1, -1 };
        }
        return new int[] { gridX, gridY };
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // If in app mode, don't handle clicks here - let the app screen handle them
        if (PhoneTextureManager.getInstance().isInAppMode()) {
            return false;
        }

        // Check if click is on an app
        List<App> apps = AppRegistry.getInstance().getAllApps();

        // Calculate total grid width to center it (same as in drawAppGrid)
        int totalGridWidth = GRID_COLS * APP_SIZE + (GRID_COLS - 1) * APP_SPACING_HORIZONTAL;
        int gridStartX = phoneX + (PHONE_WIDTH - totalGridWidth) / 2;

        // Check each app
        for (App app : apps) {
            // Skip placeholder apps with IDs starting with "app"
            if (app.getId().startsWith("app")) {
                continue;
            }

            // Get the app's position
            com.pokecobble.phone.app.AppPosition position =
                com.pokecobble.phone.app.AppPositionManager.getInstance().getAppPosition(app.getId());

            int col = position.getGridX();
            int row = position.getGridY();

            // Skip if outside the grid
            if (col < 0 || col >= GRID_COLS || row < 0 || row >= GRID_ROWS) {
                continue;
            }

            // Calculate app position
            int appX = gridStartX + col * (APP_SIZE + APP_SPACING_HORIZONTAL);
            int appY = phoneY + GRID_START_Y + row * (APP_SIZE + APP_SPACING_VERTICAL + 8);

            // Check if click is on this app
            if (mouseX >= appX && mouseX <= appX + APP_SIZE &&
                mouseY >= appY && mouseY <= appY + APP_SIZE) {

                // Left click to start tracking for potential long click or immediate open
                if (button == 0) {
                    // Record the click start time and app info
                    clickStartTime = System.currentTimeMillis();
                    clickedAppId = app.getId();
                    clickedAppX = appX;
                    clickedAppY = appY;
                    isLongClickInProgress = false;

                    // Set up drag parameters in case this becomes a drag operation
                    dragStartX = (int)mouseX;
                    dragStartY = (int)mouseY;
                    dragOffsetX = (int)mouseX - appX;
                    dragOffsetY = (int)mouseY - appY;
                    dragCurrentX = (int)mouseX;
                    dragCurrentY = (int)mouseY;

                    // Click started, waiting to determine if it's a long click
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Checks if a click is inside the phone area.
     *
     * @param mouseX The mouse X coordinate
     * @param mouseY The mouse Y coordinate
     * @return true if the click is inside the phone, false otherwise
     */
    private boolean isClickInsidePhone(double mouseX, double mouseY) {
        return mouseX >= phoneX && mouseX <= phoneX + PHONE_WIDTH &&
               mouseY >= phoneY && mouseY <= phoneY + PHONE_HEIGHT;
    }

    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        // Only allow dragging if we've confirmed it's a long click
        if (isDragging && draggingAppId != null && isLongClickInProgress) {
            // Update the current drag position
            dragCurrentX = (int)mouseX;
            dragCurrentY = (int)mouseY;
            return true;
        } else if (clickedAppId != null && !isLongClickInProgress) {
            // If we're moving the mouse while holding down but haven't confirmed a long click yet,
            // check if we've moved too far from the original position and cancel the click if so
            double distance = Math.sqrt(Math.pow(mouseX - dragStartX, 2) + Math.pow(mouseY - dragStartY, 2));
            if (distance > 5) { // If moved more than 5 pixels, cancel the click
                // Mouse moved too far during click, canceling
                clickedAppId = null;
                clickStartTime = 0;
            }
        }

        return super.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
    }

    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        // If in app mode, don't process app clicks
        if (PhoneTextureManager.getInstance().isInAppMode()) {
            return false;
        }

        // If we have a click in progress but it's not a long click yet, it's a short click - open the app
        if (clickedAppId != null && !isLongClickInProgress && !isDragging) {
            App app = AppRegistry.getInstance().getApp(clickedAppId);
            if (app != null) {
                com.pokecobble.Pokecobbleclaim.LOGGER.info("Short click detected on app " + clickedAppId + ", opening app");
                app.open(this.client);

                // Reset click tracking
                clickedAppId = null;
                clickStartTime = 0;
                return true;
            }
        }

        // Handle drag release
        if (isDragging && draggingAppId != null) {
            try {
                // Get the grid position where the app was dropped
                int[] gridPos = getGridPositionFromMouse((int)mouseX, (int)mouseY);

                // If the position is valid (within the grid), move the app there
                if (gridPos[0] >= 0 && gridPos[0] < GRID_COLS && gridPos[1] >= 0 && gridPos[1] < GRID_ROWS) {
                    // Get the app position manager
                    com.pokecobble.phone.app.AppPositionManager appManager = com.pokecobble.phone.app.AppPositionManager.getInstance();

                    // Check if the position is already occupied by another app
                    boolean occupied = appManager.isPositionOccupied(gridPos[0], gridPos[1], draggingAppId);

                    // Get the current position for comparison later
                    com.pokecobble.phone.app.AppPosition currentPos = appManager.getAppPosition(draggingAppId);

                    if (occupied) {
                        // Find the nearest available position
                        int[] nearestPos = appManager.findNearestAvailablePosition(
                                gridPos[0], gridPos[1], draggingAppId, GRID_COLS, GRID_ROWS);

                        // Update the app's position
                        appManager.setAppPosition(draggingAppId, nearestPos[0], nearestPos[1]);
                    } else {
                        // Position is not occupied, update the app's position directly
                        appManager.setAppPosition(draggingAppId, gridPos[0], gridPos[1]);
                    }

                    // Force a reload of positions to ensure we're using the latest data
                    appManager.loadPositions();

                    // Verify the position was updated by getting it again
                    com.pokecobble.phone.app.AppPosition updatedPos = appManager.getAppPosition(draggingAppId);

                    // Check if the position actually changed
                    if (updatedPos.getGridX() == currentPos.getGridX() && updatedPos.getGridY() == currentPos.getGridY()) {
                        // Force a direct update to the position object
                        updatedPos.setGridPosition(gridPos[0], gridPos[1]);
                        // Save again
                        appManager.savePositions();
                    }
                }
            } catch (Exception e) {
                com.pokecobble.Pokecobbleclaim.LOGGER.error("Error in mouseReleased: " + e.getMessage());
                e.printStackTrace();
            } finally {
                // Reset all tracking states
                isDragging = false;
                draggingAppId = null;
                clickedAppId = null;
                clickStartTime = 0;
                isLongClickInProgress = false;
            }
            return true;
        }

        return super.mouseReleased(mouseX, mouseY, button);
    }

    @Override
    public boolean shouldPause() {
        // Don't pause the game when the phone is open
        return false;
    }

    @Override
    public boolean shouldCloseOnEsc() {
        // Close the phone when ESC is pressed
        return true;
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        // Close the phone when F key is pressed
        if (keyCode == org.lwjgl.glfw.GLFW.GLFW_KEY_F) {
            this.close();
            return true;
        }

        // Open the shape visualizer when V key is pressed (only in dev mode)
        if (keyCode == org.lwjgl.glfw.GLFW.GLFW_KEY_V) {
            if (isDevModeEnabled()) {
                Pokecobbleclaim.LOGGER.info("V key pressed on phone screen, opening shape visualizer");
                // Use toggleShapeVisualizer instead of openOnPhone to ensure consistent behavior
                com.pokecobble.tools.gui.ShapeVisualizerTool.toggleShapeVisualizer();
                return true;
            } else {
                Pokecobbleclaim.LOGGER.debug("V key pressed but dev mode is not enabled");
            }
        }

        return super.keyPressed(keyCode, scanCode, modifiers);
    }
}
