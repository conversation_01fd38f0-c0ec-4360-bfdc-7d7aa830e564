package com.pokecobble.phone.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.DevModeManager;
import com.pokecobble.phone.PhoneTextureManager;
import com.pokecobble.tools.gui.ShapeVisualizerTool;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;
import net.minecraft.util.Identifier;
import org.lwjgl.glfw.GLFW;

/**
 * The settings app screen that displays the settings interface and handles interactions.
 */
public class SettingsAppScreen extends Screen {
    // Phone dimensions - these should match the texture dimensions
    private static final int PHONE_WIDTH = 100;
    private static final int PHONE_HEIGHT = 160;

    // Phone position
    private int phoneX;
    private int phoneY;

    // Exit bar dimensions and position
    private static final int EXIT_BAR_WIDTH = 60;
    private static final int EXIT_BAR_HEIGHT = 10;
    private static final int EXIT_BAR_RELATIVE_X = 21; // Centered horizontally
    private static final int EXIT_BAR_RELATIVE_Y = 152; // At the bottom

    // Exit bar absolute position (for reference)
    private static final int ABSOLUTE_EXIT_BAR_X = 391;
    private static final int ABSOLUTE_EXIT_BAR_Y = 255;

    // Reference phone position
    private static final int REFERENCE_PHONE_X = 370;
    private static final int REFERENCE_PHONE_Y = 92;

    // Exit bar physics parameters
    private static final float EXIT_BAR_RETENTION_FORCE = 0.15f;
    private static final float EXIT_BAR_FRICTION = 0.9f;
    private static final float EXIT_BAR_MAX_UP_OFFSET = 30.0f;
    private static final float EXIT_BAR_MAX_SIDE_OFFSET = 15.0f;

    // Dynamic resizing parameters
    private static final float EXIT_BAR_MIN_WIDTH_PERCENT = 0.7f;
    private static final float EXIT_BAR_RESIZE_FACTOR = 0.015f;

    // First toggle switch position and dimensions (from shape visualizer) - made bigger and scaled horizontally
    private static final int TOGGLE1_RELATIVE_X = 65; // X position relative to phone (adjusted for bigger size)
    private static final int TOGGLE1_RELATIVE_Y = 30; // Y position relative to phone (adjusted for bigger size)
    private static final int TOGGLE_WIDTH = 30; // Width of the element (increased from 25 for horizontal scaling)
    private static final int TOGGLE_HEIGHT = 14; // Height of the element (increased from 10)

    // Reference phone position when these coordinates were captured
    private static final int TOGGLE1_REFERENCE_PHONE_X = 370; // Reference phone X position
    private static final int TOGGLE1_REFERENCE_PHONE_Y = 103; // Reference phone Y position

    // Absolute position on screen (adjusted for bigger size and horizontal scaling)
    private static final int TOGGLE1_ABSOLUTE_X = 435; // Absolute X position on screen (adjusted for horizontal scaling)
    private static final int TOGGLE1_ABSOLUTE_Y = 133; // Absolute Y position on screen (adjusted for bigger size)

    // Second toggle switch position and dimensions (from shape visualizer) - same size as first
    private static final int TOGGLE2_RELATIVE_X = 65; // X position relative to phone (adjusted for bigger size)
    private static final int TOGGLE2_RELATIVE_Y = 75; // Y position relative to phone (adjusted for bigger size)

    // Reference phone position when these coordinates were captured
    private static final int TOGGLE2_REFERENCE_PHONE_X = 370; // Reference phone X position
    private static final int TOGGLE2_REFERENCE_PHONE_Y = 103; // Reference phone Y position

    // Absolute position on screen (adjusted for bigger size and horizontal scaling)
    private static final int TOGGLE2_ABSOLUTE_X = 435; // Absolute X position on screen (adjusted for horizontal scaling)
    private static final int TOGGLE2_ABSOLUTE_Y = 178; // Absolute Y position on screen (adjusted for bigger size)

    // Toggle switch states
    private boolean toggle1State = false; // false = off, true = on
    private boolean toggle2State = false; // false = off, true = on

    // Exit bar state
    private boolean isExitBarDragging = false;
    private float exitBarOffsetX = 0.0f;
    private float exitBarOffsetY = 0.0f;
    private float exitBarVelocityX = 0.0f;
    private float exitBarVelocityY = 0.0f;
    private int exitBarDragStartX;
    private int exitBarDragStartY;

    /**
     * Creates a new settings app screen.
     */
    public SettingsAppScreen() {
        super(Text.literal("Settings App"));
    }

    @Override
    protected void init() {
        super.init();

        // Calculate phone position (bottom right)
        phoneX = this.width - PHONE_WIDTH - 10;
        phoneY = this.height - PHONE_HEIGHT;

        // Log screen dimensions for debugging
        Pokecobbleclaim.LOGGER.debug("Screen dimensions: width=" + this.width + ", height=" + this.height);
        Pokecobbleclaim.LOGGER.debug("Phone position: x=" + phoneX + ", y=" + phoneY);
    }

    @Override
    public void renderBackground(DrawContext context) {
        // Don't render the standard dark background
    }

    // Debug info is only shown when dev mode is enabled
    private boolean isDevModeEnabled() {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            return client != null && client.player != null && DevModeManager.getInstance().isDevModeEnabled(client.player);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error checking dev mode status: " + e.getMessage());
            return false;
        }
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Skip calling super.render() to avoid the dark background
        // Instead, just render our phone UI

        // Get the current phone texture from the texture manager
        Identifier phoneTexture = PhoneTextureManager.getInstance().getCurrentTexture();

        // Make sure the texture is not null before drawing
        if (phoneTexture != null) {
            // Draw phone background
            context.drawTexture(phoneTexture, phoneX, phoneY, 0, 0, PHONE_WIDTH, PHONE_HEIGHT, PHONE_WIDTH, PHONE_HEIGHT);

            // Call super.render() to ensure all widgets are rendered properly
            super.render(context, mouseX, mouseY, delta);
        } else {
            Pokecobbleclaim.LOGGER.error("Phone texture is null, cannot render phone");
        }

        // Draw both toggle switches
        drawToggleSwitch1(context, mouseX, mouseY);
        drawToggleSwitch2(context, mouseX, mouseY);

        // Draw the exit bar
        drawExitBar(context, mouseX, mouseY, delta);

        // Draw debug information if dev mode is enabled
        if (isDevModeEnabled()) {
            drawDebugInfo(context);
        }
    }

    /**
     * Draws the first toggle switch on the settings screen.
     *
     * @param context The draw context
     * @param mouseX The mouse X coordinate
     * @param mouseY The mouse Y coordinate
     */
    private void drawToggleSwitch1(DrawContext context, int mouseX, int mouseY) {
        // Get the toggle switch position
        int[] toggleDimensions = getToggleSwitch1Dimensions();
        int toggleX = toggleDimensions[0];
        int toggleY = toggleDimensions[1];
        int toggleWidth = toggleDimensions[2];
        int toggleHeight = toggleDimensions[3];

        // Choose the appropriate texture based on toggle state
        Identifier toggleTexture = toggle1State ?
            PhoneTextureManager.SETTINGS_TOGGLE_ON_TEXTURE :
            PhoneTextureManager.SETTINGS_TOGGLE_OFF_TEXTURE;

        // Draw the toggle switch
        context.drawTexture(
            toggleTexture,
            toggleX, toggleY,
            0, 0,
            toggleWidth, toggleHeight,
            toggleWidth, toggleHeight // Assuming texture dimensions match element dimensions
        );
    }

    /**
     * Draws the second toggle switch on the settings screen.
     *
     * @param context The draw context
     * @param mouseX The mouse X coordinate
     * @param mouseY The mouse Y coordinate
     */
    private void drawToggleSwitch2(DrawContext context, int mouseX, int mouseY) {
        // Get the toggle switch position
        int[] toggleDimensions = getToggleSwitch2Dimensions();
        int toggleX = toggleDimensions[0];
        int toggleY = toggleDimensions[1];
        int toggleWidth = toggleDimensions[2];
        int toggleHeight = toggleDimensions[3];

        // Choose the appropriate texture based on toggle state
        Identifier toggleTexture = toggle2State ?
            PhoneTextureManager.SETTINGS_TOGGLE_ON_TEXTURE :
            PhoneTextureManager.SETTINGS_TOGGLE_OFF_TEXTURE;

        // Draw the toggle switch
        context.drawTexture(
            toggleTexture,
            toggleX, toggleY,
            0, 0,
            toggleWidth, toggleHeight,
            toggleWidth, toggleHeight // Assuming texture dimensions match element dimensions
        );
    }

    /**
     * Gets the first toggle switch dimensions based on the current phone position.
     *
     * @return An array containing [toggleX, toggleY, toggleWidth, toggleHeight]
     */
    private int[] getToggleSwitch1Dimensions() {
        // Calculate toggle position relative to the phone
        int toggleX = phoneX + TOGGLE1_RELATIVE_X;
        int toggleY = phoneY + TOGGLE1_RELATIVE_Y;

        // For the reference screen size, use the exact absolute position
        if (this.width == 480 && this.height == 252) {
            // Calculate the difference between current and reference phone positions
            int phoneDiffX = phoneX - TOGGLE1_REFERENCE_PHONE_X;
            int phoneDiffY = phoneY - TOGGLE1_REFERENCE_PHONE_Y;

            // Adjust the absolute position based on the phone position difference
            toggleX = TOGGLE1_ABSOLUTE_X + phoneDiffX;
            toggleY = TOGGLE1_ABSOLUTE_Y + phoneDiffY;
        }

        return new int[] { toggleX, toggleY, TOGGLE_WIDTH, TOGGLE_HEIGHT };
    }

    /**
     * Gets the second toggle switch dimensions based on the current phone position.
     *
     * @return An array containing [toggleX, toggleY, toggleWidth, toggleHeight]
     */
    private int[] getToggleSwitch2Dimensions() {
        // Calculate toggle position relative to the phone
        int toggleX = phoneX + TOGGLE2_RELATIVE_X;
        int toggleY = phoneY + TOGGLE2_RELATIVE_Y;

        // For the reference screen size, use the exact absolute position
        if (this.width == 480 && this.height == 252) {
            // Calculate the difference between current and reference phone positions
            int phoneDiffX = phoneX - TOGGLE2_REFERENCE_PHONE_X;
            int phoneDiffY = phoneY - TOGGLE2_REFERENCE_PHONE_Y;

            // Adjust the absolute position based on the phone position difference
            toggleX = TOGGLE2_ABSOLUTE_X + phoneDiffX;
            toggleY = TOGGLE2_ABSOLUTE_Y + phoneDiffY;
        }

        return new int[] { toggleX, toggleY, TOGGLE_WIDTH, TOGGLE_HEIGHT };
    }

    /**
     * Checks if the mouse is over the first toggle switch.
     *
     * @param mouseX The mouse X coordinate
     * @param mouseY The mouse Y coordinate
     * @return true if the mouse is over the first toggle switch, false otherwise
     */
    private boolean isMouseOverToggleSwitch1(int mouseX, int mouseY) {
        int[] dimensions = getToggleSwitch1Dimensions();
        int toggleX = dimensions[0];
        int toggleY = dimensions[1];
        int toggleWidth = dimensions[2];
        int toggleHeight = dimensions[3];

        return mouseX >= toggleX && mouseX <= toggleX + toggleWidth &&
               mouseY >= toggleY && mouseY <= toggleY + toggleHeight;
    }

    /**
     * Checks if the mouse is over the second toggle switch.
     *
     * @param mouseX The mouse X coordinate
     * @param mouseY The mouse Y coordinate
     * @return true if the mouse is over the second toggle switch, false otherwise
     */
    private boolean isMouseOverToggleSwitch2(int mouseX, int mouseY) {
        int[] dimensions = getToggleSwitch2Dimensions();
        int toggleX = dimensions[0];
        int toggleY = dimensions[1];
        int toggleWidth = dimensions[2];
        int toggleHeight = dimensions[3];

        return mouseX >= toggleX && mouseX <= toggleX + toggleWidth &&
               mouseY >= toggleY && mouseY <= toggleY + toggleHeight;
    }

    /**
     * Draws the exit bar at the bottom of the app.
     */
    private void drawExitBar(DrawContext context, int mouseX, int mouseY, float delta) {
        try {
            // Calculate exit bar position relative to the phone
            int[] dimensions = getExitBarDimensions();
            int barX = dimensions[0];
            int barY = dimensions[1];
            int baseBarWidth = dimensions[2];
            int barHeight = dimensions[3];



            // Calculate the distance from the original position
            float distance = (float)Math.sqrt(exitBarOffsetX * exitBarOffsetX + exitBarOffsetY * exitBarOffsetY);

            // Calculate the dynamic width based on distance
            float widthPercent = 1.0f - (distance * EXIT_BAR_RESIZE_FACTOR);
            widthPercent = Math.max(EXIT_BAR_MIN_WIDTH_PERCENT, widthPercent);

            // Calculate the actual width
            int barWidth = (int)(baseBarWidth * widthPercent);

            // Center the bar horizontally
            int widthDifference = baseBarWidth - barWidth;
            int barXOffset = widthDifference / 2;

            // Apply the current offset to the bar position
            barX += (int)exitBarOffsetX + barXOffset;
            barY += (int)exitBarOffsetY;

            // Draw the exit bar texture
            context.drawTexture(
                PhoneTextureManager.EXIT_BAR_TEXTURE,
                barX, barY,
                0, 0,
                barWidth, barHeight,
                78, 10 // Original texture dimensions
            );

            // Update physics if the bar is not being dragged
            if (!isExitBarDragging) {
                updateExitBarPhysics(delta);
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error drawing exit bar: " + e.getMessage());
        }
    }

    /**
     * Updates the exit bar physics.
     */
    private void updateExitBarPhysics(float delta) {
        // Apply retention force (pulling back to center)
        exitBarVelocityX -= exitBarOffsetX * EXIT_BAR_RETENTION_FORCE;
        exitBarVelocityY -= exitBarOffsetY * EXIT_BAR_RETENTION_FORCE;

        // Apply friction
        exitBarVelocityX *= EXIT_BAR_FRICTION;
        exitBarVelocityY *= EXIT_BAR_FRICTION;

        // Update position
        exitBarOffsetX += exitBarVelocityX;
        exitBarOffsetY += exitBarVelocityY;

        // Check if the bar has been moved to trigger navigation
        checkExitBarNavigation();
    }

    /**
     * Checks if the exit bar has been moved enough to trigger navigation.
     */
    private void checkExitBarNavigation() {
        // Check if the bar has been moved up enough to go to the main menu
        if (exitBarOffsetY <= -EXIT_BAR_MAX_UP_OFFSET) {
            // Reset the texture manager state before closing
            PhoneTextureManager.getInstance().resetToDefaultTexture();
            this.client.setScreen(new PhoneScreen());
            Pokecobbleclaim.LOGGER.debug("Exit bar moved up, returning to phone main menu");

            // Reset the exit bar state
            resetExitBarState();
        }
    }

    /**
     * Resets the exit bar state.
     */
    private void resetExitBarState() {
        exitBarOffsetX = 0.0f;
        exitBarOffsetY = 0.0f;
        exitBarVelocityX = 0.0f;
        exitBarVelocityY = 0.0f;
        isExitBarDragging = false;
    }

    /**
     * Gets the exit bar dimensions based on the current phone position.
     */
    private int[] getExitBarDimensions() {
        // Calculate bar position relative to the phone
        int barX = phoneX + EXIT_BAR_RELATIVE_X;
        int barY = phoneY + EXIT_BAR_RELATIVE_Y;



        // For the reference screen size, use the exact absolute position
        if (this.width == 480 && this.height == 252) {
            // Calculate the difference between current and reference phone positions
            int phoneDiffX = phoneX - REFERENCE_PHONE_X;
            int phoneDiffY = phoneY - REFERENCE_PHONE_Y;

            // Adjust the absolute position based on the phone position difference
            barX = ABSOLUTE_EXIT_BAR_X + phoneDiffX;
            barY = ABSOLUTE_EXIT_BAR_Y + phoneDiffY;
        }
        return new int[] { barX, barY, EXIT_BAR_WIDTH, EXIT_BAR_HEIGHT };
    }

    /**
     * Checks if the mouse is over the exit bar.
     */
    private boolean isMouseOverExitBar(int mouseX, int mouseY) {
        // Get the exit bar dimensions
        int[] dimensions = getExitBarDimensions();
        int barX = dimensions[0];
        int barY = dimensions[1];
        int barWidth = dimensions[2];
        int barHeight = dimensions[3];

        // Apply the current offset to the bar position
        barX += (int)exitBarOffsetX;
        barY += (int)exitBarOffsetY;

        return mouseX >= barX && mouseX <= barX + barWidth && mouseY >= barY && mouseY <= barY + barHeight;
    }

    /**
     * Draws debug information to help with positioning.
     */
    private void drawDebugInfo(DrawContext context) {
        try {
            // Position debug info on the right side of the screen
            int rightMargin = 10;
            int textX = this.width - 200 - rightMargin;

            // Draw phone boundary
            int outlineColor = 0x40FFFFFF; // Semi-transparent white
            context.drawBorder(phoneX, phoneY, PHONE_WIDTH, PHONE_HEIGHT, outlineColor);

            // Draw current phone position
            String phoneText = String.format("Phone: x=%d, y=%d", phoneX, phoneY);
            context.drawTextWithShadow(this.textRenderer, phoneText, textX, 42, 0xFFFFFFFF);

            // Draw settings app info
            String settingsText = "Settings App Active";
            context.drawTextWithShadow(this.textRenderer, settingsText, textX, 54, 0xFF00FF00);

            // Draw first toggle switch debug info
            int[] toggle1Dimensions = getToggleSwitch1Dimensions();
            if (toggle1Dimensions != null && toggle1Dimensions.length >= 4) {
                int toggle1X = toggle1Dimensions[0];
                int toggle1Y = toggle1Dimensions[1];
                int toggle1Width = toggle1Dimensions[2];
                int toggle1Height = toggle1Dimensions[3];

                // Draw first toggle switch boundary
                context.drawBorder(toggle1X, toggle1Y, toggle1Width, toggle1Height, 0x40FF00FF); // Purple outline

                // Draw first toggle state info
                String toggle1Text = String.format("Toggle1: %s (x=%d, y=%d)",
                                                toggle1State ? "ON" : "OFF", toggle1X, toggle1Y);
                context.drawTextWithShadow(this.textRenderer, toggle1Text, textX, 66, 0xFFFFFF00);
            }

            // Draw second toggle switch debug info
            int[] toggle2Dimensions = getToggleSwitch2Dimensions();
            if (toggle2Dimensions != null && toggle2Dimensions.length >= 4) {
                int toggle2X = toggle2Dimensions[0];
                int toggle2Y = toggle2Dimensions[1];
                int toggle2Width = toggle2Dimensions[2];
                int toggle2Height = toggle2Dimensions[3];

                // Draw second toggle switch boundary
                context.drawBorder(toggle2X, toggle2Y, toggle2Width, toggle2Height, 0x4000FFFF); // Cyan outline

                // Draw second toggle state info
                String toggle2Text = String.format("Toggle2: %s (x=%d, y=%d)",
                                                toggle2State ? "ON" : "OFF", toggle2X, toggle2Y);
                context.drawTextWithShadow(this.textRenderer, toggle2Text, textX, 78, 0xFF00FFFF);
            }

            // Draw exit bar debug info
            int[] exitBarDimensions = getExitBarDimensions();
            if (exitBarDimensions != null && exitBarDimensions.length >= 4) {
                String exitBarText = String.format("Exit Bar: x=%d, y=%d (offset: %.1f, %.1f)",
                                                 exitBarDimensions[0] + (int)exitBarOffsetX,
                                                 exitBarDimensions[1] + (int)exitBarOffsetY,
                                                 exitBarOffsetX, exitBarOffsetY);
                context.drawTextWithShadow(this.textRenderer, exitBarText, textX, 90, 0xFFFFFFFF);
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error drawing debug info: " + e.getMessage());
            // Draw error message instead
            String errorText = "Debug Error: " + e.getMessage();
            context.drawTextWithShadow(this.textRenderer, errorText, 10, 42, 0xFFFF0000);
        }
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Check if the click is on the first toggle switch
        if (isMouseOverToggleSwitch1((int)mouseX, (int)mouseY)) {
            // Toggle the first switch state
            toggle1State = !toggle1State;
            Pokecobbleclaim.LOGGER.debug("Toggle switch 1 clicked, new state: " + (toggle1State ? "ON" : "OFF"));
            return true;
        }

        // Check if the click is on the second toggle switch
        if (isMouseOverToggleSwitch2((int)mouseX, (int)mouseY)) {
            // Toggle the second switch state
            toggle2State = !toggle2State;
            Pokecobbleclaim.LOGGER.debug("Toggle switch 2 clicked, new state: " + (toggle2State ? "ON" : "OFF"));
            return true;
        }

        // Check if the click is on the exit bar
        if (isMouseOverExitBar((int)mouseX, (int)mouseY)) {
            isExitBarDragging = true;
            exitBarDragStartX = (int)mouseX;
            exitBarDragStartY = (int)mouseY;
            Pokecobbleclaim.LOGGER.debug("Started dragging exit bar at (" + mouseX + ", " + mouseY + ")");
            return true;
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        // Handle exit bar dragging
        if (isExitBarDragging && button == 0) {
            // Calculate the new offset based on the drag
            float newOffsetX = exitBarOffsetX + (float)deltaX;
            float newOffsetY = exitBarOffsetY + (float)deltaY;

            // Limit the offset to the maximum allowed values (same as BankAppScreen)
            newOffsetX = Math.max(-EXIT_BAR_MAX_SIDE_OFFSET, Math.min(EXIT_BAR_MAX_SIDE_OFFSET, newOffsetX));
            // Allow more movement upward than downward
            newOffsetY = Math.max(-EXIT_BAR_MAX_UP_OFFSET, Math.min(EXIT_BAR_MAX_UP_OFFSET / 4, newOffsetY));

            // Update the offset
            exitBarOffsetX = newOffsetX;
            exitBarOffsetY = newOffsetY;

            // Update the velocity based on the drag
            exitBarVelocityX = (float)deltaX * 0.5f;
            exitBarVelocityY = (float)deltaY * 0.5f;

            return true;
        }

        return super.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
    }

    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        // Handle exit bar release
        if (isExitBarDragging && button == 0) {
            isExitBarDragging = false;
            Pokecobbleclaim.LOGGER.debug("Exit bar drag released at (" + mouseX + ", " + mouseY + ")");

            // Check if the bar has been moved enough to trigger navigation
            checkExitBarNavigation();

            return true;
        }

        return super.mouseReleased(mouseX, mouseY, button);
    }

    @Override
    public boolean shouldPause() {
        // Don't pause the game when the settings app is open
        return false;
    }

    @Override
    public boolean shouldCloseOnEsc() {
        // Close the settings app when ESC is pressed
        return true;
    }

    @Override
    public void close() {
        // Reset the texture manager state before closing
        PhoneTextureManager.getInstance().resetToDefaultTexture();
        super.close();
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        // Close the phone when F key is pressed
        if (keyCode == GLFW.GLFW_KEY_F) {
            // Reset the texture manager state before closing
            PhoneTextureManager.getInstance().resetToDefaultTexture();
            this.close();
            return true;
        }

        // Open the shape visualizer when V key is pressed (only in dev mode)
        if (keyCode == GLFW.GLFW_KEY_V) {
            if (isDevModeEnabled()) {
                Pokecobbleclaim.LOGGER.debug("V key pressed on settings app screen, opening shape visualizer");
                ShapeVisualizerTool.toggleShapeVisualizer();
                return true;
            } else {
                Pokecobbleclaim.LOGGER.debug("V key pressed but dev mode is not enabled");
            }
        }

        return super.keyPressed(keyCode, scanCode, modifiers);
    }
}
