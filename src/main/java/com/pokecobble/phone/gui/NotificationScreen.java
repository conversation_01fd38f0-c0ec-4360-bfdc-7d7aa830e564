package com.pokecobble.phone.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.PhoneTextureManager;
import com.pokecobble.phone.app.App;
import com.pokecobble.phone.app.AppRegistry;
import com.pokecobble.phone.notification.PhoneNotification;
import com.pokecobble.phone.notification.PhoneNotificationManager;
import com.pokecobble.town.client.InviteNotification;
import com.pokecobble.town.gui.TownScreenManager;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;
import net.minecraft.util.Identifier;

import java.util.List;

/**
 * Screen that displays the notification interface using the notif.png texture.
 */
public class NotificationScreen extends Screen {

    // Phone dimensions (should match other phone screens)
    private static final int PHONE_WIDTH = 100;
    private static final int PHONE_HEIGHT = 160;

    // App grid constants (matching PhoneScreen)
    private static final int GRID_ROWS = 6;
    private static final int GRID_COLS = 4;
    private static final int APP_SIZE = 16;
    private static final int APP_SPACING_HORIZONTAL = 6;
    private static final int APP_SPACING_VERTICAL = 2;
    private static final int GRID_START_X = 10;
    private static final int GRID_START_Y = 30;

    // Screen positioning
    private int phoneX;
    private int phoneY;

    // Mouse interaction
    private boolean wasMouseDown = false;

    // Debug counter
    private int renderCount = 0;

    public NotificationScreen() {
        super(Text.literal("Notification Screen"));
    }

    @Override
    protected void init() {
        super.init();

        // Calculate phone position (centered on screen, matching other phone screens)
        this.phoneX = (this.width - PHONE_WIDTH) / 2;
        this.phoneY = (this.height - PHONE_HEIGHT) / 2;

        Pokecobbleclaim.LOGGER.info("Initialized notification screen at position: " + phoneX + ", " + phoneY + " (screen size: " + this.width + "x" + this.height + ")");
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        renderCount++;
        if (renderCount % 60 == 1) { // Log every 60 frames (once per second at 60fps)
            Pokecobbleclaim.LOGGER.info("NotificationScreen render called - frame " + renderCount);
        }

        // Skip calling super.render() to avoid the dark background

        // First, draw the phone background (default phone texture)
        Identifier phoneTexture = PhoneTextureManager.getInstance().getCurrentTexture();
        if (phoneTexture != null) {
            context.drawTexture(phoneTexture, phoneX, phoneY, 0, 0,
                              PHONE_WIDTH, PHONE_HEIGHT, PHONE_WIDTH, PHONE_HEIGHT);
            Pokecobbleclaim.LOGGER.debug("Rendered phone background texture: " + phoneTexture);
        } else {
            Pokecobbleclaim.LOGGER.warn("Phone texture is null!");
        }

        // Draw the app grid on top of the phone background
        drawAppGrid(context);

        // Finally, draw the notification overlay on top of everything
        Identifier notificationTexture = PhoneTextureManager.NOTIFICATION_SCREEN_TEXTURE;
        if (renderCount % 60 == 1) { // Log once per second
            Pokecobbleclaim.LOGGER.info("Drawing notification overlay - texture: " + notificationTexture + ", position: " + phoneX + "," + phoneY + ", size: " + PHONE_WIDTH + "x" + PHONE_HEIGHT);
        }

        if (notificationTexture != null) {
            try {
                // Draw the notification texture scaled to phone dimensions
                context.drawTexture(notificationTexture, phoneX, phoneY, 0, 0,
                                  PHONE_WIDTH, PHONE_HEIGHT, 442, 610);
                if (renderCount % 60 == 1) {
                    Pokecobbleclaim.LOGGER.info("Successfully rendered notification overlay texture");
                }
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Failed to render notification texture: " + e.getMessage());
                // Fallback: draw a semi-transparent red rectangle to show where the notification should be
                context.fill(phoneX, phoneY, phoneX + PHONE_WIDTH, phoneY + PHONE_HEIGHT, 0x80FF0000);
            }
        } else {
            Pokecobbleclaim.LOGGER.error("Notification texture is null!");
            // Fallback: draw a semi-transparent blue rectangle to show where the notification should be
            context.fill(phoneX, phoneY, phoneX + PHONE_WIDTH, phoneY + PHONE_HEIGHT, 0x800000FF);
        }

        // Also draw a visible test overlay to make sure rendering is working
        if (renderCount < 300) { // Show for first 5 seconds
            context.fill(phoneX + 10, phoneY + 10, phoneX + 30, phoneY + 30, 0xFFFF0000); // Red square
        }

        // Handle mouse interaction
        handleMouseInteraction(mouseX, mouseY);
    }

    /**
     * Draws the app grid on the phone.
     */
    private void drawAppGrid(DrawContext context) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client == null) return;

        List<App> apps = AppRegistry.getInstance().getAllApps();
        int appIndex = 0;

        for (int row = 0; row < GRID_ROWS; row++) {
            for (int col = 0; col < GRID_COLS; col++) {
                if (appIndex < apps.size()) {
                    App app = apps.get(appIndex);

                    // Calculate app position (matching PhoneScreen layout)
                    int appX = phoneX + GRID_START_X + col * (APP_SIZE + APP_SPACING_HORIZONTAL);
                    int appY = phoneY + GRID_START_Y + row * (APP_SIZE + APP_SPACING_VERTICAL + 8); // Extra space for app name

                    // Draw app icon
                    context.drawTexture(app.getIconTexture(), appX, appY, 0, 0, APP_SIZE, APP_SIZE, APP_SIZE, APP_SIZE);

                    // Draw app name - with smaller font (matching PhoneScreen)
                    String appName = app.getName();
                    int textWidth = client.textRenderer.getWidth(appName) / 3; // Account for scale
                    int textX = appX + (APP_SIZE - textWidth) / 2;
                    int textY = appY + APP_SIZE + 1;

                    // Scale down the text
                    context.getMatrices().push();
                    context.getMatrices().scale(0.33f, 0.33f, 1.0f);
                    context.drawText(client.textRenderer, appName, (int)(textX / 0.33f), (int)(textY / 0.33f), 0xFFFFFF, false);
                    context.getMatrices().pop();

                    appIndex++;
                }
            }
        }
    }

    /**
     * Handles mouse interaction with the notification screen.
     */
    private void handleMouseInteraction(int mouseX, int mouseY) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client == null) return;

        boolean isMouseDown = client.mouse.wasLeftButtonClicked();

        // Check if mouse is over the phone area
        if (mouseX >= phoneX && mouseX <= phoneX + PHONE_WIDTH &&
            mouseY >= phoneY && mouseY <= phoneY + PHONE_HEIGHT) {

            // Check if notification was clicked
            if (isMouseDown && !wasMouseDown) {
                handleNotificationClick(mouseX - phoneX, mouseY - phoneY);
            }
        }

        wasMouseDown = isMouseDown;
    }

    /**
     * Handles clicking on the notification screen.
     */
    private void handleNotificationClick(int relativeX, int relativeY) {
        Pokecobbleclaim.LOGGER.info("Notification screen clicked at: " + relativeX + ", " + relativeY);

        // Get the current town invite notification
        List<PhoneNotification> notifications = PhoneNotificationManager.getInstance().getNotifications();
        PhoneNotification townInvite = null;

        for (PhoneNotification notification : notifications) {
            if (notification.isTownInvite()) {
                townInvite = notification;
                break;
            }
        }

        if (townInvite != null) {
            // Set the pending invite for the town screen to handle
            try {
                java.util.UUID townId = java.util.UUID.fromString(townInvite.getTownId());
                InviteNotification.setPendingInvite(townId, townInvite.getTownName());

                // Close the notification screen and open the town screen
                closeNotificationScreen();
                TownScreenManager.openTownScreen();

                Pokecobbleclaim.LOGGER.info("Opening town screen for invite: " + townInvite.getTownName());
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling notification click: " + e.getMessage());
                // Just close the notification screen if there's an error
                closeNotificationScreen();
            }
        } else {
            // No town invite found, just close the notification screen
            closeNotificationScreen();
        }
    }

    /**
     * Closes the notification screen and returns to the default phone view.
     */
    private void closeNotificationScreen() {
        // Clear the notification
        PhoneNotificationManager.getInstance().clearAllNotifications();

        // Close this screen
        MinecraftClient client = MinecraftClient.getInstance();
        if (client != null) {
            client.setScreen(null);
        }

        Pokecobbleclaim.LOGGER.debug("Closed notification screen");
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        // Handle escape key to close the notification
        if (keyCode == 256) { // GLFW.GLFW_KEY_ESCAPE
            closeNotificationScreen();
            return true;
        }

        // Handle F key to close the notification (same as phone key)
        if (keyCode == org.lwjgl.glfw.GLFW.GLFW_KEY_F) {
            closeNotificationScreen();
            return true;
        }

        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    @Override
    public boolean shouldPause() {
        // Don't pause the game when this screen is open
        return false;
    }

    @Override
    public void close() {
        closeNotificationScreen();
        super.close();
    }
}
