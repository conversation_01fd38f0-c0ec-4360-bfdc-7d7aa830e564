package com.pokecobble.phone.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.app.App;
import com.pokecobble.phone.app.AppRegistry;
import com.pokecobble.phone.PhoneTextureManager;
import com.pokecobble.phone.notification.PhoneNotificationOverlay;
import net.fabricmc.fabric.api.client.rendering.v1.HudRenderCallback;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.util.Identifier;

import java.util.List;

/**
 * Renders the phone on the HUD.
 */
public class PhoneRenderer {
    private static final PhoneRenderer INSTANCE = new PhoneRenderer();
    private static final MinecraftClient client = MinecraftClient.getInstance();

    // Phone dimensions - these should match the texture dimensions

    private static final int PHONE_WIDTH = 100;
    private static final int PHONE_HEIGHT = 160;

    // App grid dimensions
    private static final int GRID_COLS = 4;
    private static final int GRID_ROWS = 6;
    private static final int APP_SIZE = 16;
    private static final int APP_SPACING = 2;
    private static final int GRID_START_X = 8;
    private static final int GRID_START_Y = 30;

    // Animation variables
    private boolean visible = false;
    private boolean animating = false;
    private long animationStartTime = 0;
    private static final float ANIMATION_DURATION = 300; // milliseconds

    // Mouse interaction
    private boolean wasMouseDown = false;
    private App hoveredApp = null;

    /**
     * Gets the phone renderer instance.
     *
     * @return The phone renderer instance
     */
    public static PhoneRenderer getInstance() {
        return INSTANCE;
    }

    /**
     * Registers the phone renderer.
     */
    public static void register() {
        Pokecobbleclaim.LOGGER.info("Registering phone renderer");

        // Register the HUD render callback
        HudRenderCallback.EVENT.register((context, tickDelta) -> {
            getInstance().render(context, tickDelta);
        });
    }

    /**
     * Renders the phone on the HUD.
     *
     * @param context The draw context
     * @param tickDelta The tick delta
     */
    public void render(DrawContext context, float tickDelta) {
        if (!visible && !animating) {
            return;
        }

        // Calculate animation progress
        float animationProgress = calculateAnimationProgress();

        // Calculate phone position
        int screenWidth = client.getWindow().getScaledWidth();
        int screenHeight = client.getWindow().getScaledHeight();

        int phoneX = screenWidth - PHONE_WIDTH - 10;
        int phoneTargetY = screenHeight - PHONE_HEIGHT;
        int phoneStartY = screenHeight + 20;

        int phoneY = phoneStartY - (int)((phoneStartY - phoneTargetY) * animationProgress);

        // Get the current phone texture from the texture manager
        Identifier phoneTexture = PhoneTextureManager.getInstance().getCurrentTexture();

        // Make sure the texture is not null before drawing
        if (phoneTexture != null) {
            // Draw phone background
            context.drawTexture(phoneTexture, phoneX, phoneY, 0, 0, PHONE_WIDTH, PHONE_HEIGHT, PHONE_WIDTH, PHONE_HEIGHT);
        } else {
            Pokecobbleclaim.LOGGER.error("Phone texture is null, cannot render phone");
        }

        // Only draw app grid if not in app mode
        if (!PhoneTextureManager.getInstance().isInAppMode()) {
            drawAppGrid(context, phoneX, phoneY);
        }

        // Handle mouse interaction
        handleMouseInteraction(phoneX, phoneY);

        // When phone is visible, we'll handle mouse input in the PhoneMixin
        // This makes it behave like a screen without actually pausing the game
    }

    /**
     * Draws the app grid.
     *
     * @param context The draw context
     * @param phoneX The phone X position
     * @param phoneY The phone Y position
     */
    private void drawAppGrid(DrawContext context, int phoneX, int phoneY) {
        List<App> apps = AppRegistry.getInstance().getAllApps();
        int appIndex = 0;

        for (int row = 0; row < GRID_ROWS; row++) {
            for (int col = 0; col < GRID_COLS; col++) {
                if (appIndex < apps.size()) {
                    App app = apps.get(appIndex);

                    // Calculate app position
                    int appX = phoneX + GRID_START_X + col * (APP_SIZE + APP_SPACING);
                    int appY = phoneY + GRID_START_Y + row * (APP_SIZE + APP_SPACING + 8); // Extra space for app name

                    // Draw app icon
                    context.drawTexture(app.getIconTexture(), appX, appY, 0, 0, APP_SIZE, APP_SIZE, APP_SIZE, APP_SIZE);

                    // Draw app name - with even smaller font
                    String appName = app.getName();
                    int textWidth = client.textRenderer.getWidth(appName) / 3; // Account for scale
                    int textX = appX + (APP_SIZE - textWidth) / 2;
                    int textY = appY + APP_SIZE + 1;

                    // Draw text at one-third scale
                    context.getMatrices().push();
                    context.getMatrices().scale(0.33f, 0.33f, 1.0f);
                    context.drawText(client.textRenderer, appName, (int)(textX * 3), (int)(textY * 3), 0xFFFFFFFF, true);
                    context.getMatrices().pop();

                    appIndex++;
                }
            }
        }
    }

    /**
     * Handles mouse interaction with the phone.
     *
     * @param phoneX The phone X position
     * @param phoneY The phone Y position
     */
    private void handleMouseInteraction(int phoneX, int phoneY) {
        if (client.currentScreen != null) {
            return; // Don't handle mouse interaction when a screen is open
        }

        // Get scaled mouse coordinates
        int mouseX = (int)client.mouse.getX() * client.getWindow().getScaledWidth() / client.getWindow().getWidth();
        int mouseY = (int)client.mouse.getY() * client.getWindow().getScaledHeight() / client.getWindow().getHeight();

        boolean isMouseDown = client.mouse.wasLeftButtonClicked();

        // If in app mode, don't handle mouse interactions here
        // Let the app screen handle them instead
        if (PhoneTextureManager.getInstance().isInAppMode()) {
            wasMouseDown = isMouseDown;
            return;
        }

        // Check if mouse is over an app
        hoveredApp = null;
        List<App> apps = AppRegistry.getInstance().getAllApps();
        int appIndex = 0;

        for (int row = 0; row < GRID_ROWS; row++) {
            for (int col = 0; col < GRID_COLS; col++) {
                if (appIndex < apps.size()) {
                    App app = apps.get(appIndex);

                    // Calculate app position
                    int appX = phoneX + GRID_START_X + col * (APP_SIZE + APP_SPACING);
                    int appY = phoneY + GRID_START_Y + row * (APP_SIZE + APP_SPACING + 8);

                    // Check if mouse is over this app
                    if (mouseX >= appX && mouseX <= appX + APP_SIZE &&
                        mouseY >= appY && mouseY <= appY + APP_SIZE) {
                        hoveredApp = app;

                        // Check if app was clicked
                        if (isMouseDown && !wasMouseDown) {
                            app.open(client);
                        }
                    }

                    appIndex++;
                }
            }
        }

        wasMouseDown = isMouseDown;
    }

    /**
     * Checks if a click is inside the phone area.
     *
     * @param mouseX The mouse X coordinate
     * @param mouseY The mouse Y coordinate
     * @param phoneX The phone X position
     * @param phoneY The phone Y position
     * @return true if the click is inside the phone, false otherwise
     */
    private boolean isClickInsidePhone(int mouseX, int mouseY, int phoneX, int phoneY) {
        return mouseX >= phoneX && mouseX <= phoneX + PHONE_WIDTH &&
               mouseY >= phoneY && mouseY <= phoneY + PHONE_HEIGHT;
    }

    /**
     * Calculates the animation progress.
     *
     * @return The animation progress (0.0 to 1.0)
     */
    private float calculateAnimationProgress() {
        if (!animating) {
            return visible ? 1.0f : 0.0f;
        }

        long currentTime = System.currentTimeMillis();
        float progress = (currentTime - animationStartTime) / ANIMATION_DURATION;

        if (progress >= 1.0f) {
            animating = false;
            progress = visible ? 1.0f : 0.0f;
        } else if (!visible) {
            progress = 1.0f - progress; // Reverse progress for hiding animation
        }

        return progress;
    }

    /**
     * Sets whether the phone is visible.
     *
     * @param visible Whether the phone is visible
     */
    public void setVisible(boolean visible) {
        if (this.visible != visible) {
            this.visible = visible;
            this.animating = true;
            this.animationStartTime = System.currentTimeMillis();
        }
    }

    /**
     * Toggles the visibility of the phone.
     */
    public void toggleVisibility() {
        setVisible(!visible);
    }

    /**
     * Checks if the phone is visible.
     *
     * @return Whether the phone is visible
     */
    public boolean isVisible() {
        return visible;
    }
}
