package com.pokecobble.phone.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.DevModeManager;
import com.pokecobble.phone.PhoneTextureManager;
import com.pokecobble.phone.gui.PhoneScreen;
import com.pokecobble.tools.gui.ShapeVisualizerTool;
import com.pokecobble.tools.gui.ShapeVisualizerUtil;
import com.pokecobble.town.client.NotificationRenderer;
import com.pokecobble.town.logging.ErrorLogger;
import com.pokecobble.town.network.money.MoneyNetworkHandler;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.client.network.PlayerListEntry;
import net.minecraft.text.Text;
import net.minecraft.util.Identifier;
import org.lwjgl.glfw.GLFW;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * The bank app screen that displays the bank interface and handles interactions.
 */
public class BankAppScreen extends Screen {
    // Phone dimensions - these should match the texture dimensions
    private static final int PHONE_WIDTH = 100;
    private static final int PHONE_HEIGHT = 160;

    // Phone position
    private int phoneX;
    private int phoneY;

    // Balance display position and dimensions
    private static final int BALANCE_RELATIVE_X = 16; // X position relative to phone
    private static final int BALANCE_RELATIVE_Y = 46; // Y position relative to phone
    private static final int BALANCE_WIDTH = 58; // Width of the element
    private static final int BALANCE_HEIGHT = 10; // Height of the element

    // Player list position and dimensions (as specified in the requirements)
    private static final int PLAYER_LIST_RELATIVE_X = 15; // X position relative to phone
    private static final int PLAYER_LIST_RELATIVE_Y = 57; // Y position relative to phone
    private static final int PLAYER_LIST_WIDTH = 73; // Width of the element
    private static final int PLAYER_LIST_HEIGHT = 60; // Height of the element

    // Reference phone position when these coordinates were captured
    private static final int PLAYER_LIST_REFERENCE_PHONE_X = 370; // Reference phone X position
    private static final int PLAYER_LIST_REFERENCE_PHONE_Y = 103; // Reference phone Y position

    // Absolute position on screen
    private static final int PLAYER_LIST_ABSOLUTE_X = 385; // Absolute X position on screen
    private static final int PLAYER_LIST_ABSOLUTE_Y = 160; // Absolute Y position on screen

    // Typing zone position and dimensions (as specified in the requirements)
    private static final int TYPING_ZONE_RELATIVE_X = 20; // X position relative to phone
    private static final int TYPING_ZONE_RELATIVE_Y = 26; // Y position relative to phone
    private static final int TYPING_ZONE_WIDTH = 54; // Width of the element
    private static final int TYPING_ZONE_HEIGHT = 12; // Height of the element

    // Reference phone position when these coordinates were captured
    private static final int TYPING_ZONE_REFERENCE_PHONE_X = 370; // Reference phone X position
    private static final int TYPING_ZONE_REFERENCE_PHONE_Y = 103; // Reference phone Y position

    // Absolute position on screen
    private static final int TYPING_ZONE_ABSOLUTE_X = 390; // Absolute X position on screen
    private static final int TYPING_ZONE_ABSOLUTE_Y = 129; // Absolute Y position on screen

    // Player list UI constants
    private static final int PLAYER_ENTRY_HEIGHT = 12; // Height of each player entry
    private static final int PLAYER_LIST_BACKGROUND_COLOR = 0x80000000; // Semi-transparent black
    private static final int PLAYER_LIST_BORDER_COLOR = 0x80FFFFFF; // Semi-transparent white
    private static final int PLAYER_LIST_SELECTED_COLOR = 0x8000FF00; // Semi-transparent green
    private static final int PLAYER_LIST_HOVER_COLOR = 0x800000FF; // Semi-transparent blue

    // Reference position for balance display
    private static final int REFERENCE_BALANCE_PHONE_X = 370; // Reference phone X position
    private static final int REFERENCE_BALANCE_PHONE_Y = 103; // Reference phone Y position

    // Absolute position for balance display
    private static final int ABSOLUTE_BALANCE_X = 386; // Absolute X position on screen
    private static final int ABSOLUTE_BALANCE_Y = 149; // Absolute Y position on screen

    // Transfer button dimensions - relative to the phone texture
    // These coordinates are relative to the phone's top-left corner
    // These values were determined using the enhanced shape visualizer tool
    private static final int TRANSFER_BUTTON_RELATIVE_X = 15; // X position relative to phone
    private static final int TRANSFER_BUTTON_RELATIVE_Y = 132; // Y position relative to phone
    private static final int TRANSFER_BUTTON_WIDTH = 72; // Width of the button
    private static final int TRANSFER_BUTTON_HEIGHT = 14; // Height of the button

    // Absolute position for the button (used when the phone is at its default position)
    // These are the exact coordinates specified by the user
    private static final int ABSOLUTE_BUTTON_X = 385; // Absolute X position on screen
    private static final int ABSOLUTE_BUTTON_Y = 224; // Absolute Y position on screen

    // Reference phone position from the shape visualizer
    // This is the phone position when the absolute coordinates were captured
    private static final int REFERENCE_PHONE_X = 370; // Reference phone X position
    private static final int REFERENCE_PHONE_Y = 92;  // Reference phone Y position

    // Transfer button on transfer page (as specified by user)
    private static final int TRANSFER_PAGE_BUTTON_RELATIVE_X = 17; // X position relative to phone
    private static final int TRANSFER_PAGE_BUTTON_RELATIVE_Y = 133; // Y position relative to phone
    private static final int TRANSFER_PAGE_BUTTON_WIDTH = 67; // Width of the button
    private static final int TRANSFER_PAGE_BUTTON_HEIGHT = 12; // Height of the button

    // Reference phone position when these coordinates were captured
    private static final int TRANSFER_PAGE_REFERENCE_PHONE_X = 370; // Reference phone X position
    private static final int TRANSFER_PAGE_REFERENCE_PHONE_Y = 103; // Reference phone Y position

    // Absolute position on screen
    private static final int TRANSFER_PAGE_ABSOLUTE_BUTTON_X = 387; // Absolute X position on screen
    private static final int TRANSFER_PAGE_ABSOLUTE_BUTTON_Y = 236; // Absolute Y position on screen

    // Exit bar dimensions and position
    private static final int EXIT_BAR_WIDTH = 60; // Reduced width from 78 to 60
    private static final int EXIT_BAR_HEIGHT = 10;
    private static final int EXIT_BAR_RELATIVE_X = 21; // Centered horizontally (was 12)
    private static final int EXIT_BAR_RELATIVE_Y = 152; // Moved down more (was 148)

    // Exit bar absolute position (for reference)
    private static final int ABSOLUTE_EXIT_BAR_X = 391; // Adjusted for new relative X (was 382)
    private static final int ABSOLUTE_EXIT_BAR_Y = 255; // Adjusted for new relative Y (was 251)

    // Exit bar physics parameters
    private static final float EXIT_BAR_RETENTION_FORCE = 0.15f; // Force pulling the bar back to center
    private static final float EXIT_BAR_FRICTION = 0.9f; // Friction to slow down movement
    private static final float EXIT_BAR_MAX_UP_OFFSET = 30.0f; // Maximum upward movement
    private static final float EXIT_BAR_MAX_SIDE_OFFSET = 15.0f; // Maximum side movement

    // Dynamic resizing parameters
    private static final float EXIT_BAR_MIN_WIDTH_PERCENT = 0.7f; // Minimum width as percentage of original (70%)
    private static final float EXIT_BAR_RESIZE_FACTOR = 0.015f; // How quickly the bar resizes with distance

    // Exit bar state
    private boolean isExitBarDragging = false;
    private float exitBarOffsetX = 0.0f; // Current X offset from original position
    private float exitBarOffsetY = 0.0f; // Current Y offset from original position
    private float exitBarVelocityX = 0.0f; // Current X velocity
    private float exitBarVelocityY = 0.0f; // Current Y velocity
    private int exitBarDragStartX;
    private int exitBarDragStartY;

    /**
     * Creates a new bank app screen.
     */
    public BankAppScreen() {
        super(Text.literal("Bank App"));
    }

    @Override
    protected void init() {
        super.init();

        // Calculate phone position (bottom right)
        phoneX = this.width - PHONE_WIDTH - 10;
        phoneY = this.height - PHONE_HEIGHT;

        // Reset the button position logging flag when the screen is initialized
        buttonPositionLogged = false;

        // Log screen dimensions for debugging using debug level to avoid spam
        Pokecobbleclaim.LOGGER.debug("Screen dimensions: width=" + this.width + ", height=" + this.height);
        Pokecobbleclaim.LOGGER.debug("Phone position: x=" + phoneX + ", y=" + phoneY);

        // Request balance from server and start watching for changes
        MoneyNetworkHandler.requestBalance();
        MoneyNetworkHandler.startWatchingBalance();

        // Initialize typing zone
        initTypingZone();

        // Check if we're at the reference screen size
        if (this.width == 480 && this.height == 252) {
            Pokecobbleclaim.LOGGER.debug("Running at reference screen size (480x252)");
        } else {
            Pokecobbleclaim.LOGGER.debug("Not at reference screen size. Reference: 480x252, Current: " +
                                       this.width + "x" + this.height);
        }

        // Log the expected button position
        int expectedButtonX = phoneX + TRANSFER_BUTTON_RELATIVE_X;
        int expectedButtonY = phoneY + TRANSFER_BUTTON_RELATIVE_Y;
        Pokecobbleclaim.LOGGER.debug("Expected button position: x=" + expectedButtonX +
                                   ", y=" + expectedButtonY +
                                   " (relative to phone: x=" + TRANSFER_BUTTON_RELATIVE_X +
                                   ", y=" + TRANSFER_BUTTON_RELATIVE_Y + ")");

        // Log reference phone position
        Pokecobbleclaim.LOGGER.debug("Reference phone position: x=" + REFERENCE_PHONE_X +
                                   ", y=" + REFERENCE_PHONE_Y +
                                   " (current phone position: x=" + phoneX +
                                   ", y=" + phoneY + ")");

        // Calculate the difference between expected and absolute positions
        if (this.width == ShapeVisualizerUtil.BASE_SCREEN_WIDTH &&
            this.height == ShapeVisualizerUtil.BASE_SCREEN_HEIGHT) {
            int diffX = ABSOLUTE_BUTTON_X - expectedButtonX;
            int diffY = ABSOLUTE_BUTTON_Y - expectedButtonY;
            Pokecobbleclaim.LOGGER.debug("Difference between expected and absolute positions: x=" +
                                       diffX + ", y=" + diffY);

            // Calculate the difference between current and reference phone positions
            int phoneDiffX = phoneX - REFERENCE_PHONE_X;
            int phoneDiffY = phoneY - REFERENCE_PHONE_Y;
            Pokecobbleclaim.LOGGER.debug("Difference between current and reference phone positions: x=" +
                                       phoneDiffX + ", y=" + phoneDiffY);
        }
    }

    @Override
    public void renderBackground(DrawContext context) {
        // Don't render the standard dark background
    }

    // Debug info is only shown when dev mode is enabled
    private boolean isDevModeEnabled() {
        MinecraftClient client = MinecraftClient.getInstance();
        return client.player != null && DevModeManager.getInstance().isDevModeEnabled(client.player);
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Skip calling super.render() to avoid the dark background
        // Instead, just render our phone UI

        // Get the current phone texture from the texture manager
        Identifier phoneTexture = PhoneTextureManager.getInstance().getCurrentTexture();

        // Make sure the texture is not null before drawing
        if (phoneTexture != null) {
            // Draw phone background
            context.drawTexture(phoneTexture, phoneX, phoneY, 0, 0, PHONE_WIDTH, PHONE_HEIGHT, PHONE_WIDTH, PHONE_HEIGHT);

            // Call super.render() to ensure all widgets are rendered properly
            super.render(context, mouseX, mouseY, delta);

            // Render our custom text for the typing zone if we're on the transfer page
            if (phoneTexture == PhoneTextureManager.BANK_APP_TRANSFER_TEXTURE && typingZone != null && typingZone.isVisible()) {
                renderTypingZoneText(context);
            }

            // Draw player balance if we're on the home screen
            if (phoneTexture == PhoneTextureManager.BANK_APP_HOME_TEXTURE) {
                drawPlayerBalance(context);

                // Hide typing zone on home screen
                if (typingZone != null) {
                    typingZone.setVisible(false);
                }
            }

            // Draw player list if we're on the transfer page
            if (phoneTexture == PhoneTextureManager.BANK_APP_TRANSFER_TEXTURE) {
                // Initialize player list if needed
                if (!isPlayerListInitialized) {
                    updatePlayerList();
                    isPlayerListInitialized = true;

                    // Show help message for dev mode users
                    if (isDevModeEnabled()) {
                        MinecraftClient client = MinecraftClient.getInstance();
                        if (client.player != null) {
                            client.player.sendMessage(Text.literal("§6Dev Mode Commands:§r T = Add test players, M = Add many players, C = Clear list"), false);
                        }
                    }
                }

                // Draw the player list
                drawPlayerList(context, mouseX, mouseY);

                // Show typing zone on transfer page
                if (typingZone != null) {
                    typingZone.setVisible(true);
                    typingZone.setFocused(true); // Set focus to the typing zone

                    // Set this as the focused element for the screen
                    // This ensures the text field is focused without clicking
                    this.setFocused(typingZone);
                }
            }
        } else {
            Pokecobbleclaim.LOGGER.error("Phone texture is null, cannot render phone");
        }

        // Draw the transfer button
        drawTransferButton(context, mouseX, mouseY);

        // Draw the exit bar
        drawExitBar(context, mouseX, mouseY, delta);

        // Draw debug information if dev mode is enabled
        if (isDevModeEnabled()) {
            drawDebugInfo(context);
        }
    }

    /**
     * Draws the exit bar at the bottom of the app.
     *
     * @param context The draw context
     * @param mouseX The mouse X coordinate
     * @param mouseY The mouse Y coordinate
     * @param delta The time delta since the last frame
     */
    private void drawExitBar(DrawContext context, int mouseX, int mouseY, float delta) {
        // Get the current texture
        Identifier currentTexture = PhoneTextureManager.getInstance().getCurrentTexture();

        // Calculate exit bar position relative to the phone
        int[] dimensions = getExitBarDimensions();
        int barX = dimensions[0];
        int barY = dimensions[1];
        int baseBarWidth = dimensions[2];
        int barHeight = dimensions[3];

        // Calculate the distance from the original position
        float distance = (float)Math.sqrt(exitBarOffsetX * exitBarOffsetX + exitBarOffsetY * exitBarOffsetY);

        // Calculate the dynamic width based on distance
        // The further from the original position, the smaller the bar gets
        float widthPercent = 1.0f - (distance * EXIT_BAR_RESIZE_FACTOR);

        // Ensure the width doesn't go below the minimum
        widthPercent = Math.max(EXIT_BAR_MIN_WIDTH_PERCENT, widthPercent);

        // Calculate the actual width
        int barWidth = (int)(baseBarWidth * widthPercent);

        // Center the bar horizontally
        int widthDifference = baseBarWidth - barWidth;
        int barXOffset = widthDifference / 2;

        // Apply the current offset to the bar position
        barX += (int)exitBarOffsetX + barXOffset;
        barY += (int)exitBarOffsetY;

        // Draw the exit bar texture
        // The texture is 78x10, but we're displaying it at a dynamic width x 10
        context.drawTexture(
            PhoneTextureManager.EXIT_BAR_TEXTURE,
            barX, barY,
            0, 0,
            barWidth, barHeight,
            78, 10 // Original texture dimensions
        );

        // Update physics if the bar is not being dragged
        if (!isExitBarDragging) {
            updateExitBarPhysics(delta);
        }
    }

    /**
     * Updates the exit bar physics.
     *
     * @param delta The time delta since the last frame
     */
    private void updateExitBarPhysics(float delta) {
        // Apply retention force (pulling back to center)
        exitBarVelocityX -= exitBarOffsetX * EXIT_BAR_RETENTION_FORCE;
        exitBarVelocityY -= exitBarOffsetY * EXIT_BAR_RETENTION_FORCE;

        // Apply friction
        exitBarVelocityX *= EXIT_BAR_FRICTION;
        exitBarVelocityY *= EXIT_BAR_FRICTION;

        // Update position
        exitBarOffsetX += exitBarVelocityX;
        exitBarOffsetY += exitBarVelocityY;

        // Check if the bar has been moved to trigger navigation
        checkExitBarNavigation();
    }

    /**
     * Checks if the exit bar has been moved enough to trigger navigation.
     */
    private void checkExitBarNavigation() {
        // Get the current texture
        Identifier currentTexture = PhoneTextureManager.getInstance().getCurrentTexture();

        // Check if the bar has been moved up enough to go to the main menu
        if (exitBarOffsetY <= -EXIT_BAR_MAX_UP_OFFSET) {
            // Reset the texture manager state before closing
            PhoneTextureManager.getInstance().resetToDefaultTexture();
            this.client.setScreen(new PhoneScreen());
            Pokecobbleclaim.LOGGER.debug("Exit bar moved up, returning to phone main menu");

            // Reset the exit bar state
            resetExitBarState();
        }
        // Check if the bar has been moved left or right enough to go back
        else if (Math.abs(exitBarOffsetX) >= EXIT_BAR_MAX_SIDE_OFFSET) {
            // If we're on the transfer page, go back to the home screen
            if (currentTexture == PhoneTextureManager.BANK_APP_TRANSFER_TEXTURE) {
                PhoneTextureManager.getInstance().openBankApp();
                Pokecobbleclaim.LOGGER.debug("Exit bar moved sideways, returning to bank home page");

                // Clear typing zone when exiting transfer page
                clearTypingZone();

                // Reset the exit bar state
                resetExitBarState();
            }
        }
    }

    /**
     * Resets the exit bar state.
     */
    private void resetExitBarState() {
        exitBarOffsetX = 0.0f;
        exitBarOffsetY = 0.0f;
        exitBarVelocityX = 0.0f;
        exitBarVelocityY = 0.0f;
        isExitBarDragging = false;
    }

    /**
     * Gets the exit bar dimensions based on the current phone position.
     *
     * @return An array containing [barX, barY, barWidth, barHeight]
     */
    private int[] getExitBarDimensions() {
        // Calculate bar position relative to the phone
        int barX = phoneX + EXIT_BAR_RELATIVE_X;
        int barY = phoneY + EXIT_BAR_RELATIVE_Y;

        // For the reference screen size, use the exact absolute position
        if (this.width == 480 && this.height == 252) {
            // Calculate the difference between current and reference phone positions
            int phoneDiffX = phoneX - REFERENCE_PHONE_X;
            int phoneDiffY = phoneY - REFERENCE_PHONE_Y;

            // Adjust the absolute position based on the phone position difference
            barX = ABSOLUTE_EXIT_BAR_X + phoneDiffX;
            barY = ABSOLUTE_EXIT_BAR_Y + phoneDiffY;
        }

        return new int[] { barX, barY, EXIT_BAR_WIDTH, EXIT_BAR_HEIGHT };
    }

    /**
     * Checks if the mouse is over the exit bar.
     *
     * @param mouseX The mouse X coordinate
     * @param mouseY The mouse Y coordinate
     * @return true if the mouse is over the exit bar, false otherwise
     */
    private boolean isMouseOverExitBar(int mouseX, int mouseY) {
        // Get the exit bar dimensions
        int[] dimensions = getExitBarDimensions();
        int barX = dimensions[0];
        int barY = dimensions[1];
        int barWidth = dimensions[2];
        int barHeight = dimensions[3];

        // Apply the current offset to the bar position
        barX += (int)exitBarOffsetX;
        barY += (int)exitBarOffsetY;

        return isMouseOverButton(mouseX, mouseY, barX, barY, barWidth, barHeight);
    }

    /**
     * Draws debug information to help with positioning.
     */
    private void drawDebugInfo(DrawContext context) {
        // Position debug info on the right side of the screen to avoid interfering with UI
        int rightMargin = 10;
        int textX = this.width - 200 - rightMargin; // 200 is approximate max text width

        // Draw phone boundary
        int outlineColor = 0x40FFFFFF; // Semi-transparent white
        context.drawBorder(phoneX, phoneY, PHONE_WIDTH, PHONE_HEIGHT, outlineColor);

        // Draw balance position
        int balanceX = phoneX + BALANCE_RELATIVE_X;
        int balanceY = phoneY + BALANCE_RELATIVE_Y;
        context.drawBorder(balanceX, balanceY, BALANCE_WIDTH, BALANCE_HEIGHT, 0x40FF0000); // Red outline

        // Draw balance position info
        String balanceText = String.format("Balance: x=%d, y=%d, w=%d, h=%d",
                                          balanceX, balanceY, BALANCE_WIDTH, BALANCE_HEIGHT);
        context.drawTextWithShadow(this.textRenderer, balanceText, textX, 102, 0xFFFFFFFF);

        // Draw reference phone position
        String refPhoneText = String.format("Ref Phone: x=%d, y=%d", REFERENCE_PHONE_X, REFERENCE_PHONE_Y);
        context.drawTextWithShadow(this.textRenderer, refPhoneText, textX, 30, 0xFFFFFFFF);

        // Draw current phone position
        String phoneText = String.format("Phone: x=%d, y=%d", phoneX, phoneY);
        context.drawTextWithShadow(this.textRenderer, phoneText, textX, 42, 0xFFFFFFFF);

        // Draw button relative position
        String relText = String.format("Button Rel: x=%d, y=%d", TRANSFER_BUTTON_RELATIVE_X, TRANSFER_BUTTON_RELATIVE_Y);
        context.drawTextWithShadow(this.textRenderer, relText, textX, 54, 0xFFFFFFFF);

        // Draw screen size
        String screenText = String.format("Screen: %dx%d", this.width, this.height);
        context.drawTextWithShadow(this.textRenderer, screenText, textX, 66, 0xFFFFFFFF);

        // Draw absolute button position
        String absText = String.format("Button Abs: x=%d, y=%d", ABSOLUTE_BUTTON_X, ABSOLUTE_BUTTON_Y);
        context.drawTextWithShadow(this.textRenderer, absText, textX, 78, 0xFFFFFFFF);

        // Draw exit bar position
        int[] exitBarDimensions = getExitBarDimensions();
        String exitBarText = String.format("Exit Bar: x=%d, y=%d (offset: %.1f, %.1f)",
                                         exitBarDimensions[0] + (int)exitBarOffsetX,
                                         exitBarDimensions[1] + (int)exitBarOffsetY,
                                         exitBarOffsetX, exitBarOffsetY);
        context.drawTextWithShadow(this.textRenderer, exitBarText, textX, 90, 0xFFFFFFFF);

        // Draw player list position if we're on the transfer page
        if (PhoneTextureManager.getInstance().getCurrentTexture() == PhoneTextureManager.BANK_APP_TRANSFER_TEXTURE) {
            int[] playerListDimensions = getPlayerListDimensions();
            int playerListX = playerListDimensions[0];
            int playerListY = playerListDimensions[1];
            int playerListWidth = playerListDimensions[2];
            int playerListHeight = playerListDimensions[3];

            // Draw player list outline
            context.drawBorder(playerListX, playerListY, playerListWidth, playerListHeight, 0x40FF00FF); // Purple outline

            // Draw player list position info
            String playerListText = String.format("Player List: x=%d, y=%d, w=%d, h=%d",
                                                playerListX, playerListY, playerListWidth, playerListHeight);
            context.drawTextWithShadow(this.textRenderer, playerListText, textX, 114, 0xFFFFFFFF);

            // Draw player list scroll info
            String scrollText = String.format("Scroll: %d, Players: %d", playerListScrollOffset, playerEntries.size());
            context.drawTextWithShadow(this.textRenderer, scrollText, textX, 126, 0xFFFFFFFF);

            // Draw selected player info
            String selectedText = "Selected: " + (selectedPlayer != null ? selectedPlayer.getPlayerName() : "None");
            context.drawTextWithShadow(this.textRenderer, selectedText, textX, 138, 0xFFFFFFFF);

            // Draw typing zone position
            int[] typingZoneDimensions = getTypingZoneDimensions();
            int typingZoneX = typingZoneDimensions[0];
            int typingZoneY = typingZoneDimensions[1];
            int typingZoneWidth = typingZoneDimensions[2];
            int typingZoneHeight = typingZoneDimensions[3];

            // Draw typing zone outline
            context.drawBorder(typingZoneX, typingZoneY, typingZoneWidth, typingZoneHeight, 0x4000FFFF); // Cyan outline

            // Draw typing zone position info
            String typingZoneText = String.format("Typing Zone: x=%d, y=%d, w=%d, h=%d",
                                               typingZoneX, typingZoneY, typingZoneWidth, typingZoneHeight);
            context.drawTextWithShadow(this.textRenderer, typingZoneText, textX, 150, 0xFFFFFFFF);

            // Draw typing zone content
            String typingContent = "Content: " + (typingZone != null ? "\"" + typingZone.getText() + "\"" : "null");
            context.drawTextWithShadow(this.textRenderer, typingContent, textX, 162, 0xFFFFFFFF);

            // Draw parsed amount
            String amountText = "Amount: " + getEnteredAmount();
            context.drawTextWithShadow(this.textRenderer, amountText, textX, 174, 0xFFFFFFFF);
        }
    }

    /**
     * Draws the transfer button on the bank app interface.
     *
     * @param context The draw context
     * @param mouseX The mouse X coordinate
     * @param mouseY The mouse Y coordinate
     */
    private void drawTransferButton(DrawContext context, int mouseX, int mouseY) {
        // Get the current texture
        Identifier currentTexture = PhoneTextureManager.getInstance().getCurrentTexture();

        // Draw button based on current page
        if (currentTexture == PhoneTextureManager.BANK_APP_HOME_TEXTURE) {
            // Draw the "Transfer" button on home screen
            drawHomeTransferButton(context, mouseX, mouseY);
        } else if (currentTexture == PhoneTextureManager.BANK_APP_TRANSFER_TEXTURE) {
            // Draw the "Start Transfer" button on transfer page
            drawTransferPageButton(context, mouseX, mouseY);
        }
    }

    /**
     * Draws the transfer button on the home screen.
     */
    private void drawHomeTransferButton(DrawContext context, int mouseX, int mouseY) {
        // Get the scaled button dimensions
        int[] dimensions = getScaledButtonDimensions();
        int buttonX = dimensions[0];
        int buttonY = dimensions[1];
        int buttonWidth = dimensions[2];
        int buttonHeight = dimensions[3];

        // Check if mouse is hovering over the button
        boolean isHovered = isMouseOverButton(mouseX, mouseY, buttonX, buttonY, buttonWidth, buttonHeight);

        // Only draw the button visuals if dev mode is enabled
        if (isDevModeEnabled()) {
            // Draw button background with a modern gradient look
            int baseColor = 0xFF4CAF50; // Green color similar to other buttons in the app
            int buttonColor = isHovered ? 0xFF66BB6A : baseColor; // Lighter green when hovered

            // Fill the button with the base color
            context.fill(buttonX, buttonY,
                      buttonX + buttonWidth,
                      buttonY + buttonHeight,
                      buttonColor);

            // Add a slight gradient effect (lighter at top, darker at bottom)
            context.fill(buttonX, buttonY,
                      buttonX + buttonWidth,
                      buttonY + buttonHeight/2,
                      0x10FFFFFF); // Semi-transparent white overlay for top half

            // Draw button border (dark green)
            context.drawBorder(buttonX, buttonY, buttonWidth, buttonHeight, 0xFF2E7D32);

            // Draw a highlight at the top edge for a more polished look
            context.fill(buttonX + 1, buttonY + 1,
                      buttonX + buttonWidth - 1,
                      buttonY + 2,
                      0x20FFFFFF); // Very subtle white highlight
        }

        // Only draw the button text if dev mode is enabled
        if (isDevModeEnabled()) {
            // Draw button text with shadow for better visibility
            String buttonText = "Transfer";
            int textWidth = this.textRenderer.getWidth(buttonText);
            int textX = buttonX + (buttonWidth - textWidth) / 2;

            // Adjust text Y position for the smaller button height
            int textY = buttonY + (buttonHeight - 7) / 2; // Adjusted for smaller height

            context.drawTextWithShadow(this.textRenderer, buttonText, textX, textY, 0xFFFFFFFF); // White text with shadow
        }

        // Log button position for debugging
        if (isHovered && !buttonPositionLogged) {
            Pokecobbleclaim.LOGGER.debug("Transfer button position: x=" + buttonX +
                                       ", y=" + buttonY +
                                       ", width=" + buttonWidth +
                                       ", height=" + buttonHeight);

            buttonPositionLogged = true;
        }
    }

    /**
     * Draws the transfer button on the transfer page.
     */
    private void drawTransferPageButton(DrawContext context, int mouseX, int mouseY) {
        // Get the transfer page button dimensions
        int[] dimensions = getTransferPageButtonDimensions();
        int buttonX = dimensions[0];
        int buttonY = dimensions[1];
        int buttonWidth = dimensions[2];
        int buttonHeight = dimensions[3];

        // Check if mouse is hovering over the button
        boolean isHovered = isMouseOverButton(mouseX, mouseY, buttonX, buttonY, buttonWidth, buttonHeight);

        // Only draw the button visuals if dev mode is enabled
        if (isDevModeEnabled()) {
            // Draw button background with a modern gradient look
            int baseColor = 0xFF2196F3; // Blue color for the transfer action
            int buttonColor = isHovered ? 0xFF42A5F5 : baseColor; // Lighter blue when hovered

            // Fill the button with the base color
            context.fill(buttonX, buttonY,
                      buttonX + buttonWidth,
                      buttonY + buttonHeight,
                      buttonColor);

            // Add a slight gradient effect (lighter at top, darker at bottom)
            context.fill(buttonX, buttonY,
                      buttonX + buttonWidth,
                      buttonY + buttonHeight/2,
                      0x10FFFFFF); // Semi-transparent white overlay for top half

            // Draw button border (dark blue)
            context.drawBorder(buttonX, buttonY, buttonWidth, buttonHeight, 0xFF1565C0);

            // Draw a highlight at the top edge for a more polished look
            context.fill(buttonX + 1, buttonY + 1,
                      buttonX + buttonWidth - 1,
                      buttonY + 2,
                      0x20FFFFFF); // Very subtle white highlight
        }

        // Only draw the button text if dev mode is enabled
        if (isDevModeEnabled()) {
            // Draw button text with shadow for better visibility
            String buttonText = "Send";
            int textWidth = this.textRenderer.getWidth(buttonText);
            int textX = buttonX + (buttonWidth - textWidth) / 2;

            // Adjust text Y position for the smaller button height
            int textY = buttonY + (buttonHeight - 7) / 2; // Adjusted for smaller height

            context.drawTextWithShadow(this.textRenderer, buttonText, textX, textY, 0xFFFFFFFF); // White text with shadow
        }
    }

    // Flag to prevent spamming the log with button position
    private boolean buttonPositionLogged = false;

    // Player list state
    private List<PlayerEntry> playerEntries = new ArrayList<>();
    private int playerListScrollOffset = 0;
    private PlayerEntry selectedPlayer = null;
    private boolean isPlayerListInitialized = false;

    // Typing zone
    private TextFieldWidget typingZone;

    // Player entry class to store player information
    private static class PlayerEntry {
        private final UUID playerId;
        private final String playerName;

        public PlayerEntry(UUID playerId, String playerName) {
            this.playerId = playerId;
            this.playerName = playerName;
        }

        public UUID getPlayerId() {
            return playerId;
        }

        public String getPlayerName() {
            return playerName;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;

            PlayerEntry other = (PlayerEntry) obj;
            return playerId.equals(other.playerId);
        }

        @Override
        public int hashCode() {
            return playerId.hashCode();
        }
    }

    /**
     * Gets the button dimensions based on the current phone position.
     * The button position is calculated relative to the phone position,
     * ensuring it stays in the same position relative to the phone texture
     * when the screen is resized.
     *
     * @return An array containing [buttonX, buttonY, buttonWidth, buttonHeight]
     */
    private int[] getScaledButtonDimensions() {
        // Calculate button position relative to the phone using the fixed relative position
        int buttonX = phoneX + TRANSFER_BUTTON_RELATIVE_X;
        int buttonY = phoneY + TRANSFER_BUTTON_RELATIVE_Y;

        // For the reference screen size, use the exact absolute position
        // The reference screen size is 480x252 as captured in the shape visualizer
        if (this.width == 480 && this.height == 252) {
            // Calculate the difference between current and reference phone positions
            int phoneDiffX = phoneX - REFERENCE_PHONE_X;
            int phoneDiffY = phoneY - REFERENCE_PHONE_Y;

            // Adjust the absolute position based on the phone position difference
            buttonX = ABSOLUTE_BUTTON_X + phoneDiffX;
            buttonY = ABSOLUTE_BUTTON_Y + phoneDiffY;

            // Log the adjustment for debugging
            Pokecobbleclaim.LOGGER.debug("Adjusted button position: x=" + buttonX +
                                       ", y=" + buttonY +
                                       " (phone diff: x=" + phoneDiffX +
                                       ", y=" + phoneDiffY + ")");
        } else {
            // Log the relative position for debugging
            Pokecobbleclaim.LOGGER.debug("Using relative button position: x=" + buttonX +
                                       ", y=" + buttonY +
                                       " (relative to phone: x=" + TRANSFER_BUTTON_RELATIVE_X +
                                       ", y=" + TRANSFER_BUTTON_RELATIVE_Y + ")");
        }

        // The button width and height are fixed relative to the phone
        // No need to scale them separately as they'll move with the phone
        int buttonWidth = TRANSFER_BUTTON_WIDTH;
        int buttonHeight = TRANSFER_BUTTON_HEIGHT;

        return new int[] { buttonX, buttonY, buttonWidth, buttonHeight };
    }

    /**
     * Gets the transfer page button dimensions based on the current phone position.
     *
     * @return An array containing [buttonX, buttonY, buttonWidth, buttonHeight]
     */
    private int[] getTransferPageButtonDimensions() {
        // Calculate button position relative to the phone using the fixed relative position
        int buttonX = phoneX + TRANSFER_PAGE_BUTTON_RELATIVE_X;
        int buttonY = phoneY + TRANSFER_PAGE_BUTTON_RELATIVE_Y;

        // For the reference screen size, use the exact absolute position
        if (this.width == 480 && this.height == 263) {
            // Calculate the difference between current and reference phone positions
            int phoneDiffX = phoneX - TRANSFER_PAGE_REFERENCE_PHONE_X;
            int phoneDiffY = phoneY - TRANSFER_PAGE_REFERENCE_PHONE_Y;

            // Adjust the absolute position based on the phone position difference
            buttonX = TRANSFER_PAGE_ABSOLUTE_BUTTON_X + phoneDiffX;
            buttonY = TRANSFER_PAGE_ABSOLUTE_BUTTON_Y + phoneDiffY;
        }

        // The button width and height are fixed relative to the phone
        int buttonWidth = TRANSFER_PAGE_BUTTON_WIDTH;
        int buttonHeight = TRANSFER_PAGE_BUTTON_HEIGHT;

        return new int[] { buttonX, buttonY, buttonWidth, buttonHeight };
    }

    /**
     * Checks if the mouse is over a button.
     *
     * @param mouseX The mouse X coordinate
     * @param mouseY The mouse Y coordinate
     * @param buttonX The button X coordinate
     * @param buttonY The button Y coordinate
     * @param buttonWidth The button width
     * @param buttonHeight The button height
     * @return true if the mouse is over the button, false otherwise
     */
    private boolean isMouseOverButton(int mouseX, int mouseY, int buttonX, int buttonY, int buttonWidth, int buttonHeight) {
        return mouseX >= buttonX &&
               mouseX <= buttonX + buttonWidth &&
               mouseY >= buttonY &&
               mouseY <= buttonY + buttonHeight;
    }

    /**
     * Checks if the mouse is over the transfer button.
     *
     * @param mouseX The mouse X coordinate
     * @param mouseY The mouse Y coordinate
     * @return true if the mouse is over the transfer button, false otherwise
     */
    private boolean isMouseOverTransferButton(int mouseX, int mouseY) {
        // Get the scaled button dimensions
        int[] dimensions = getScaledButtonDimensions();
        int buttonX = dimensions[0];
        int buttonY = dimensions[1];
        int buttonWidth = dimensions[2];
        int buttonHeight = dimensions[3];

        return isMouseOverButton(mouseX, mouseY, buttonX, buttonY, buttonWidth, buttonHeight);
    }

    /**
     * Checks if the mouse is over the transfer page button.
     *
     * @param mouseX The mouse X coordinate
     * @param mouseY The mouse Y coordinate
     * @return true if the mouse is over the transfer page button, false otherwise
     */
    private boolean isMouseOverTransferPageButton(int mouseX, int mouseY) {
        // Get the transfer page button dimensions
        int[] dimensions = getTransferPageButtonDimensions();
        int buttonX = dimensions[0];
        int buttonY = dimensions[1];
        int buttonWidth = dimensions[2];
        int buttonHeight = dimensions[3];

        return isMouseOverButton(mouseX, mouseY, buttonX, buttonY, buttonWidth, buttonHeight);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Get the current texture
        Identifier currentTexture = PhoneTextureManager.getInstance().getCurrentTexture();

        // Log the current texture and click position for debugging
        Pokecobbleclaim.LOGGER.debug("Mouse clicked at (" + mouseX + ", " + mouseY + ") with texture: " +
                                   (currentTexture == PhoneTextureManager.BANK_APP_HOME_TEXTURE ? "HOME" :
                                    currentTexture == PhoneTextureManager.BANK_APP_TRANSFER_TEXTURE ? "TRANSFER" : "UNKNOWN"));

        // Check if the click is on the exit bar
        if (isMouseOverExitBar((int)mouseX, (int)mouseY)) {
            // Start dragging the exit bar
            isExitBarDragging = true;
            exitBarDragStartX = (int)mouseX;
            exitBarDragStartY = (int)mouseY;
            Pokecobbleclaim.LOGGER.debug("Exit bar drag started at (" + mouseX + ", " + mouseY + ")");
            return true;
        }

        // Check if the click is on the transfer button when on home screen
        if (currentTexture == PhoneTextureManager.BANK_APP_HOME_TEXTURE &&
            isMouseOverTransferButton((int)mouseX, (int)mouseY)) {
            // Go to transfer page
            PhoneTextureManager.getInstance().openBankTransferPage();

            // Initialize player list when going to transfer page
            updatePlayerList();
            isPlayerListInitialized = true;

            // Clear typing zone when entering transfer page
            clearTypingZone();

            // Focus the typing zone
            if (typingZone != null) {
                typingZone.setFocused(true);
                this.setFocused(typingZone);
            }

            Pokecobbleclaim.LOGGER.debug("Opening bank transfer page from position (" + mouseX + ", " + mouseY + ")");
            return true;
        }

        // Check if we're on the transfer page
        if (currentTexture == PhoneTextureManager.BANK_APP_TRANSFER_TEXTURE) {
            // Check if the click is on the transfer page button
            if (isMouseOverTransferPageButton((int)mouseX, (int)mouseY)) {
                // Handle transfer button click
                handleTransferButtonClick();
                return true;
            }

            // Check if the click is on the player list
            if (isMouseOverPlayerList((int)mouseX, (int)mouseY)) {
                // Handle player list click
                handlePlayerListClick((int)mouseX, (int)mouseY);
                return true;
            }

            // If click is not on the player list, don't go back to home screen
            // This allows interaction with the player list
        }

        // We no longer need to check for clicks inside the phone area
        // since we now have the exit bar for navigation

        return super.mouseClicked(mouseX, mouseY, button);
    }

    /**
     * Checks if the mouse is over the player list.
     *
     * @param mouseX The mouse X coordinate
     * @param mouseY The mouse Y coordinate
     * @return true if the mouse is over the player list, false otherwise
     */
    private boolean isMouseOverPlayerList(int mouseX, int mouseY) {
        // Get the player list dimensions
        int[] dimensions = getPlayerListDimensions();
        int listX = dimensions[0];
        int listY = dimensions[1];
        int listWidth = dimensions[2];
        int listHeight = dimensions[3];

        return mouseX >= listX && mouseX <= listX + listWidth &&
               mouseY >= listY && mouseY <= listY + listHeight;
    }

    /**
     * Handles a click on the player list.
     *
     * @param mouseX The mouse X coordinate
     * @param mouseY The mouse Y coordinate
     */
    private void handlePlayerListClick(int mouseX, int mouseY) {
        // Get the player list dimensions
        int[] dimensions = getPlayerListDimensions();
        int listX = dimensions[0];
        int listY = dimensions[1];
        int listWidth = dimensions[2];
        int listHeight = dimensions[3];

        // Check if the click is on the scrollbar
        if (mouseX >= listX + listWidth - 5 && mouseX <= listX + listWidth - 1 &&
            mouseY >= listY + 1 && mouseY <= listY + listHeight - 1) {

            // Calculate total height of all entries
            int totalHeight = playerEntries.size() * PLAYER_ENTRY_HEIGHT;

            // Calculate max scroll
            int maxScroll = Math.max(0, totalHeight - listHeight);

            if (maxScroll > 0) {
                // Calculate new scroll position based on click position
                float clickPosition = (float)(mouseY - listY - 1) / (listHeight - 2);
                playerListScrollOffset = Math.round(clickPosition * maxScroll);
                playerListScrollOffset = Math.max(0, Math.min(playerListScrollOffset, maxScroll));
                return;
            }
        }

        // Check if the click is on a player entry
        if (mouseX >= listX + 1 && mouseX <= listX + listWidth - 5 &&
            mouseY >= listY + 1 && mouseY <= listY + listHeight - 1) {

            // Calculate which player was clicked
            int entryY = listY + 1 - playerListScrollOffset;

            for (PlayerEntry entry : playerEntries) {
                // Skip if entry is completely outside visible area
                if (entryY + PLAYER_ENTRY_HEIGHT < listY + 1 || entryY > listY + listHeight - 1) {
                    entryY += PLAYER_ENTRY_HEIGHT;
                    continue;
                }

                // Check if this entry was clicked
                if (mouseY >= entryY && mouseY <= entryY + PLAYER_ENTRY_HEIGHT) {
                    // Select this player
                    selectedPlayer = entry;
                    Pokecobbleclaim.LOGGER.debug("Selected player: " + entry.getPlayerName());
                    return;
                }

                // Move to next entry
                entryY += PLAYER_ENTRY_HEIGHT;
            }
        }
    }

    /**
     * Checks if a click is inside the phone area.
     *
     * @param mouseX The mouse X coordinate
     * @param mouseY The mouse Y coordinate
     * @return true if the click is inside the phone, false otherwise
     */
    private boolean isClickInsidePhone(double mouseX, double mouseY) {
        return mouseX >= phoneX && mouseX <= phoneX + PHONE_WIDTH &&
               mouseY >= phoneY && mouseY <= phoneY + PHONE_HEIGHT;
    }

    @Override
    public boolean shouldPause() {
        // Don't pause the game when the phone is open
        return false;
    }

    @Override
    public boolean shouldCloseOnEsc() {
        // Close the phone when ESC is pressed
        return true;
    }

    /**
     * Initializes the typing zone text field.
     */
    private void initTypingZone() {
        // Calculate typing zone position
        int typingZoneX = phoneX + TYPING_ZONE_RELATIVE_X;
        int typingZoneY = phoneY + TYPING_ZONE_RELATIVE_Y;

        // For the reference screen size, use the exact absolute position
        if (this.width == 480 && this.height == 252) {
            // Calculate the difference between current and reference phone positions
            int phoneDiffX = phoneX - TYPING_ZONE_REFERENCE_PHONE_X;
            int phoneDiffY = phoneY - TYPING_ZONE_REFERENCE_PHONE_Y;

            // Adjust the absolute position based on the phone position difference
            typingZoneX = TYPING_ZONE_ABSOLUTE_X + phoneDiffX;
            typingZoneY = TYPING_ZONE_ABSOLUTE_Y + phoneDiffY;
        }

        // Create a custom text field that doesn't render anything
        typingZone = new TextFieldWidget(
            this.textRenderer,
            typingZoneX,
            typingZoneY,
            TYPING_ZONE_WIDTH,
            TYPING_ZONE_HEIGHT,
            Text.literal("")
        ) {
            @Override
            public void render(DrawContext context, int mouseX, int mouseY, float delta) {
                // Don't render anything - we'll handle rendering ourselves
                // But still need to update the cursor position
                this.tick();
            }
        };

        // Configure the text field
        typingZone.setMaxLength(10); // Limit to 10 digits (reasonable for a money amount)
        typingZone.setDrawsBackground(false); // No background
        typingZone.setVisible(true);
        typingZone.setEditable(true);
        typingZone.setFocusUnlocked(true); // Allow focus without background
        typingZone.setText(""); // Ensure it starts empty

        // We'll handle the placeholder text ourselves
        typingZone.setPlaceholder(Text.empty());

        // Add text filter to only allow numbers
        typingZone.setTextPredicate(this::isValidNumber);

        // Add the text field to the screen
        this.addDrawableChild(typingZone);

        Pokecobbleclaim.LOGGER.debug("Initialized typing zone at x=" + typingZoneX + ", y=" + typingZoneY);
    }

    /**
     * Gets the typing zone dimensions based on the current phone position.
     *
     * @return An array containing [zoneX, zoneY, zoneWidth, zoneHeight]
     */
    private int[] getTypingZoneDimensions() {
        // Calculate typing zone position relative to the phone
        int zoneX = phoneX + TYPING_ZONE_RELATIVE_X;
        int zoneY = phoneY + TYPING_ZONE_RELATIVE_Y;

        // For the reference screen size, use the exact absolute position
        if (this.width == 480 && this.height == 252) {
            // Calculate the difference between current and reference phone positions
            int phoneDiffX = phoneX - TYPING_ZONE_REFERENCE_PHONE_X;
            int phoneDiffY = phoneY - TYPING_ZONE_REFERENCE_PHONE_Y;

            // Adjust the absolute position based on the phone position difference
            zoneX = TYPING_ZONE_ABSOLUTE_X + phoneDiffX;
            zoneY = TYPING_ZONE_ABSOLUTE_Y + phoneDiffY;
        }

        return new int[] { zoneX, zoneY, TYPING_ZONE_WIDTH, TYPING_ZONE_HEIGHT };
    }

    /**
     * Validates that the input string contains only numbers.
     *
     * @param text The text to validate
     * @return true if the text contains only numbers, false otherwise
     */
    private boolean isValidNumber(String text) {
        // Empty string is valid (allows deleting all text)
        if (text.isEmpty()) {
            return true;
        }

        // Check if the text contains only digits
        for (char c : text.toCharArray()) {
            if (!Character.isDigit(c)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Gets the current amount entered in the typing zone.
     *
     * @return The amount as a long, or 0 if the typing zone is empty or invalid
     */
    private long getEnteredAmount() {
        if (typingZone == null || typingZone.getText().isEmpty()) {
            return 0;
        }

        try {
            return Long.parseLong(typingZone.getText());
        } catch (NumberFormatException e) {
            // This shouldn't happen since we validate input, but just in case
            Pokecobbleclaim.LOGGER.error("Error parsing amount: " + e.getMessage());
            return 0;
        }
    }

    /**
     * Clears the typing zone.
     */
    private void clearTypingZone() {
        if (typingZone != null) {
            typingZone.setText("");
        }
    }

    /**
     * Manually renders the text in the typing zone.
     * This is called after the normal rendering to overlay our custom text.
     *
     * @param context The draw context
     */
    private void renderTypingZoneText(DrawContext context) {
        // Get the typing zone dimensions
        int[] dimensions = getTypingZoneDimensions();
        int zoneX = dimensions[0];
        int zoneY = dimensions[1];
        int zoneWidth = dimensions[2];
        int zoneHeight = dimensions[3];

        // Enable scissor to clip text to the typing zone bounds
        context.enableScissor(
            zoneX,
            zoneY,
            zoneX + zoneWidth,
            zoneY + zoneHeight
        );

        // Get the text to display
        String text = typingZone.getText();

        // If the text is empty and the typing zone is not focused, show the placeholder
        if (text.isEmpty() && !typingZone.isFocused()) {
            // Draw placeholder text
            context.drawText(
                this.textRenderer,
                "Type amount here...",
                zoneX, // No padding - start at the exact left edge
                zoneY + 2, // Add a small padding
                0x80FFFFFF, // Semi-transparent white
                false // No shadow for placeholder
            );
            context.disableScissor();
            return;
        }

        // Draw the text with a shadow to make it more visible
        context.drawTextWithShadow(
            this.textRenderer,
            text,
            zoneX, // No padding - start at the exact left edge
            zoneY + 2, // Add a small padding
            0xFFFFFFFF // White color
        );

        // Draw cursor if the field is focused
        if (typingZone.isFocused()) {
            // Calculate cursor position
            int cursorPos = this.textRenderer.getWidth(text.substring(0, typingZone.getCursor()));

            // Draw cursor
            if ((System.currentTimeMillis() / 500) % 2 == 0) { // Blink every 500ms
                context.fill(
                    zoneX + cursorPos,
                    zoneY + 1,
                    zoneX + 1 + cursorPos,
                    zoneY + 1 + this.textRenderer.fontHeight,
                    0xFFFFFFFF // White cursor
                );
            }
        }

        // Disable scissor
        context.disableScissor();
    }

    /**
     * Updates the player list with online players.
     */
    private void updatePlayerList() {
        // Clear the current list
        playerEntries.clear();

        // Get the Minecraft client
        MinecraftClient client = MinecraftClient.getInstance();

        // Get all online players from the player list
        if (client != null && client.getNetworkHandler() != null) {
            for (PlayerListEntry entry : client.getNetworkHandler().getPlayerList()) {
                UUID playerId = entry.getProfile().getId();
                String playerName = entry.getProfile().getName();

                // Skip the current player
                if (client.player != null && playerId.equals(client.player.getUuid())) {
                    continue;
                }

                playerEntries.add(new PlayerEntry(playerId, playerName));
            }
        }

        // Reset scroll offset
        playerListScrollOffset = 0;

        // Reset selected player
        selectedPlayer = null;

        Pokecobbleclaim.LOGGER.debug("Updated player list, found " + playerEntries.size() + " players");
    }

    /**
     * Adds dummy players to the player list for testing.
     */
    private void addDummyPlayers() {
        // Add some dummy players with random UUIDs
        playerEntries.add(new PlayerEntry(UUID.randomUUID(), "Steve"));
        playerEntries.add(new PlayerEntry(UUID.randomUUID(), "Alex"));
        playerEntries.add(new PlayerEntry(UUID.randomUUID(), "Notch"));
        playerEntries.add(new PlayerEntry(UUID.randomUUID(), "Jeb"));
        playerEntries.add(new PlayerEntry(UUID.randomUUID(), "Dinnerbone"));
        playerEntries.add(new PlayerEntry(UUID.randomUUID(), "Grumm"));
        playerEntries.add(new PlayerEntry(UUID.randomUUID(), "Herobrine"));
        playerEntries.add(new PlayerEntry(UUID.randomUUID(), "Honeydew"));
        playerEntries.add(new PlayerEntry(UUID.randomUUID(), "Xephos"));
        playerEntries.add(new PlayerEntry(UUID.randomUUID(), "Sips"));
        playerEntries.add(new PlayerEntry(UUID.randomUUID(), "Sjin"));
        playerEntries.add(new PlayerEntry(UUID.randomUUID(), "Duncan"));

        // Reset scroll offset
        playerListScrollOffset = 0;

        // Reset selected player
        selectedPlayer = null;

        Pokecobbleclaim.LOGGER.debug("Added " + playerEntries.size() + " dummy players to the list");

        // Show a notification to the user
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player != null) {
            client.player.sendMessage(Text.literal("§aAdded dummy players to the list. Press C to clear."), true);
        }
    }

    /**
     * Adds a specified number of dummy players to the player list for testing.
     *
     * @param count The number of dummy players to add
     */
    private void addManyDummyPlayers(int count) {
        // Generate random player names
        String[] prefixes = {"Player", "User", "Gamer", "MC", "Pro", "Noob", "Cool", "Epic", "Super", "Mega"};

        // Add the specified number of dummy players
        for (int i = 0; i < count; i++) {
            String prefix = prefixes[i % prefixes.length];
            String playerName = prefix + "_" + (i + 1);
            playerEntries.add(new PlayerEntry(UUID.randomUUID(), playerName));
        }

        // Reset scroll offset
        playerListScrollOffset = 0;

        // Reset selected player
        selectedPlayer = null;

        Pokecobbleclaim.LOGGER.debug("Added " + count + " dummy players to the list");

        // Show a notification to the user
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player != null) {
            client.player.sendMessage(Text.literal("§aAdded " + count + " dummy players to the list. Press C to clear."), true);
        }
    }

    /**
     * Clears the player list.
     */
    private void clearPlayerList() {
        // Clear the player list
        playerEntries.clear();

        // Reset scroll offset
        playerListScrollOffset = 0;

        // Reset selected player
        selectedPlayer = null;

        Pokecobbleclaim.LOGGER.debug("Cleared player list");

        // Show a notification to the user
        MinecraftClient client = MinecraftClient.getInstance();
        if (client.player != null) {
            client.player.sendMessage(Text.literal("§cCleared player list. Press T to add dummy players."), true);
        }
    }

    /**
     * Gets the player list dimensions based on the current phone position.
     *
     * @return An array containing [listX, listY, listWidth, listHeight]
     */
    private int[] getPlayerListDimensions() {
        // Calculate list position relative to the phone
        int listX = phoneX + PLAYER_LIST_RELATIVE_X;
        int listY = phoneY + PLAYER_LIST_RELATIVE_Y;

        // For the reference screen size, use the exact absolute position
        if (this.width == 480 && this.height == 252) {
            // Calculate the difference between current and reference phone positions
            int phoneDiffX = phoneX - PLAYER_LIST_REFERENCE_PHONE_X;
            int phoneDiffY = phoneY - PLAYER_LIST_REFERENCE_PHONE_Y;

            // Adjust the absolute position based on the phone position difference
            listX = PLAYER_LIST_ABSOLUTE_X + phoneDiffX;
            listY = PLAYER_LIST_ABSOLUTE_Y + phoneDiffY;
        }

        return new int[] { listX, listY, PLAYER_LIST_WIDTH, PLAYER_LIST_HEIGHT };
    }

    /**
     * Draws the player list on the transfer page.
     *
     * @param context The draw context
     * @param mouseX The mouse X coordinate
     * @param mouseY The mouse Y coordinate
     */
    private void drawPlayerList(DrawContext context, int mouseX, int mouseY) {
        // Get the player list dimensions
        int[] dimensions = getPlayerListDimensions();
        int listX = dimensions[0];
        int listY = dimensions[1];
        int listWidth = dimensions[2];
        int listHeight = dimensions[3];

        // Draw list background
        context.fill(listX, listY, listX + listWidth, listY + listHeight, PLAYER_LIST_BACKGROUND_COLOR);

        // Draw list border
        context.drawBorder(listX, listY, listWidth, listHeight, PLAYER_LIST_BORDER_COLOR);

        // Calculate total height of all entries
        int totalHeight = playerEntries.size() * PLAYER_ENTRY_HEIGHT;

        // Calculate max scroll
        int maxScroll = Math.max(0, totalHeight - listHeight);
        playerListScrollOffset = Math.min(playerListScrollOffset, maxScroll);

        // Apply scissor to clip content to visible area
        context.enableScissor(
            listX + 1,
            listY + 1,
            listX + listWidth - 1,
            listY + listHeight - 1
        );

        // Draw player entries with scrolling
        int entryY = listY + 1 - playerListScrollOffset;

        for (PlayerEntry entry : playerEntries) {
            // Skip if entry is completely outside visible area
            if (entryY + PLAYER_ENTRY_HEIGHT < listY + 1 || entryY > listY + listHeight - 1) {
                entryY += PLAYER_ENTRY_HEIGHT;
                continue;
            }

            // Check if this entry is hovered or selected
            boolean isHovered = mouseX >= listX + 1 && mouseX <= listX + listWidth - 1 &&
                               mouseY >= entryY && mouseY <= entryY + PLAYER_ENTRY_HEIGHT &&
                               mouseY >= listY + 1 && mouseY <= listY + listHeight - 1;

            boolean isSelected = entry.equals(selectedPlayer);

            // Draw entry background
            if (isSelected) {
                context.fill(listX + 1, entryY, listX + listWidth - 1, entryY + PLAYER_ENTRY_HEIGHT, PLAYER_LIST_SELECTED_COLOR);
            } else if (isHovered) {
                context.fill(listX + 1, entryY, listX + listWidth - 1, entryY + PLAYER_ENTRY_HEIGHT, PLAYER_LIST_HOVER_COLOR);
            }

            // Draw player name
            context.drawTextWithShadow(
                this.textRenderer,
                entry.getPlayerName(),
                listX + 3,
                entryY + 2,
                0xFFFFFFFF
            );

            // Move to next entry
            entryY += PLAYER_ENTRY_HEIGHT;
        }

        // Disable scissor
        context.disableScissor();

        // Draw scrollbar if needed
        if (maxScroll > 0) {
            // Draw scrollbar track
            context.fill(listX + listWidth - 5, listY + 1, listX + listWidth - 1, listY + listHeight - 1, 0x40FFFFFF);

            // Calculate scrollbar height and position
            int scrollbarHeight = Math.max(20, listHeight * listHeight / totalHeight);
            int scrollbarY = listY + 1 + (listHeight - 2 - scrollbarHeight) * playerListScrollOffset / maxScroll;

            // Draw scrollbar handle
            context.fill(listX + listWidth - 5, scrollbarY, listX + listWidth - 1, scrollbarY + scrollbarHeight, 0x80FFFFFF);
        }
    }

    /**
     * Draws the player's balance on the bank app screen.
     *
     * @param context The draw context
     */
    // Cache for the displayed balance to reduce flickering
    private long lastDisplayedBalance = 0;
    private String lastBalanceText = "0"; // Removed dollar sign
    private long lastBalanceUpdateTime = 0;
    private static final long BALANCE_UPDATE_INTERVAL_MS = 500; // Update display every 500ms

    private void drawPlayerBalance(DrawContext context) {
        // Calculate balance position
        int balanceX = phoneX + BALANCE_RELATIVE_X;
        int balanceY = phoneY + BALANCE_RELATIVE_Y;

        // Check if it's time to update the displayed balance
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastBalanceUpdateTime > BALANCE_UPDATE_INTERVAL_MS) {
            try {
                // Get player balance from network handler
                long balance = MoneyNetworkHandler.getCurrentPlayerBalance();

                // Only update if balance has changed
                if (balance != lastDisplayedBalance) {
                    lastDisplayedBalance = balance;
                    lastBalanceText = String.format("%,d", balance); // Removed dollar sign
                }

                // Update timestamp
                lastBalanceUpdateTime = currentTime;
            } catch (Exception e) {
                // If there's an error, just use the last known balance
                Pokecobbleclaim.LOGGER.debug("Error updating balance display: " + e.getMessage());
                // Don't spam logs with errors
            }
        }

        // Draw balance text
        int textColor = 0xFFFFFFFF; // White color for money text

        // Calculate text width for right alignment
        int textWidth = this.textRenderer.getWidth(lastBalanceText);

        // Right-align the text within the balance zone
        int rightAlignedX = balanceX + BALANCE_WIDTH - textWidth;

        // Draw the balance text with shadow for better visibility, right-aligned
        context.drawTextWithShadow(this.textRenderer, lastBalanceText, rightAlignedX, balanceY, textColor);
    }

    /**
     * Override the close method to ensure proper cleanup.
     */
    @Override
    public void close() {
        // Reset the texture manager state before closing
        PhoneTextureManager.getInstance().resetToDefaultTexture();

        // Stop watching balance when closing the app
        MoneyNetworkHandler.stopWatchingBalance();

        super.close();
    }

    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        // Handle exit bar dragging
        if (isExitBarDragging && button == 0) {
            // Calculate the new offset based on the drag
            float newOffsetX = exitBarOffsetX + (float)deltaX;
            float newOffsetY = exitBarOffsetY + (float)deltaY;

            // Limit the offset to the maximum allowed values
            newOffsetX = Math.max(-EXIT_BAR_MAX_SIDE_OFFSET, Math.min(EXIT_BAR_MAX_SIDE_OFFSET, newOffsetX));
            // Allow more movement upward than downward
            newOffsetY = Math.max(-EXIT_BAR_MAX_UP_OFFSET, Math.min(EXIT_BAR_MAX_UP_OFFSET / 4, newOffsetY));

            // Update the offset
            exitBarOffsetX = newOffsetX;
            exitBarOffsetY = newOffsetY;

            // Update the velocity based on the drag
            exitBarVelocityX = (float)deltaX * 0.5f;
            exitBarVelocityY = (float)deltaY * 0.5f;

            return true;
        }

        return super.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
    }

    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        // Handle exit bar release
        if (isExitBarDragging && button == 0) {
            isExitBarDragging = false;
            Pokecobbleclaim.LOGGER.debug("Exit bar drag released at (" + mouseX + ", " + mouseY + ")");

            // Check if the bar has been moved enough to trigger navigation
            checkExitBarNavigation();

            return true;
        }

        return super.mouseReleased(mouseX, mouseY, button);
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        // Close the phone when F key is pressed
        if (keyCode == GLFW.GLFW_KEY_F) {
            this.close();
            return true;
        }

        // Open the shape visualizer when V key is pressed (only in dev mode)
        if (keyCode == GLFW.GLFW_KEY_V) {
            if (isDevModeEnabled()) {
                Pokecobbleclaim.LOGGER.debug("V key pressed on bank app screen, opening shape visualizer");
                // Use toggleShapeVisualizer instead of openOnPhone to ensure consistent behavior
                ShapeVisualizerTool.toggleShapeVisualizer();
                return true;
            } else {
                Pokecobbleclaim.LOGGER.debug("V key pressed but dev mode is not enabled");
            }
        }

        // Add dummy players when T key is pressed (only in dev mode and on transfer page)
        if (keyCode == GLFW.GLFW_KEY_T) {
            if (isDevModeEnabled()) {
                Identifier currentTexture = PhoneTextureManager.getInstance().getCurrentTexture();
                if (currentTexture == PhoneTextureManager.BANK_APP_TRANSFER_TEXTURE) {
                    addDummyPlayers();
                    return true;
                }
            }
        }

        // Add many dummy players when M key is pressed (only in dev mode and on transfer page)
        if (keyCode == GLFW.GLFW_KEY_M) {
            if (isDevModeEnabled()) {
                Identifier currentTexture = PhoneTextureManager.getInstance().getCurrentTexture();
                if (currentTexture == PhoneTextureManager.BANK_APP_TRANSFER_TEXTURE) {
                    addManyDummyPlayers(30); // Add 30 dummy players
                    return true;
                }
            }
        }

        // Clear dummy players when C key is pressed (only in dev mode and on transfer page)
        if (keyCode == GLFW.GLFW_KEY_C) {
            if (isDevModeEnabled()) {
                Identifier currentTexture = PhoneTextureManager.getInstance().getCurrentTexture();
                if (currentTexture == PhoneTextureManager.BANK_APP_TRANSFER_TEXTURE) {
                    clearPlayerList();
                    return true;
                }
            }
        }

        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    @Override
    public void tick() {
        super.tick();

        // Tick the typing zone if it exists
        if (typingZone != null) {
            typingZone.tick();
        }
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        // Pass the event to the shape visualizer if it's active
        if (ShapeVisualizerTool.isActive()) {
            return ShapeVisualizerTool.getInstance().handleMouseScroll(mouseX, mouseY, amount);
        }

        // Get the current texture
        Identifier currentTexture = PhoneTextureManager.getInstance().getCurrentTexture();

        // Check if we're on the transfer page
        if (currentTexture == PhoneTextureManager.BANK_APP_TRANSFER_TEXTURE) {
            // Check if the mouse is over the player list
            if (isMouseOverPlayerList((int)mouseX, (int)mouseY)) {
                // Calculate total height of all entries
                int totalHeight = playerEntries.size() * PLAYER_ENTRY_HEIGHT;

                // Get the player list dimensions
                int[] dimensions = getPlayerListDimensions();
                int listHeight = dimensions[3];

                // Calculate max scroll
                int maxScroll = Math.max(0, totalHeight - listHeight);

                // Update scroll offset with smoother scrolling
                playerListScrollOffset -= (int) (amount * 10); // Scroll amount

                // Ensure scroll offset stays within bounds
                playerListScrollOffset = Math.max(0, Math.min(playerListScrollOffset, maxScroll));

                return true;
            }
        }

        return super.mouseScrolled(mouseX, mouseY, amount);
    }

    /**
     * Handles the transfer button click on the transfer page.
     */
    private void handleTransferButtonClick() {
        // Validate that a player is selected
        if (selectedPlayer == null) {
            String errorMsg = "Transfer attempt failed: No player selected";
            Pokecobbleclaim.LOGGER.debug("No player selected for transfer");
            ErrorLogger.getInstance().logError(
                errorMsg,
                null,
                "Money",
                ErrorLogger.ErrorSeverity.WARNING
            );
            // TODO: Show error message to user
            return;
        }

        // Get the entered amount
        long amount = getEnteredAmount();
        if (amount <= 0) {
            String errorMsg = "Transfer attempt failed: Invalid amount entered: " + amount;
            Pokecobbleclaim.LOGGER.debug("Invalid amount entered: " + amount);
            ErrorLogger.getInstance().logError(
                errorMsg,
                null,
                "Money",
                ErrorLogger.ErrorSeverity.WARNING
            );
            // TODO: Show error message to user
            return;
        }

        // Log transfer initiation
        String transferMsg = "Player initiated transfer: " + amount + " to " + selectedPlayer.getPlayerName();
        ErrorLogger.getInstance().logInfo(transferMsg, "Money");

        // Send transfer request to server
        MoneyNetworkHandler.requestMoneyTransfer(selectedPlayer.getPlayerName(), amount);

        Pokecobbleclaim.LOGGER.debug("Requesting transfer of " + amount + " to " + selectedPlayer.getPlayerName());
    }

    /**
     * Handles the transfer result from the server.
     *
     * @param resultCode The result code from the server
     * @param message The result message
     */
    public void handleTransferResult(int resultCode, String message) {
        Pokecobbleclaim.LOGGER.debug("Transfer result: " + resultCode + " - " + message);

        // Log the transfer result to ErrorLogger
        String resultMsg = "Transfer result processed: ";
        ErrorLogger.ErrorSeverity severity;

        // Navigate to appropriate result page based on result code
        switch (resultCode) {
            case MoneyNetworkHandler.TRANSFER_RESULT_SUCCESS:
                // Transfer successful - show success page
                resultMsg += "SUCCESS - Navigating to success page";
                severity = ErrorLogger.ErrorSeverity.INFO;
                PhoneTextureManager.getInstance().openBankTransferSuccessPage();
                break;

            case MoneyNetworkHandler.TRANSFER_RESULT_INSUFFICIENT_FUNDS:
                // Insufficient funds - show failed page
                resultMsg += "INSUFFICIENT_FUNDS - Navigating to failed page";
                severity = ErrorLogger.ErrorSeverity.WARNING;
                PhoneTextureManager.getInstance().openBankTransferFailedPage();
                break;

            case MoneyNetworkHandler.TRANSFER_RESULT_ERROR:
            default:
                // Error occurred - show error page
                resultMsg += "ERROR - Navigating to error page";
                severity = ErrorLogger.ErrorSeverity.ERROR;
                PhoneTextureManager.getInstance().openBankTransferErrorPage();
                break;
        }

        // Log the result to ErrorLogger
        ErrorLogger.getInstance().logError(resultMsg + " - " + message, null, "Money", severity);
    }
}
