package com.pokecobble.phone.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.context.CommandContext;
import com.pokecobble.phone.DevModeManager;
import net.minecraft.command.CommandRegistryAccess;
import net.minecraft.server.command.CommandManager;
import net.minecraft.server.command.ServerCommandSource;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;

/**
 * Command handler for the /phone devmode command.
 * This command toggles developer mode for the phone system.
 */
public class PhoneDevModeCommand {
    
    /**
     * Registers the /phone devmode command.
     * 
     * @param dispatcher The command dispatcher
     * @param registryAccess The command registry access
     * @param environment The command registration environment
     */
    public static void register(CommandDispatcher<ServerCommandSource> dispatcher, 
                               CommandRegistryAccess registryAccess,
                               CommandManager.RegistrationEnvironment environment) {
        
        dispatcher.register(
            CommandManager.literal("phone")
                .then(CommandManager.literal("devmode")
                    .requires(source -> source.hasPermissionLevel(2)) // Require operator permission level
                    .executes(PhoneDevModeCommand::toggleDevMode)
                )
        );
    }
    
    /**
     * Handles the execution of the /phone devmode command.
     * 
     * @param context The command context
     * @return 1 if successful, 0 otherwise
     */
    private static int toggleDevMode(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();
        ServerPlayerEntity player = source.getPlayer();
        
        if (player == null) {
            source.sendError(Text.literal("This command can only be executed by a player."));
            return 0;
        }
        
        boolean enabled = DevModeManager.getInstance().toggleDevMode(player);
        source.sendFeedback(() -> Text.literal("Phone dev mode " + (enabled ? "enabled" : "disabled") + "."), false);
        
        return 1;
    }
}
