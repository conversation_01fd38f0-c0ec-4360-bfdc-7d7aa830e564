package com.pokecobble.mixin;

import com.pokecobble.town.claim.ClaimTool;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.Mouse;
import org.spongepowered.asm.mixin.Final;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Shadow;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(Mouse.class)
public class MouseScrollMixin {
    @Shadow @Final private MinecraftClient client;

    @Inject(method = "onMouseScroll", at = @At("HEAD"), cancellable = true)
    private void onMouseScroll(long window, double horizontal, double vertical, CallbackInfo ci) {
        // Check if the claim tool is active
        if (ClaimTool.getInstance().isActive() && client.currentScreen == null) {
            // Always cancel the event when claim tool is active to prevent normal scrolling (item switching)
            ci.cancel();

            // Handle mouse scrolling in the claim tool
            ClaimTool.getInstance().handleMouseScroll(vertical);
        }
    }
}
