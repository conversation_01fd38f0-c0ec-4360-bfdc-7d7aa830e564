package com.pokecobble.mixin;

import com.pokecobble.phone.gui.PhoneRenderer;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.Mouse;
import org.spongepowered.asm.mixin.Final;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Shadow;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

/**
 * Mixin to handle mouse behavior when the phone is open.
 */
@Mixin(Mouse.class)
public class PhoneMixin {
    @Shadow @Final private MinecraftClient client;

    /**
     * Prevents mouse input from affecting the game when the phone is open.
     */
    @Inject(method = "updateMouse", at = @At("HEAD"), cancellable = true)
    private void onUpdateMouse(CallbackInfo ci) {
        // If the phone is visible and we're not in a screen, cancel mouse updates to the game
        if (PhoneRenderer.getInstance().isVisible() && client.currentScreen == null) {
            // Cancel the mouse update to prevent it from affecting the game
            // This makes the phone behave more like a screen
            ci.cancel();
        }
    }

    /**
     * Prevents mouse clicks from affecting the game when the phone is open.
     */
    @Inject(method = "onMouseButton", at = @At("HEAD"), cancellable = true)
    private void onMouseButton(long window, int button, int action, int mods, CallbackInfo ci) {
        // If the phone is visible and we're not in a screen, cancel mouse clicks
        if (PhoneRenderer.getInstance().isVisible() && client.currentScreen == null) {
            // Cancel the mouse click to prevent it from affecting the game
            // This makes the phone behave more like a screen
            ci.cancel();
        }
    }
}
