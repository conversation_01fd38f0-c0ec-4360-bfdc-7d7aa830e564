package com.pokecobble.mixin;

import com.pokecobble.town.claim.ClaimTool;
import net.minecraft.client.Keyboard;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.screen.GameMenuScreen;
import org.spongepowered.asm.mixin.Final;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Shadow;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;
import org.lwjgl.glfw.GLFW;

/**
 * Mixin to intercept keyboard input and handle ESC key for the claim tool.
 */
@Mixin(Keyboard.class)
public class KeyboardMixin {
    @Shadow @Final private MinecraftClient client;

    /**
     * Intercepts key presses to handle ESC key for the claim tool.
     * This is injected at the HEAD to handle ESC key before other mods.
     */
    @Inject(method = "onKey", at = @At("HEAD"), cancellable = true) // We'll make this override other mods
    private void onKey(long window, int key, int scancode, int action, int modifiers, CallbackInfo ci) {
        // Only handle key press events (not releases or repeats)
        if (action != GLFW.GLFW_PRESS) {
            return;
        }

        // Check if the claim tool is active and the ESC key was pressed
        if (ClaimTool.getInstance().isActive() && key == GLFW.GLFW_KEY_ESCAPE) {
            // Log the key press
            com.pokecobble.Pokecobbleclaim.LOGGER.info("ESC key pressed in KeyboardMixin");

            // Show the exit confirmation dialog regardless of whether a screen is open
            // This will override any other mod's ESC handling when the claim tool is active
            com.pokecobble.Pokecobbleclaim.LOGGER.info("Claim tool active, showing exit confirmation");
            ClaimTool.getInstance().showExitConfirmation();

            // Cancel the event to prevent the pause screen from opening or any other mod handling it
            ci.cancel();
        }
    }

    /**
     * Prevents F3 combinations from working when the claim tool is active.
     */
    @Inject(method = "processF3", at = @At("HEAD"), cancellable = true)
    private void onProcessF3(int key, CallbackInfoReturnable<Boolean> cir) {
        // If the claim tool is active, prevent F3 combinations from working
        if (ClaimTool.getInstance().isActive()) {
            cir.setReturnValue(false);
            cir.cancel();
        }
    }
}
