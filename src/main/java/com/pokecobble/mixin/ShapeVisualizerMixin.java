package com.pokecobble.mixin;

import com.pokecobble.tools.gui.ShapeVisualizerTool;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.Mouse;
import org.spongepowered.asm.mixin.Final;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Shadow;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

/**
 * Mixin to handle mouse input for the Shape Visualizer tool when used as an overlay.
 */
@Mixin(Mouse.class)
public class ShapeVisualizerMixin {
    @Shadow @Final private MinecraftClient client;

    /**
     * Intercepts mouse clicks to handle them for the Shape Visualizer when it's active.
     */
    @Inject(method = "onMouseButton", at = @At("HEAD"), cancellable = true)
    private void onMouseButton(long window, int button, int action, int mods, CallbackInfo ci) {
        if (ShapeVisualizerTool.isActive()) {
            // Get mouse position
            double mouseX = client.mouse.getX();
            double mouseY = client.mouse.getY();

            // Handle mouse click or release
            if (action == 1) { // GLFW_PRESS
                if (ShapeVisualizerTool.getInstance().handleMouseClick(mouseX, mouseY, button)) {
                    ci.cancel(); // Cancel the event if it was handled by the visualizer
                }
            } else if (action == 0) { // GLFW_RELEASE
                if (ShapeVisualizerTool.getInstance().handleMouseRelease(mouseX, mouseY, button)) {
                    ci.cancel(); // Cancel the event if it was handled by the visualizer
                }
            }
        }
    }

    /**
     * Intercepts mouse drag events to handle them for the Shape Visualizer when it's active.
     */
    @Inject(method = "onCursorPos", at = @At("HEAD"), cancellable = true)
    private void onCursorPos(long window, double x, double y, CallbackInfo ci) {
        if (ShapeVisualizerTool.isActive()) {
            // Get mouse position and calculate delta
            double mouseX = x;
            double mouseY = y;
            double deltaX = mouseX - client.mouse.getX();
            double deltaY = mouseY - client.mouse.getY();

            // Handle mouse drag
            if (deltaX != 0 || deltaY != 0) {
                if (ShapeVisualizerTool.getInstance().handleMouseDrag(mouseX, mouseY, 0, deltaX, deltaY)) {
                    ci.cancel(); // Cancel the event if it was handled by the visualizer
                }
            }
        }
    }

    /**
     * Intercepts mouse scroll events to handle them for the Shape Visualizer when it's active.
     */
    @Inject(method = "onMouseScroll", at = @At("HEAD"), cancellable = true)
    private void onMouseScroll(long window, double horizontal, double vertical, CallbackInfo ci) {
        if (ShapeVisualizerTool.isActive()) {
            // Get mouse position
            double mouseX = client.mouse.getX();
            double mouseY = client.mouse.getY();

            // Handle mouse scroll
            if (ShapeVisualizerTool.getInstance().handleMouseScroll(mouseX, mouseY, vertical)) {
                ci.cancel(); // Cancel the event if it was handled by the visualizer
            }
        }
    }
}
