package com.pokecobble.mixin;

import com.pokecobble.town.claim.ClaimTool;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.screen.Screen;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

/**
 * Mixin to prevent the pause screen from opening when the claim tool is active.
 */
@Mixin(MinecraftClient.class)
public class MinecraftClientMixin {

    /**
     * Prevents the pause screen from opening when the claim tool is active.
     */
    @Inject(method = "setScreen", at = @At("HEAD"), cancellable = true)
    private void onSetScreen(Screen screen, CallbackInfo ci) {
        MinecraftClient client = (MinecraftClient)(Object)this;
        Screen currentScreen = client.currentScreen;

        // If the claim tool is active and we're trying to open the pause screen
        if (ClaimTool.getInstance().isActive() &&
            screen != null &&
            screen.getClass().getSimpleName().equals("GameMenuScreen")) {

            // Only show exit confirmation if there's no screen currently open
            // This allows ESC to close screens normally when the claim tool is active
            if (currentScreen == null) {
                // Show the exit confirmation dialog instead
                ClaimTool.getInstance().showExitConfirmation();

                // Cancel opening the pause screen
                ci.cancel();
            }
        }
    }
}
