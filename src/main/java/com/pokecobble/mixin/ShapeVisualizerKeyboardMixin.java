package com.pokecobble.mixin;

import com.pokecobble.tools.gui.ShapeVisualizerTool;
import net.minecraft.client.Keyboard;
import net.minecraft.client.MinecraftClient;
import org.spongepowered.asm.mixin.Final;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Shadow;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import org.lwjgl.glfw.GLFW;

/**
 * Mixin to handle keyboard input for the Shape Visualizer tool when used as an overlay.
 */
@Mixin(Keyboard.class)
public class ShapeVisualizerKeyboardMixin {
    @Shadow @Final private MinecraftClient client;

    /**
     * Intercepts key presses to handle them for the Shape Visualizer when it's active.
     */
    @Inject(method = "onKey", at = @At("HEAD"), cancellable = true)
    private void onKey(long window, int key, int scancode, int action, int modifiers, CallbackInfo ci) {
        // Only handle key press events (not releases or repeats)
        if (action != GLFW.GLFW_PRESS) {
            return;
        }

        // Check if the Shape Visualizer is active
        if (ShapeVisualizerTool.isActive()) {
            // Handle key press
            if (ShapeVisualizerTool.getInstance().handleKeyPress(key, scancode, modifiers)) {
                ci.cancel(); // Cancel the event if it was handled by the visualizer
            }
        }
    }
}
