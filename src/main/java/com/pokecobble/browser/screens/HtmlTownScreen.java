package com.pokecobble.browser.screens;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.browser.bridge.JavaScriptBridge;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;

import java.util.List;

/**
 * HTML version of the ModernTownScreen.
 * Displays town management interface using HTML/CSS/JavaScript.
 */
public class HtmlTownScreen extends HtmlScreenBase {
    
    public HtmlTownScreen(Screen parent, String screenId, String htmlFile) {
        super(parent, screenId, htmlFile, Text.literal("Town Management"));
    }
    
    @Override
    protected void init() {
        super.init();
        
        // Set up town-specific JavaScript bridge handlers
        setupTownBridgeHandlers();
        
        // Load initial town data
        loadTownData();
    }
    
    /**
     * Sets up JavaScript bridge handlers specific to town management.
     */
    private void setupTownBridgeHandlers() {
        JavaScriptBridge bridge = JavaScriptBridge.getInstance();
        
        // Get all towns handler
        bridge.registerHandler("getTowns", (data) -> {
            try {
                java.util.Collection<Town> townCollection = TownManager.getInstance().getAllTowns();
                java.util.List<Town> towns = new java.util.ArrayList<>(townCollection);
                Pokecobbleclaim.LOGGER.debug("Sending " + towns.size() + " towns to HTML interface");
                return towns;
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error getting towns for HTML interface", e);
                return null;
            }
        });

        // Get player's town handler
        bridge.registerHandler("getPlayerTown", (data) -> {
            try {
                if (client != null && client.player != null) {
                    java.util.UUID playerId = client.player.getUuid();
                    Town playerTown = TownManager.getInstance().getPlayerTown(playerId);
                    if (playerTown != null) {
                        Pokecobbleclaim.LOGGER.debug("Sending player town data to HTML interface: " + playerTown.getName());
                        return playerTown;
                    }
                }
                return null;
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error getting player town for HTML interface", e);
                return null;
            }
        });
        
        // Join town handler
        bridge.registerHandler("joinTown", (data) -> {
            try {
                if (data.has("townId")) {
                    String townId = data.get("townId").getAsString();
                    return handleJoinTown(townId);
                }
                return "Missing town ID";
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling join town request", e);
                return "Error: " + e.getMessage();
            }
        });
        
        // Leave town handler
        bridge.registerHandler("leaveTown", (data) -> {
            try {
                return handleLeaveTown();
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling leave town request", e);
                return "Error: " + e.getMessage();
            }
        });
        
        // Create town handler
        bridge.registerHandler("createTown", (data) -> {
            try {
                if (data.has("townName")) {
                    String townName = data.get("townName").getAsString();
                    return handleCreateTown(townName);
                }
                return "Missing town name";
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error handling create town request", e);
                return "Error: " + e.getMessage();
            }
        });
        
        // Get town details handler
        bridge.registerHandler("getTownDetails", (data) -> {
            try {
                if (data.has("townId")) {
                    String townId = data.get("townId").getAsString();
                    Town town = TownManager.getInstance().getTownById(java.util.UUID.fromString(townId));
                    if (town != null) {
                        return createTownDetailsObject(town);
                    }
                }
                return null;
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error getting town details", e);
                return null;
            }
        });
    }
    
    /**
     * Loads initial town data and sends it to the HTML interface.
     */
    private void loadTownData() {
        if (browserReady) {
            try {
                // Send initial data to the HTML interface
                JavaScriptBridge bridge = JavaScriptBridge.getInstance();
                
                // Send all towns
                java.util.Collection<Town> townCollection = TownManager.getInstance().getAllTowns();
                java.util.List<Town> towns = new java.util.ArrayList<>(townCollection);
                bridge.sendToBrowser(screenId, "townsLoaded", towns);

                // Send player's town if they have one
                if (client != null && client.player != null) {
                    java.util.UUID playerId = client.player.getUuid();
                    Town playerTown = TownManager.getInstance().getPlayerTown(playerId);
                    if (playerTown != null) {
                        bridge.sendToBrowser(screenId, "playerTownLoaded", playerTown);
                    }
                }
                
                Pokecobbleclaim.LOGGER.debug("Initial town data sent to HTML interface");
                
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error loading initial town data", e);
            }
        }
    }
    
    /**
     * Handles joining a town request from the HTML interface.
     */
    private String handleJoinTown(String townId) {
        try {
            if (client == null || client.player == null) {
                return "Player not available";
            }
            
            java.util.UUID playerId = client.player.getUuid();

            // Check if player is already in a town
            Town currentTown = TownManager.getInstance().getPlayerTown(playerId);
            if (currentTown != null) {
                return "You are already in a town: " + currentTown.getName();
            }
            
            // Find the town to join
            Town targetTown = TownManager.getInstance().getTownById(java.util.UUID.fromString(townId));
            if (targetTown == null) {
                return "Town not found";
            }
            
            // For now, just log the request - in a full implementation this would
            // send a network packet to the server to handle the join request
            String playerName = client.player.getName().getString();
            Pokecobbleclaim.LOGGER.info("Player " + playerName + " requested to join town: " + targetTown.getName());

            return "Join request sent for town: " + targetTown.getName();
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling join town request", e);
            return "Error processing join request";
        }
    }
    
    /**
     * Handles leaving a town request from the HTML interface.
     */
    private String handleLeaveTown() {
        try {
            if (client == null || client.player == null) {
                return "Player not available";
            }
            
            String playerName = client.player.getName().getString();
            java.util.UUID playerId = client.player.getUuid();
            Town currentTown = TownManager.getInstance().getPlayerTown(playerId);

            if (currentTown == null) {
                return "You are not in a town";
            }

            // For now, just log the request - in a full implementation this would
            // send a network packet to the server to handle the leave request
            Pokecobbleclaim.LOGGER.info("Player " + playerName + " requested to leave town: " + currentTown.getName());
            
            return "Leave request sent for town: " + currentTown.getName();
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling leave town request", e);
            return "Error processing leave request";
        }
    }
    
    /**
     * Handles creating a new town request from the HTML interface.
     */
    private String handleCreateTown(String townName) {
        try {
            if (client == null || client.player == null) {
                return "Player not available";
            }
            
            String playerName = client.player.getName().getString();
            java.util.UUID playerId = client.player.getUuid();

            // Check if player is already in a town
            Town currentTown = TownManager.getInstance().getPlayerTown(playerId);
            if (currentTown != null) {
                return "You are already in a town: " + currentTown.getName();
            }
            
            // Validate town name
            if (townName == null || townName.trim().isEmpty()) {
                return "Town name cannot be empty";
            }
            
            if (townName.length() > 32) {
                return "Town name too long (max 32 characters)";
            }
            
            // For now, just log the request - in a full implementation this would
            // send a network packet to the server to handle the creation
            Pokecobbleclaim.LOGGER.info("Player " + playerName + " requested to create town: " + townName);
            
            return "Town creation request sent: " + townName;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling create town request", e);
            return "Error processing creation request";
        }
    }
    
    /**
     * Creates a detailed town object for the HTML interface.
     */
    private Object createTownDetailsObject(Town town) {
        // Create a simplified town object with the data needed by the HTML interface
        java.util.Map<String, Object> details = new java.util.HashMap<>();
        details.put("id", town.getId().toString());
        details.put("name", town.getName());
        details.put("playerCount", town.getPlayerCount());
        details.put("description", town.getDescription());
        details.put("isPublic", town.isOpen()); // Use isOpen() instead of isPublic()
        details.put("createdDate", "Unknown"); // Town class doesn't have getCreatedDate(), use placeholder
        
        // Add player list if available
        if (town.getPlayers() != null) {
            details.put("players", town.getPlayers());
        }
        
        return details;
    }
    
    @Override
    protected void setupBrowserCallbacks() {
        super.setupBrowserCallbacks();
        
        // Additional setup specific to town screen
        Pokecobbleclaim.LOGGER.debug("Setting up town-specific browser callbacks");
    }
    
    @Override
    public void close() {
        // Clean up town-specific resources if needed
        super.close();
    }
}
