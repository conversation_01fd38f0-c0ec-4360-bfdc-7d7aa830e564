package com.pokecobble.browser.screens;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.browser.core.BrowserManager;
import com.pokecobble.browser.core.BrowserConfig;
import com.pokecobble.browser.core.SwingBrowser;
import com.pokecobble.browser.renderer.HtmlRenderer;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;

import java.awt.image.BufferedImage;
import java.util.List;

/**
 * Base class for HTML-based screens in Minecraft.
 * Handles browser integration, rendering, and input forwarding.
 */
public abstract class HtmlScreenBase extends Screen {
    protected final Screen parent;
    protected final String screenId;
    protected final String htmlFile;
    
    protected SwingBrowser browser; // SwingBrowser instance
    protected boolean browserReady = false;
    protected boolean initializationFailed = false;

    // HTML renderer (fallback)
    protected HtmlRenderer htmlRenderer;
    
    // Rendering properties
    protected int browserWidth;
    protected int browserHeight;
    protected BufferedImage browserImage;
    
    // Input handling
    protected boolean mousePressed = false;
    protected int lastMouseX = 0;
    protected int lastMouseY = 0;
    
    /**
     * Creates a new HTML-based screen.
     * @param parent The parent screen to return to
     * @param screenId Unique identifier for this screen
     * @param htmlFile The HTML file to load (relative to resources)
     * @param title The screen title
     */
    public HtmlScreenBase(Screen parent, String screenId, String htmlFile, Text title) {
        super(title);
        this.parent = parent;
        this.screenId = screenId;
        this.htmlFile = htmlFile;
        
        // Initialize browser dimensions
        BrowserConfig config = BrowserConfig.getInstance();
        this.browserWidth = config.getBrowserWidth();
        this.browserHeight = config.getBrowserHeight();
    }
    
    @Override
    protected void init() {
        super.init();
        
        // Adjust browser size to screen if needed
        if (width > 0 && height > 0) {
            browserWidth = Math.min(browserWidth, width - 40); // Leave some margin
            browserHeight = Math.min(browserHeight, height - 40);
        }
        
        initializeBrowser();
    }
    
    /**
     * Initializes the browser for this screen.
     */
    protected void initializeBrowser() {
        try {
            Pokecobbleclaim.LOGGER.info("Initializing SwingBrowser for screen: " + screenId);

            // Get browser instance from manager (MCEF or SwingBrowser)
            browser = BrowserManager.getInstance().getBrowserForScreen(screenId, htmlFile);

            if (browser != null && browser.isContentLoaded()) {
                setupBrowserCallbacks();
                browserReady = true;
                Pokecobbleclaim.LOGGER.info("SwingBrowser initialized successfully for screen: " + screenId);
            } else {
                Pokecobbleclaim.LOGGER.warn("SwingBrowser failed, falling back to HTML renderer for screen: " + screenId);

                // Fallback to HTML renderer
                htmlRenderer = new HtmlRenderer(textRenderer);
                if (htmlRenderer.loadHtmlFromResource(htmlFile)) {
                    browserReady = true;
                    Pokecobbleclaim.LOGGER.info("HTML renderer fallback initialized for screen: " + screenId);
                } else {
                    Pokecobbleclaim.LOGGER.error("Both SwingBrowser and HTML renderer failed for screen: " + screenId);
                    initializationFailed = true;
                }
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Exception during browser initialization for screen: " + screenId, e);
            initializationFailed = true;
        }
    }
    
    /**
     * Sets up browser callbacks and event handlers.
     */
    protected void setupBrowserCallbacks() {
        // This will be expanded when we integrate the JavaScript bridge
        Pokecobbleclaim.LOGGER.debug("Setting up browser callbacks for screen: " + screenId);
    }


    
    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Render background
        this.renderBackground(context);
        
        if (initializationFailed) {
            renderFallbackError(context);
            return;
        }
        
        if (!browserReady || browser == null) {
            renderLoadingScreen(context);
            return;
        }
        
        // Render browser content
        renderBrowserContent(context, mouseX, mouseY);
        
        // Render any overlays
        renderOverlays(context, mouseX, mouseY, delta);
    }
    
    /**
     * Renders the browser content to the screen.
     */
    protected void renderBrowserContent(DrawContext context, int mouseX, int mouseY) {
        try {
            if (browser != null && browser.isContentLoaded()) {
                renderSwingBrowserContent(context);
            } else if (htmlRenderer != null) {
                renderHtmlContent(context);
            } else {
                renderBrowserPlaceholder(context);
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error rendering browser content for screen: " + screenId, e);
            renderFallbackError(context);
        }
    }

    /**
     * Renders the SwingBrowser content to the screen.
     */
    protected void renderSwingBrowserContent(DrawContext context) {
        // Calculate centered position
        int x = (width - browserWidth) / 2;
        int y = (height - browserHeight) / 2;

        // Draw browser window frame
        context.fill(x, y, x + browserWidth, y + browserHeight, 0xFF1E1E1E);
        context.drawBorder(x - 1, y - 1, browserWidth + 2, browserHeight + 2, 0xFF007ACC);

        // Draw title bar
        context.fill(x, y, x + browserWidth, y + 30, 0xFF2D2D30);
        context.drawText(textRenderer, "PokeCobbleClaim - Real HTML Browser", x + 10, y + 10, 0xFFFFFFFF, false);

        // Draw close button
        context.fill(x + browserWidth - 25, y + 5, x + browserWidth - 5, y + 25, 0xFFFF4444);
        context.drawText(textRenderer, "X", x + browserWidth - 18, y + 10, 0xFFFFFFFF, false);

        // Render actual browser content
        try {
            BufferedImage browserImage = browser.renderToImage();
            if (browserImage != null) {
                Pokecobbleclaim.LOGGER.debug("Got BufferedImage from browser: " + browserImage.getWidth() + "x" + browserImage.getHeight());

                // Debug: Save image to file for inspection (only in debug mode)
                if (Pokecobbleclaim.LOGGER.isDebugEnabled()) {
                    saveDebugImage(browserImage);
                }

                // Convert BufferedImage to Minecraft texture and render
                renderBufferedImageToMinecraft(context, browserImage, x, y + 30, browserWidth, browserHeight - 30);
            } else {
                // Show loading message
                String loadingText = "Rendering HTML content...";
                int textWidth = textRenderer.getWidth(loadingText);
                context.drawText(textRenderer, loadingText, x + (browserWidth - textWidth) / 2, y + browserHeight / 2, 0xFFFFFF00, false);
                Pokecobbleclaim.LOGGER.debug("Browser returned null image");
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error rendering SwingBrowser image", e);
            String errorText = "Error rendering browser content";
            int textWidth = textRenderer.getWidth(errorText);
            context.drawText(textRenderer, errorText, x + (browserWidth - textWidth) / 2, y + browserHeight / 2, 0xFFFF0000, false);
        }
    }

    /**
     * Renders a BufferedImage to Minecraft's rendering system.
     * Converts the BufferedImage to a Minecraft texture and renders it.
     */
    protected void renderBufferedImageToMinecraft(DrawContext context, BufferedImage image, int x, int y, int maxWidth, int maxHeight) {
        if (image == null) {
            // Show loading message if no image
            String loadingText = "Rendering HTML content...";
            int textWidth = textRenderer.getWidth(loadingText);
            context.drawText(textRenderer, loadingText, x + (maxWidth - textWidth) / 2, y + maxHeight / 2, 0xFFFFFF00, false);
            return;
        }

        try {
            // Convert BufferedImage to NativeImage
            net.minecraft.client.texture.NativeImage nativeImage = convertBufferedImageToNativeImage(image);

            if (nativeImage != null) {
                // Create a unique identifier for this texture
                String textureId = "pokecobbleclaim_html_" + screenId + "_" + System.currentTimeMillis();
                net.minecraft.util.Identifier identifier = new net.minecraft.util.Identifier("pokecobbleclaim", textureId);

                // Create texture from NativeImage
                net.minecraft.client.texture.NativeImageBackedTexture texture =
                    new net.minecraft.client.texture.NativeImageBackedTexture(nativeImage);

                // Register texture with Minecraft's texture manager
                net.minecraft.client.MinecraftClient.getInstance().getTextureManager().registerTexture(identifier, texture);

                // Calculate scaling to fit within maxWidth/maxHeight while maintaining aspect ratio
                int imageWidth = image.getWidth();
                int imageHeight = image.getHeight();

                float scaleX = (float) maxWidth / imageWidth;
                float scaleY = (float) maxHeight / imageHeight;
                float scale = Math.min(scaleX, scaleY);

                int scaledWidth = (int) (imageWidth * scale);
                int scaledHeight = (int) (imageHeight * scale);

                // Center the image
                int centeredX = x + (maxWidth - scaledWidth) / 2;
                int centeredY = y + (maxHeight - scaledHeight) / 2;

                // Render the texture
                context.drawTexture(identifier, centeredX, centeredY, 0, 0, scaledWidth, scaledHeight, scaledWidth, scaledHeight);

                Pokecobbleclaim.LOGGER.debug("Successfully rendered HTML content as texture: " + scaledWidth + "x" + scaledHeight);

                // Clean up texture after a delay to prevent memory leaks
                scheduleTextureCleanup(identifier);

            } else {
                // Fallback if conversion failed
                renderImageConversionError(context, x, y, maxWidth, maxHeight);
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error rendering BufferedImage to Minecraft texture", e);
            renderImageConversionError(context, x, y, maxWidth, maxHeight);
        }
    }

    /**
     * Converts a BufferedImage to a NativeImage for Minecraft rendering.
     */
    private net.minecraft.client.texture.NativeImage convertBufferedImageToNativeImage(BufferedImage bufferedImage) {
        try {
            int width = bufferedImage.getWidth();
            int height = bufferedImage.getHeight();

            // Create NativeImage with RGBA format
            net.minecraft.client.texture.NativeImage nativeImage =
                new net.minecraft.client.texture.NativeImage(width, height, false);

            // Copy pixel data from BufferedImage to NativeImage
            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    int rgb = bufferedImage.getRGB(x, y);

                    // Extract ARGB components
                    int alpha = (rgb >> 24) & 0xFF;
                    int red = (rgb >> 16) & 0xFF;
                    int green = (rgb >> 8) & 0xFF;
                    int blue = rgb & 0xFF;

                    // Convert to ABGR format for NativeImage
                    int abgr = (alpha << 24) | (blue << 16) | (green << 8) | red;

                    nativeImage.setColor(x, y, abgr);
                }
            }

            return nativeImage;

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error converting BufferedImage to NativeImage", e);
            return null;
        }
    }

    /**
     * Renders an error message when image conversion fails.
     */
    private void renderImageConversionError(DrawContext context, int x, int y, int maxWidth, int maxHeight) {
        String errorText = "Error rendering HTML content";
        String fallbackText = "Using fallback renderer...";

        int errorWidth = textRenderer.getWidth(errorText);
        int fallbackWidth = textRenderer.getWidth(fallbackText);

        context.drawText(textRenderer, errorText, x + (maxWidth - errorWidth) / 2, y + maxHeight / 2 - 10, 0xFFFF4444, false);
        context.drawText(textRenderer, fallbackText, x + (maxWidth - fallbackWidth) / 2, y + maxHeight / 2 + 10, 0xFFFFFF00, false);
    }

    /**
     * Schedules cleanup of a texture to prevent memory leaks.
     */
    private void scheduleTextureCleanup(net.minecraft.util.Identifier identifier) {
        // Schedule cleanup after 5 seconds to allow for rendering
        new Thread(() -> {
            try {
                Thread.sleep(5000);
                net.minecraft.client.MinecraftClient.getInstance().execute(() -> {
                    try {
                        net.minecraft.client.MinecraftClient.getInstance().getTextureManager().destroyTexture(identifier);
                        Pokecobbleclaim.LOGGER.debug("Cleaned up texture: " + identifier);
                    } catch (Exception e) {
                        Pokecobbleclaim.LOGGER.debug("Texture already cleaned up: " + identifier);
                    }
                });
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }

    /**
     * Saves a BufferedImage to file for debugging purposes.
     */
    private void saveDebugImage(BufferedImage image) {
        try {
            java.io.File debugDir = new java.io.File("run/debug");
            if (!debugDir.exists()) {
                debugDir.mkdirs();
            }

            java.io.File debugFile = new java.io.File(debugDir, "html_render_" + screenId + "_" + System.currentTimeMillis() + ".png");
            javax.imageio.ImageIO.write(image, "PNG", debugFile);
            Pokecobbleclaim.LOGGER.debug("Saved debug image to: " + debugFile.getAbsolutePath());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to save debug image", e);
        }
    }

    /**
     * Renders the actual HTML content using the HTML renderer.
     */
    protected void renderHtmlContent(DrawContext context) {
        // Calculate centered position
        int x = (width - browserWidth) / 2;
        int y = (height - browserHeight) / 2;

        // Draw browser window frame
        context.fill(x, y, x + browserWidth, y + browserHeight, 0xFF1E1E1E);
        context.drawBorder(x - 1, y - 1, browserWidth + 2, browserHeight + 2, 0xFF007ACC);

        // Draw title bar
        context.fill(x, y, x + browserWidth, y + 30, 0xFF2D2D30);
        context.drawText(textRenderer, "PokeCobbleClaim HTML Interface", x + 10, y + 10, 0xFFFFFFFF, false);

        // Draw close button
        context.fill(x + browserWidth - 25, y + 5, x + browserWidth - 5, y + 25, 0xFFFF4444);
        context.drawText(textRenderer, "X", x + browserWidth - 18, y + 10, 0xFFFFFFFF, false);

        // Render HTML content
        htmlRenderer.render(context, x, y + 30, browserWidth, browserHeight - 30);
    }
    
    /**
     * Renders a placeholder for the browser content.
     */
    protected void renderBrowserPlaceholder(DrawContext context) {
        // Calculate centered position
        int x = (width - browserWidth) / 2;
        int y = (height - browserHeight) / 2;

        // Draw browser area background with gradient effect
        context.fill(x, y, x + browserWidth, y + browserHeight, 0xFF1E1E1E);

        // Draw border with accent color
        context.drawBorder(x - 1, y - 1, browserWidth + 2, browserHeight + 2, 0xFF007ACC);

        // Draw header bar
        context.fill(x, y, x + browserWidth, y + 30, 0xFF2D2D30);

        // Draw title
        String titleText = "PokeCobbleClaim HTML Interface";
        context.drawText(textRenderer, titleText, x + 10, y + 10, 0xFFFFFFFF, false);

        // Draw close button
        context.fill(x + browserWidth - 25, y + 5, x + browserWidth - 5, y + 25, 0xFFFF4444);
        context.drawText(textRenderer, "X", x + browserWidth - 18, y + 10, 0xFFFFFFFF, false);

        // Draw content area
        renderHtmlContent(context, x, y + 30, browserWidth, browserHeight - 30);
    }

    /**
     * Renders HTML-like content using Minecraft's rendering system.
     */
    protected void renderHtmlContent(DrawContext context, int x, int y, int width, int height) {
        // Simulate HTML content rendering
        int currentY = y + 20;

        // Header
        String headerText = "Town Management";
        int headerWidth = textRenderer.getWidth(headerText);
        context.drawText(textRenderer, headerText, x + (width - headerWidth) / 2, currentY, 0xFF00AAFF, false);
        currentY += 30;

        // Navigation tabs
        renderTab(context, x + 20, currentY, "All Towns", true);
        renderTab(context, x + 120, currentY, "My Town", false);
        renderTab(context, x + 220, currentY, "Create Town", false);
        currentY += 40;

        // Content area
        context.fill(x + 10, currentY, x + width - 10, y + height - 10, 0xFF2D2D30);
        currentY += 10;

        // Sample town cards
        renderTownCard(context, x + 20, currentY, "Development Town", "A town for testing", 5, true);
        renderTownCard(context, x + 20, currentY + 80, "Private Haven", "A private community", 12, false);
        renderTownCard(context, x + 20, currentY + 160, "Mega City", "The largest town", 25, true);

        // Instructions
        String instructionText = "This is a preview of the HTML interface. Press ESC to return.";
        int instructionWidth = textRenderer.getWidth(instructionText);
        context.drawText(textRenderer, instructionText, x + (width - instructionWidth) / 2, y + height - 20, 0xFFFFFF00, false);
    }

    /**
     * Renders a navigation tab.
     */
    protected void renderTab(DrawContext context, int x, int y, String text, boolean active) {
        int tabWidth = 80;
        int tabHeight = 25;

        // Tab background
        int bgColor = active ? 0xFF007ACC : 0xFF3C3C3C;
        context.fill(x, y, x + tabWidth, y + tabHeight, bgColor);

        // Tab border
        context.drawBorder(x, y, tabWidth, tabHeight, 0xFF555555);

        // Tab text
        int textColor = active ? 0xFFFFFFFF : 0xFFCCCCCC;
        int textWidth = textRenderer.getWidth(text);
        context.drawText(textRenderer, text, x + (tabWidth - textWidth) / 2, y + 8, textColor, false);
    }

    /**
     * Renders a town card.
     */
    protected void renderTownCard(DrawContext context, int x, int y, String name, String description, int players, boolean isPublic) {
        int cardWidth = browserWidth - 40;
        int cardHeight = 60;

        // Card background
        context.fill(x, y, x + cardWidth, y + cardHeight, 0xFF3C3C3C);
        context.drawBorder(x, y, cardWidth, cardHeight, 0xFF555555);

        // Town name
        context.drawText(textRenderer, name, x + 10, y + 8, 0xFFFFFFFF, false);

        // Town description
        context.drawText(textRenderer, description, x + 10, y + 22, 0xFFCCCCCC, false);

        // Player count
        String playerText = players + " players";
        context.drawText(textRenderer, playerText, x + 10, y + 36, 0xFF00FF00, false);

        // Public/Private status
        String statusText = isPublic ? "Public" : "Private";
        int statusColor = isPublic ? 0xFF00FF00 : 0xFFFF8800;
        int statusWidth = textRenderer.getWidth(statusText);
        context.drawText(textRenderer, statusText, x + cardWidth - statusWidth - 10, y + 8, statusColor, false);

        // Join button
        int buttonWidth = 50;
        int buttonHeight = 20;
        int buttonX = x + cardWidth - buttonWidth - 10;
        int buttonY = y + cardHeight - buttonHeight - 8;

        context.fill(buttonX, buttonY, buttonX + buttonWidth, buttonY + buttonHeight, 0xFF007ACC);
        context.drawBorder(buttonX, buttonY, buttonWidth, buttonHeight, 0xFF005A9E);

        String buttonText = "Join";
        int buttonTextWidth = textRenderer.getWidth(buttonText);
        context.drawText(textRenderer, buttonText, buttonX + (buttonWidth - buttonTextWidth) / 2, buttonY + 6, 0xFFFFFFFF, false);
    }
    
    /**
     * Renders a loading screen while the browser initializes.
     */
    protected void renderLoadingScreen(DrawContext context) {
        String loadingText = "Loading HTML Interface...";
        int textWidth = textRenderer.getWidth(loadingText);
        context.drawText(textRenderer, loadingText, (width - textWidth) / 2, height / 2, 0xFFFFFFFF, false);
    }
    
    /**
     * Renders an error screen when browser initialization fails.
     */
    protected void renderFallbackError(DrawContext context) {
        String errorText = "HTML Interface Failed to Load";
        String fallbackText = "Falling back to Minecraft GUI...";
        String instructionText = "Press ESC to continue";
        
        int errorWidth = textRenderer.getWidth(errorText);
        int fallbackWidth = textRenderer.getWidth(fallbackText);
        int instructionWidth = textRenderer.getWidth(instructionText);
        
        context.drawText(textRenderer, errorText, (width - errorWidth) / 2, height / 2 - 20, 0xFFFF0000, false);
        context.drawText(textRenderer, fallbackText, (width - fallbackWidth) / 2, height / 2, 0xFFFFFF00, false);
        context.drawText(textRenderer, instructionText, (width - instructionWidth) / 2, height / 2 + 20, 0xFFCCCCCC, false);
    }
    
    /**
     * Renders any additional overlays (can be overridden by subclasses).
     */
    protected void renderOverlays(DrawContext context, int mouseX, int mouseY, float delta) {
        // Default implementation does nothing
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (browserReady && browser != null) {
            // Handle clicks on the HTML interface
            return handleHtmlClick(mouseX, mouseY, button);
        }
        return super.mouseClicked(mouseX, mouseY, button);
    }

    /**
     * Handles mouse clicks on the HTML interface.
     */
    protected boolean handleHtmlClick(double mouseX, double mouseY, int button) {
        int x = (width - browserWidth) / 2;
        int y = (height - browserHeight) / 2;

        // Check if click is within browser area
        if (mouseX >= x && mouseX <= x + browserWidth && mouseY >= y && mouseY <= y + browserHeight) {
            // Check close button
            if (mouseX >= x + browserWidth - 25 && mouseX <= x + browserWidth - 5 &&
                mouseY >= y + 5 && mouseY <= y + 25) {
                this.close();
                return true;
            }

            // Forward click to browser if available
            if (browser != null && browser.isContentLoaded()) {
                // Convert coordinates to browser space
                int browserX = (int)(mouseX - x);
                int browserY = (int)(mouseY - y - 30); // Account for title bar

                if (browserX >= 0 && browserY >= 0) {
                    browser.handleMouseClick(browserX, browserY, button);
                    Pokecobbleclaim.LOGGER.debug("Forwarded click to SwingBrowser: " + browserX + ", " + browserY);
                }
                return true;
            }

            // Fallback to HTML renderer
            if (htmlRenderer != null) {
                List<HtmlRenderer.HtmlElement> clickedElements = htmlRenderer.getElementsAt(
                    (int)mouseX, (int)mouseY, x, y + 30);

                for (HtmlRenderer.HtmlElement element : clickedElements) {
                    if ("button".equals(element.type)) {
                        onHtmlElementClicked(element);
                        return true;
                    }
                }
            }

            return true; // Consume click within browser area
        }

        return false;
    }

    /**
     * Called when an HTML element is clicked.
     */
    protected void onHtmlElementClicked(HtmlRenderer.HtmlElement element) {
        Pokecobbleclaim.LOGGER.info("HTML element clicked: " + element.type + " - " + element.text);

        if ("button".equals(element.type)) {
            if (element.className != null) {
                if (element.className.contains("join-btn")) {
                    onJoinButtonClicked(element.text);
                } else if (element.className.contains("close")) {
                    this.close();
                }
            }
        }
    }

    /**
     * Called when a navigation tab is clicked.
     */
    protected void onTabClicked(String tabId) {
        Pokecobbleclaim.LOGGER.info("Tab clicked: " + tabId);
        // In a full implementation, this would switch the displayed content
    }

    /**
     * Called when a join button is clicked.
     */
    protected void onJoinButtonClicked(String buttonText) {
        Pokecobbleclaim.LOGGER.info("Join button clicked: " + buttonText);
        // In a full implementation, this would send a join request to the server
    }
    
    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        if (browserReady && browser != null) {
            // Forward mouse drag events to browser
            forwardMouseDrag(mouseX, mouseY, button, deltaX, deltaY);
            return true;
        }
        return super.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
    }
    
    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        if (browserReady && browser != null) {
            // Forward key events to browser
            forwardKeyPress(keyCode, scanCode, modifiers);
        }
        return super.keyPressed(keyCode, scanCode, modifiers);
    }
    
    /**
     * Forwards mouse click events to the browser.
     */
    protected void forwardMouseClick(double mouseX, double mouseY, int button) {
        // Implementation will be added when we have proper browser integration
        Pokecobbleclaim.LOGGER.debug("Mouse click forwarded to browser: " + mouseX + ", " + mouseY + ", button: " + button);
    }
    
    /**
     * Forwards mouse drag events to the browser.
     */
    protected void forwardMouseDrag(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        // Implementation will be added when we have proper browser integration
        Pokecobbleclaim.LOGGER.debug("Mouse drag forwarded to browser");
    }
    
    /**
     * Forwards key press events to the browser.
     */
    protected void forwardKeyPress(int keyCode, int scanCode, int modifiers) {
        // Implementation will be added when we have proper browser integration
        Pokecobbleclaim.LOGGER.debug("Key press forwarded to browser: " + keyCode);
    }
    
    @Override
    public void close() {
        cleanup();
        super.close();
        
        // Return to parent screen
        if (client != null) {
            client.setScreen(parent);
        }
    }
    
    /**
     * Cleans up browser resources.
     */
    protected void cleanup() {
        if (browser != null) {
            try {
                BrowserManager.getInstance().releaseBrowser(screenId);
                browser = null;
                browserReady = false;
                Pokecobbleclaim.LOGGER.debug("Cleaned up browser for screen: " + screenId);
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error cleaning up browser for screen: " + screenId, e);
            }
        }
    }
    
    @Override
    public boolean shouldPause() {
        // Don't pause the game for HTML screens
        return false;
    }
    
    /**
     * Gets the screen identifier.
     */
    public String getScreenId() {
        return screenId;
    }
    
    /**
     * Gets the HTML file being displayed.
     */
    public String getHtmlFile() {
        return htmlFile;
    }
    
    /**
     * Checks if the browser is ready.
     */
    public boolean isBrowserReady() {
        return browserReady;
    }
}
