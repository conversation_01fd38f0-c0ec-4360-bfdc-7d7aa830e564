package com.pokecobble.browser.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.browser.core.BrowserConfig;
import com.pokecobble.browser.core.BrowserEngine;
import com.pokecobble.browser.core.BrowserManager;
import com.pokecobble.browser.ui.ScreenToggleManager;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.command.v2.ClientCommandManager;
import net.fabricmc.fabric.api.client.command.v2.FabricClientCommandSource;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

/**
 * Command for managing browser UI toggle and configuration.
 * Provides commands to switch between Minecraft GUI and HTML interface.
 */
@Environment(EnvType.CLIENT)
public class BrowserToggleCommand {
    
    /**
     * Registers the browser toggle commands.
     */
    public static void register(CommandDispatcher<FabricClientCommandSource> dispatcher) {
        dispatcher.register(
            ClientCommandManager.literal("pokecobbleclaim")
                .then(ClientCommandManager.literal("ui")
                    .then(ClientCommandManager.literal("toggle")
                        .executes(BrowserToggleCommand::toggleUi)
                    )
                    .then(ClientCommandManager.literal("mode")
                        .then(ClientCommandManager.argument("mode", StringArgumentType.string())
                            .suggests((context, builder) -> {
                                builder.suggest("html");
                                builder.suggest("minecraft");
                                return builder.buildFuture();
                            })
                            .executes(BrowserToggleCommand::setUiMode)
                        )
                    )
                    .then(ClientCommandManager.literal("status")
                        .executes(BrowserToggleCommand::showStatus)
                    )
                    .then(ClientCommandManager.literal("reload")
                        .executes(BrowserToggleCommand::reloadBrowser)
                    )
                    .then(ClientCommandManager.literal("debug")
                        .executes(BrowserToggleCommand::showDebugInfo)
                    )
                )
                .then(ClientCommandManager.literal("browser")
                    .then(ClientCommandManager.literal("init")
                        .executes(BrowserToggleCommand::initializeBrowser)
                    )
                    .then(ClientCommandManager.literal("shutdown")
                        .executes(BrowserToggleCommand::shutdownBrowser)
                    )
                    .then(ClientCommandManager.literal("config")
                        .executes(BrowserToggleCommand::showConfig)
                    )
                )
        );
    }
    
    /**
     * Toggles between HTML UI and Minecraft GUI.
     */
    private static int toggleUi(CommandContext<FabricClientCommandSource> context) {
        try {
            ScreenToggleManager toggleManager = ScreenToggleManager.getInstance();
            boolean newMode = toggleManager.toggleUiMode();
            
            String modeText = newMode ? "HTML Interface" : "Minecraft GUI";
            Formatting color = newMode ? Formatting.GREEN : Formatting.YELLOW;
            
            context.getSource().sendFeedback(
                Text.literal("UI Mode switched to: ").formatted(Formatting.GRAY)
                    .append(Text.literal(modeText).formatted(color))
            );
            
            // Show additional info if switching to HTML mode
            if (newMode) {
                BrowserEngine engine = BrowserEngine.getInstance();
                if (!engine.isInitialized()) {
                    context.getSource().sendFeedback(
                        Text.literal("Note: Browser engine is not initialized. Use '/pokecobbleclaim browser init' to initialize.")
                            .formatted(Formatting.YELLOW)
                    );
                }
            }
            
            return 1;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error toggling UI mode", e);
            context.getSource().sendError(
                Text.literal("Error toggling UI mode: " + e.getMessage()).formatted(Formatting.RED)
            );
            return 0;
        }
    }
    
    /**
     * Sets a specific UI mode.
     */
    private static int setUiMode(CommandContext<FabricClientCommandSource> context) {
        try {
            String mode = StringArgumentType.getString(context, "mode").toLowerCase();
            ScreenToggleManager toggleManager = ScreenToggleManager.getInstance();
            
            boolean htmlMode;
            switch (mode) {
                case "html":
                    htmlMode = true;
                    break;
                case "minecraft":
                case "mc":
                    htmlMode = false;
                    break;
                default:
                    context.getSource().sendError(
                        Text.literal("Invalid mode. Use 'html' or 'minecraft'.").formatted(Formatting.RED)
                    );
                    return 0;
            }
            
            toggleManager.setUiMode(htmlMode);
            
            String modeText = htmlMode ? "HTML Interface" : "Minecraft GUI";
            Formatting color = htmlMode ? Formatting.GREEN : Formatting.YELLOW;
            
            context.getSource().sendFeedback(
                Text.literal("UI Mode set to: ").formatted(Formatting.GRAY)
                    .append(Text.literal(modeText).formatted(color))
            );
            
            return 1;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error setting UI mode", e);
            context.getSource().sendError(
                Text.literal("Error setting UI mode: " + e.getMessage()).formatted(Formatting.RED)
            );
            return 0;
        }
    }
    
    /**
     * Shows the current UI status.
     */
    private static int showStatus(CommandContext<FabricClientCommandSource> context) {
        try {
            BrowserConfig config = BrowserConfig.getInstance();
            BrowserEngine engine = BrowserEngine.getInstance();
            BrowserManager manager = BrowserManager.getInstance();
            ScreenToggleManager toggleManager = ScreenToggleManager.getInstance();
            
            // Current mode
            String currentMode = config.isHtmlUiEnabled() ? "HTML Interface" : "Minecraft GUI";
            Formatting modeColor = config.isHtmlUiEnabled() ? Formatting.GREEN : Formatting.YELLOW;
            
            context.getSource().sendFeedback(
                Text.literal("=== PokeCobbleClaim UI Status ===").formatted(Formatting.GOLD)
            );
            
            context.getSource().sendFeedback(
                Text.literal("Current Mode: ").formatted(Formatting.GRAY)
                    .append(Text.literal(currentMode).formatted(modeColor))
            );
            
            // Browser engine status
            String engineStatus;
            Formatting engineColor;
            if (engine.isInitialized()) {
                engineStatus = "Initialized";
                engineColor = Formatting.GREEN;
            } else if (engine.hasInitializationFailed()) {
                engineStatus = "Failed";
                engineColor = Formatting.RED;
            } else {
                engineStatus = "Not Initialized";
                engineColor = Formatting.YELLOW;
            }
            
            context.getSource().sendFeedback(
                Text.literal("Browser Engine: ").formatted(Formatting.GRAY)
                    .append(Text.literal(engineStatus).formatted(engineColor))
            );
            
            // Active browsers
            context.getSource().sendFeedback(
                Text.literal("Active Browsers: ").formatted(Formatting.GRAY)
                    .append(Text.literal(String.valueOf(manager.getActiveBrowserCount())).formatted(Formatting.AQUA))
            );
            
            // Fallback setting
            String fallbackStatus = config.isEnableFallback() ? "Enabled" : "Disabled";
            Formatting fallbackColor = config.isEnableFallback() ? Formatting.GREEN : Formatting.RED;
            
            context.getSource().sendFeedback(
                Text.literal("Fallback to MC GUI: ").formatted(Formatting.GRAY)
                    .append(Text.literal(fallbackStatus).formatted(fallbackColor))
            );
            
            return 1;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error showing status", e);
            context.getSource().sendError(
                Text.literal("Error showing status: " + e.getMessage()).formatted(Formatting.RED)
            );
            return 0;
        }
    }
    
    /**
     * Reloads the current browser.
     */
    private static int reloadBrowser(CommandContext<FabricClientCommandSource> context) {
        try {
            // This would reload the current browser if one is active
            context.getSource().sendFeedback(
                Text.literal("Browser reload functionality not yet implemented.").formatted(Formatting.YELLOW)
            );
            
            return 1;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error reloading browser", e);
            context.getSource().sendError(
                Text.literal("Error reloading browser: " + e.getMessage()).formatted(Formatting.RED)
            );
            return 0;
        }
    }
    
    /**
     * Shows debug information.
     */
    private static int showDebugInfo(CommandContext<FabricClientCommandSource> context) {
        try {
            BrowserManager manager = BrowserManager.getInstance();
            ScreenToggleManager toggleManager = ScreenToggleManager.getInstance();
            
            context.getSource().sendFeedback(
                Text.literal("=== Debug Information ===").formatted(Formatting.GOLD)
            );
            
            context.getSource().sendFeedback(
                Text.literal(manager.getBrowserInfo()).formatted(Formatting.GRAY)
            );
            
            context.getSource().sendFeedback(
                Text.literal(toggleManager.getStats()).formatted(Formatting.GRAY)
            );
            
            return 1;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error showing debug info", e);
            context.getSource().sendError(
                Text.literal("Error showing debug info: " + e.getMessage()).formatted(Formatting.RED)
            );
            return 0;
        }
    }
    
    /**
     * Initializes the browser engine.
     */
    private static int initializeBrowser(CommandContext<FabricClientCommandSource> context) {
        try {
            BrowserEngine engine = BrowserEngine.getInstance();
            
            if (engine.isInitialized()) {
                context.getSource().sendFeedback(
                    Text.literal("Browser engine is already initialized.").formatted(Formatting.YELLOW)
                );
                return 1;
            }
            
            context.getSource().sendFeedback(
                Text.literal("Initializing browser engine...").formatted(Formatting.GRAY)
            );
            
            engine.initializeAsync().thenAccept(success -> {
                if (success) {
                    context.getSource().sendFeedback(
                        Text.literal("Browser engine initialized successfully!").formatted(Formatting.GREEN)
                    );
                } else {
                    context.getSource().sendError(
                        Text.literal("Failed to initialize browser engine.").formatted(Formatting.RED)
                    );
                }
            });
            
            return 1;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error initializing browser", e);
            context.getSource().sendError(
                Text.literal("Error initializing browser: " + e.getMessage()).formatted(Formatting.RED)
            );
            return 0;
        }
    }
    
    /**
     * Shuts down the browser engine.
     */
    private static int shutdownBrowser(CommandContext<FabricClientCommandSource> context) {
        try {
            BrowserEngine engine = BrowserEngine.getInstance();
            BrowserManager manager = BrowserManager.getInstance();
            
            context.getSource().sendFeedback(
                Text.literal("Shutting down browser engine...").formatted(Formatting.GRAY)
            );
            
            manager.shutdown();
            engine.shutdown();
            
            context.getSource().sendFeedback(
                Text.literal("Browser engine shut down successfully.").formatted(Formatting.GREEN)
            );
            
            return 1;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error shutting down browser", e);
            context.getSource().sendError(
                Text.literal("Error shutting down browser: " + e.getMessage()).formatted(Formatting.RED)
            );
            return 0;
        }
    }
    
    /**
     * Shows browser configuration.
     */
    private static int showConfig(CommandContext<FabricClientCommandSource> context) {
        try {
            BrowserConfig config = BrowserConfig.getInstance();
            
            context.getSource().sendFeedback(
                Text.literal("=== Browser Configuration ===").formatted(Formatting.GOLD)
            );
            
            context.getSource().sendFeedback(
                Text.literal(config.getConfigSummary()).formatted(Formatting.GRAY)
            );
            
            return 1;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error showing config", e);
            context.getSource().sendError(
                Text.literal("Error showing config: " + e.getMessage()).formatted(Formatting.RED)
            );
            return 0;
        }
    }
}
