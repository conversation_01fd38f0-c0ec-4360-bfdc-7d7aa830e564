package com.pokecobble.browser.ui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.browser.core.BrowserConfig;
import com.pokecobble.browser.screens.HtmlScreenBase;
import com.pokecobble.browser.screens.HtmlTownScreen;
import com.pokecobble.town.gui.ModernTownScreen;
import com.pokecobble.town.gui.MyTownScreen;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.screen.Screen;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * Manages toggling between Minecraft GUI screens and their HTML equivalents.
 * Provides a centralized way to switch UI modes and handle fallbacks.
 */
public class ScreenToggleManager {
    private static ScreenToggleManager instance;
    
    // Mapping of Minecraft screen classes to their HTML equivalents
    private final Map<Class<? extends Screen>, Function<Screen, HtmlScreenBase>> htmlScreenFactories = new HashMap<>();
    
    // Mapping of HTML screen IDs to their Minecraft equivalents
    private final Map<String, Function<Screen, Screen>> minecraftScreenFactories = new HashMap<>();
    
    private ScreenToggleManager() {
        setupScreenMappings();
    }
    
    /**
     * Gets the singleton instance of the screen toggle manager.
     */
    public static synchronized ScreenToggleManager getInstance() {
        if (instance == null) {
            instance = new ScreenToggleManager();
        }
        return instance;
    }
    
    /**
     * Sets up the mappings between Minecraft screens and HTML screens.
     */
    private void setupScreenMappings() {
        // Town management screens
        htmlScreenFactories.put(ModernTownScreen.class, (parent) ->
            new HtmlTownScreen(parent, "modern_town", "screens/town_management_basic.html")
        );
        
        // Add more screen mappings as they are implemented
        // htmlScreenFactories.put(MyTownScreen.class, (parent) -> 
        //     new HtmlMyTownScreen(parent, "my_town", "screens/my_town.html")
        // );
        
        // Reverse mappings for HTML to Minecraft
        minecraftScreenFactories.put("modern_town", (parent) -> new ModernTownScreen(parent));
        minecraftScreenFactories.put("my_town", (parent) -> new MyTownScreen(parent));
    }
    
    /**
     * Opens a screen using the current UI mode (HTML or Minecraft GUI).
     * @param screenClass The Minecraft screen class to open
     * @param parent The parent screen
     * @return The opened screen, or null if failed
     */
    public Screen openScreen(Class<? extends Screen> screenClass, Screen parent) {
        BrowserConfig config = BrowserConfig.getInstance();
        
        if (config.isHtmlUiEnabled()) {
            // Try to open HTML version
            Screen htmlScreen = openHtmlScreen(screenClass, parent);
            if (htmlScreen != null) {
                return htmlScreen;
            }
            
            // Fallback to Minecraft GUI if HTML fails and fallback is enabled
            if (config.isEnableFallback()) {
                Pokecobbleclaim.LOGGER.warn("HTML screen failed, falling back to Minecraft GUI for: " + screenClass.getSimpleName());
                return openMinecraftScreen(screenClass, parent);
            } else {
                Pokecobbleclaim.LOGGER.error("HTML screen failed and fallback is disabled for: " + screenClass.getSimpleName());
                return null;
            }
        } else {
            // Open Minecraft GUI version
            return openMinecraftScreen(screenClass, parent);
        }
    }
    
    /**
     * Opens the HTML version of a screen.
     * @param screenClass The Minecraft screen class
     * @param parent The parent screen
     * @return The HTML screen or null if not available/failed
     */
    private Screen openHtmlScreen(Class<? extends Screen> screenClass, Screen parent) {
        try {
            Function<Screen, HtmlScreenBase> factory = htmlScreenFactories.get(screenClass);
            if (factory != null) {
                HtmlScreenBase htmlScreen = factory.apply(parent);
                Pokecobbleclaim.LOGGER.info("Opening HTML screen for: " + screenClass.getSimpleName());
                return htmlScreen;
            } else {
                Pokecobbleclaim.LOGGER.debug("No HTML equivalent available for: " + screenClass.getSimpleName());
                return null;
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to create HTML screen for: " + screenClass.getSimpleName(), e);
            return null;
        }
    }
    
    /**
     * Opens the Minecraft GUI version of a screen.
     * @param screenClass The screen class to open
     * @param parent The parent screen
     * @return The Minecraft screen or null if failed
     */
    private Screen openMinecraftScreen(Class<? extends Screen> screenClass, Screen parent) {
        try {
            // Use reflection to create the Minecraft screen
            if (screenClass == ModernTownScreen.class) {
                return new ModernTownScreen(parent);
            } else if (screenClass == MyTownScreen.class) {
                return new MyTownScreen(parent);
            }
            
            // Add more screen types as needed
            
            Pokecobbleclaim.LOGGER.warn("No factory available for Minecraft screen: " + screenClass.getSimpleName());
            return null;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to create Minecraft screen for: " + screenClass.getSimpleName(), e);
            return null;
        }
    }
    
    /**
     * Toggles the UI mode and reopens the current screen if applicable.
     * @return true if UI mode was toggled successfully
     */
    public boolean toggleUiMode() {
        BrowserConfig config = BrowserConfig.getInstance();
        boolean newMode = config.toggleUiMode();
        
        MinecraftClient client = MinecraftClient.getInstance();
        if (client != null && client.currentScreen != null) {
            Screen currentScreen = client.currentScreen;
            
            // Try to reopen the current screen in the new mode
            if (newMode && currentScreen instanceof Screen) {
                // Switching to HTML mode
                Screen htmlScreen = openHtmlScreen(currentScreen.getClass(), null);
                if (htmlScreen != null) {
                    client.setScreen(htmlScreen);
                    Pokecobbleclaim.LOGGER.info("Switched current screen to HTML mode");
                }
            } else if (!newMode && currentScreen instanceof HtmlScreenBase) {
                // Switching to Minecraft GUI mode
                HtmlScreenBase htmlScreen = (HtmlScreenBase) currentScreen;
                String screenId = htmlScreen.getScreenId();
                
                Function<Screen, Screen> factory = minecraftScreenFactories.get(screenId);
                if (factory != null) {
                    Screen minecraftScreen = factory.apply(null);
                    if (minecraftScreen != null) {
                        client.setScreen(minecraftScreen);
                        Pokecobbleclaim.LOGGER.info("Switched current screen to Minecraft GUI mode");
                    }
                }
            }
        }
        
        return newMode;
    }
    
    /**
     * Checks if a screen has an HTML equivalent.
     * @param screenClass The screen class to check
     * @return true if HTML equivalent exists
     */
    public boolean hasHtmlEquivalent(Class<? extends Screen> screenClass) {
        return htmlScreenFactories.containsKey(screenClass);
    }
    
    /**
     * Checks if an HTML screen has a Minecraft equivalent.
     * @param screenId The HTML screen ID
     * @return true if Minecraft equivalent exists
     */
    public boolean hasMinecraftEquivalent(String screenId) {
        return minecraftScreenFactories.containsKey(screenId);
    }
    
    /**
     * Registers a new HTML screen factory.
     * @param screenClass The Minecraft screen class
     * @param factory The factory function to create the HTML screen
     */
    public void registerHtmlScreen(Class<? extends Screen> screenClass, Function<Screen, HtmlScreenBase> factory) {
        htmlScreenFactories.put(screenClass, factory);
        Pokecobbleclaim.LOGGER.debug("Registered HTML screen factory for: " + screenClass.getSimpleName());
    }
    
    /**
     * Registers a new Minecraft screen factory.
     * @param screenId The HTML screen ID
     * @param factory The factory function to create the Minecraft screen
     */
    public void registerMinecraftScreen(String screenId, Function<Screen, Screen> factory) {
        minecraftScreenFactories.put(screenId, factory);
        Pokecobbleclaim.LOGGER.debug("Registered Minecraft screen factory for: " + screenId);
    }
    
    /**
     * Gets the current UI mode.
     * @return true if HTML UI is enabled, false for Minecraft GUI
     */
    public boolean isHtmlUiEnabled() {
        return BrowserConfig.getInstance().isHtmlUiEnabled();
    }
    
    /**
     * Forces a specific UI mode.
     * @param htmlMode true for HTML UI, false for Minecraft GUI
     */
    public void setUiMode(boolean htmlMode) {
        BrowserConfig config = BrowserConfig.getInstance();
        if (config.isHtmlUiEnabled() != htmlMode) {
            config.setHtmlUiEnabled(htmlMode);
            config.save();
            Pokecobbleclaim.LOGGER.info("UI mode set to: " + (htmlMode ? "HTML" : "Minecraft GUI"));
        }
    }
    
    /**
     * Gets statistics about registered screens.
     */
    public String getStats() {
        return String.format(
            "Screen Toggle Manager: %d HTML screens, %d Minecraft screens, Mode: %s",
            htmlScreenFactories.size(),
            minecraftScreenFactories.size(),
            isHtmlUiEnabled() ? "HTML" : "Minecraft GUI"
        );
    }
}
