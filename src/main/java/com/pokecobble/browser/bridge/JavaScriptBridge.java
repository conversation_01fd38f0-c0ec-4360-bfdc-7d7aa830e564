package com.pokecobble.browser.bridge;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.browser.core.BrowserManager;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 * JavaScript bridge for communication between the browser and Minecraft.
 * Framework implementation - handles bidirectional data exchange and function calls.
 */
public class JavaScriptBridge {
    private static JavaScriptBridge instance;
    private final Gson gson = new Gson();
    private final Map<String, Function<JsonObject, Object>> handlers = new ConcurrentHashMap<>();
    
    private JavaScriptBridge() {
        setupDefaultHandlers();
    }
    
    /**
     * Gets the singleton instance of the JavaScript bridge.
     */
    public static synchronized JavaScriptBridge getInstance() {
        if (instance == null) {
            instance = new JavaScriptBridge();
        }
        return instance;
    }
    
    /**
     * Sets up default message handlers.
     */
    private void setupDefaultHandlers() {
        // System information handler
        registerHandler("getSystemInfo", (data) -> {
            JsonObject info = new JsonObject();
            info.addProperty("modVersion", "1.0.0");
            info.addProperty("minecraftVersion", "1.20.1");
            info.addProperty("playerName", getPlayerName());
            info.addProperty("timestamp", System.currentTimeMillis());
            return info;
        });
        
        // Configuration handler
        registerHandler("getConfig", (data) -> {
            return com.pokecobble.browser.core.BrowserConfig.getInstance();
        });
        
        // Log message handler
        registerHandler("log", (data) -> {
            String level = data.has("level") ? data.get("level").getAsString() : "info";
            String message = data.has("message") ? data.get("message").getAsString() : "";
            
            switch (level.toLowerCase()) {
                case "error":
                    Pokecobbleclaim.LOGGER.error("Browser: " + message);
                    break;
                case "warn":
                    Pokecobbleclaim.LOGGER.warn("Browser: " + message);
                    break;
                case "debug":
                    Pokecobbleclaim.LOGGER.debug("Browser: " + message);
                    break;
                default:
                    Pokecobbleclaim.LOGGER.info("Browser: " + message);
                    break;
            }
            
            return "logged";
        });
        
        // Screen navigation handler
        registerHandler("navigateToScreen", (data) -> {
            String screenId = data.has("screenId") ? data.get("screenId").getAsString() : "";
            return handleScreenNavigation(screenId);
        });
        
        // Close screen handler
        registerHandler("closeScreen", (data) -> {
            String screenId = data.has("screenId") ? data.get("screenId").getAsString() : "";
            return handleScreenClose(screenId);
        });
    }
    
    /**
     * Handles JavaScript queries from the browser.
     * Framework implementation - will be extended with actual browser integration.
     */
    public Object handleJavaScriptQuery(String request) {
        try {
            Pokecobbleclaim.LOGGER.debug("Received JavaScript query: " + request);

            // Parse the request JSON
            JsonObject requestObj = JsonParser.parseString(request).getAsJsonObject();
            String action = requestObj.has("action") ? requestObj.get("action").getAsString() : "";
            JsonObject data = requestObj.has("data") ? requestObj.getAsJsonObject("data") : new JsonObject();

            // Find and execute handler
            Function<JsonObject, Object> handler = handlers.get(action);
            if (handler != null) {
                Object result = handler.apply(data);
                return createResponse(true, result, null);
            } else {
                String error = "Unknown action: " + action;
                Pokecobbleclaim.LOGGER.warn(error);
                return createResponse(false, null, error);
            }

        } catch (Exception e) {
            String error = "Error handling JavaScript query: " + e.getMessage();
            Pokecobbleclaim.LOGGER.error(error, e);
            return createResponse(false, null, error);
        }
    }
    
    /**
     * Creates a standardized response object.
     */
    private JsonObject createResponse(boolean success, Object data, String error) {
        JsonObject response = new JsonObject();
        response.addProperty("success", success);
        response.addProperty("timestamp", System.currentTimeMillis());
        
        if (success && data != null) {
            response.add("data", gson.toJsonTree(data));
        }
        
        if (!success && error != null) {
            response.addProperty("error", error);
        }
        
        return response;
    }
    
    /**
     * Registers a new message handler.
     * @param action The action name to handle
     * @param handler The handler function
     */
    public void registerHandler(String action, Function<JsonObject, Object> handler) {
        handlers.put(action, handler);
        Pokecobbleclaim.LOGGER.debug("Registered JavaScript bridge handler: " + action);
    }
    
    /**
     * Sends a message to the browser JavaScript.
     * @param screenId The screen identifier
     * @param action The action to perform
     * @param data The data to send
     */
    public void sendToBrowser(String screenId, String action, Object data) {
        try {
            JsonObject message = new JsonObject();
            message.addProperty("action", action);
            message.addProperty("timestamp", System.currentTimeMillis());
            
            if (data != null) {
                message.add("data", gson.toJsonTree(data));
            }
            
            String javascript = String.format(
                "if (window.MinecraftBridge && window.MinecraftBridge.handleMessage) { " +
                "window.MinecraftBridge.handleMessage(%s); }",
                gson.toJson(message)
            );
            
            BrowserManager.getInstance().executeJavaScript(screenId, javascript);
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending message to browser", e);
        }
    }
    
    /**
     * Gets the message router handler for browser integration.
     * Framework implementation - returns null for now.
     */
    public Object getMessageHandler() {
        return null; // Will be implemented with actual browser engines
    }
    
    /**
     * Handles screen navigation requests from JavaScript.
     */
    private String handleScreenNavigation(String screenId) {
        try {
            // This will be implemented to work with the screen manager
            Pokecobbleclaim.LOGGER.info("Navigation request to screen: " + screenId);
            
            // For now, just return success
            return "Navigation initiated to: " + screenId;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling screen navigation", e);
            return "Navigation failed: " + e.getMessage();
        }
    }
    
    /**
     * Handles screen close requests from JavaScript.
     */
    private String handleScreenClose(String screenId) {
        try {
            Pokecobbleclaim.LOGGER.info("Close request for screen: " + screenId);
            
            // This will be implemented to work with the screen manager
            return "Screen close initiated: " + screenId;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling screen close", e);
            return "Screen close failed: " + e.getMessage();
        }
    }
    
    /**
     * Gets the current player name.
     */
    private String getPlayerName() {
        try {
            net.minecraft.client.MinecraftClient client = net.minecraft.client.MinecraftClient.getInstance();
            if (client != null && client.player != null) {
                return client.player.getName().getString();
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.debug("Could not get player name", e);
        }
        return "Unknown";
    }
    
    /**
     * Notifies all browsers of a data update.
     * @param dataType The type of data that was updated
     * @param data The updated data
     */
    public void notifyDataUpdate(String dataType, Object data) {
        // This will send updates to all active browsers
        // Implementation will be added when we have screen management
        Pokecobbleclaim.LOGGER.debug("Data update notification: " + dataType);
    }
    
    /**
     * Gets bridge statistics for debugging.
     */
    public String getBridgeStats() {
        return String.format(
            "JavaScript Bridge: %d handlers registered",
            handlers.size()
        );
    }
}
