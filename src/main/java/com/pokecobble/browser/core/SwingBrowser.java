package com.pokecobble.browser.core;

import com.pokecobble.Pokecobbleclaim;

import javax.swing.*;
import javax.swing.text.html.HTMLDocument;
import javax.swing.text.html.HTMLEditorKit;
import java.awt.*;
import java.awt.event.MouseEvent;
import java.awt.image.BufferedImage;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URL;

/**
 * Swing-based HTML browser component that can render real HTML content.
 * Uses JEditorPane with HTMLEditorKit for actual HTML rendering.
 */
public class SwingBrowser {
    private JEditorPane editorPane;
    private JScrollPane scrollPane;
    private JPanel browserPanel;
    private String currentUrl;
    private BufferedImage renderedImage;
    private boolean contentLoaded = false;
    
    public SwingBrowser(int width, int height) {
        initializeBrowser(width, height);
    }
    
    /**
     * Initializes the Swing browser components.
     */
    private void initializeBrowser(int width, int height) {
        try {
            SwingUtilities.invokeAndWait(() -> {
                // Create editor pane with HTML support
                editorPane = new JEditorPane();
                editorPane.setContentType("text/html");
                editorPane.setEditorKit(new HTMLEditorKit());
                editorPane.setEditable(false);
                editorPane.setBackground(Color.WHITE);
                editorPane.setOpaque(true);

                // Set size for the editor pane
                editorPane.setSize(width, height);
                editorPane.setPreferredSize(new Dimension(width, height));

                // Create scroll pane
                scrollPane = new JScrollPane(editorPane);
                scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
                scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_AS_NEEDED);
                scrollPane.setOpaque(true);
                scrollPane.setBackground(Color.WHITE);

                // Create main panel
                browserPanel = new JPanel(new BorderLayout());
                browserPanel.add(scrollPane, BorderLayout.CENTER);
                browserPanel.setSize(width, height);
                browserPanel.setPreferredSize(new Dimension(width, height));
                browserPanel.setOpaque(true);
                browserPanel.setBackground(Color.WHITE);

                // Force layout
                browserPanel.doLayout();
                browserPanel.validate();

                Pokecobbleclaim.LOGGER.debug("Swing browser components initialized with size: " + width + "x" + height);
            });
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to initialize Swing browser", e);
        }
    }
    
    /**
     * Loads HTML content from a resource file.
     */
    public boolean loadHtmlFromResource(String resourcePath) {
        try {
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream("assets/pokecobbleclaim/html/" + resourcePath);
            if (inputStream == null) {
                Pokecobbleclaim.LOGGER.error("Could not find HTML resource: " + resourcePath);
                return false;
            }
            
            StringBuilder content = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    content.append(line).append("\n");
                }
            }
            
            return loadHtmlContent(content.toString());
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error loading HTML resource: " + resourcePath, e);
            return false;
        }
    }
    
    /**
     * Loads HTML content directly.
     */
    public boolean loadHtmlContent(String htmlContent) {
        try {
            SwingUtilities.invokeAndWait(() -> {
                try {
                    // Set the HTML content
                    editorPane.setText(htmlContent);

                    // Set base URL for relative resources
                    HTMLDocument doc = (HTMLDocument) editorPane.getDocument();
                    URL baseUrl = getClass().getClassLoader().getResource("assets/pokecobbleclaim/html/");
                    if (baseUrl != null) {
                        doc.setBase(baseUrl);
                    }

                    // Force layout and repaint
                    editorPane.revalidate();
                    editorPane.repaint();
                    browserPanel.revalidate();
                    browserPanel.repaint();

                    // Debug: Log the actual text content that was set
                    String actualText = editorPane.getText();
                    Pokecobbleclaim.LOGGER.debug("EditorPane text set to: " + actualText.substring(0, Math.min(200, actualText.length())));

                    // Debug: Check if the document has any content
                    javax.swing.text.Document document = editorPane.getDocument();
                    Pokecobbleclaim.LOGGER.debug("Document length: " + document.getLength());

                    contentLoaded = true;
                    Pokecobbleclaim.LOGGER.info("HTML content loaded successfully: " + htmlContent.length() + " characters");

                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.error("Error setting HTML content", e);
                    contentLoaded = false;
                }
            });

            return contentLoaded;

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error loading HTML content", e);
            return false;
        }
    }
    
    /**
     * Renders the browser content to a BufferedImage.
     */
    public BufferedImage renderToImage() {
        if (!contentLoaded || browserPanel == null || editorPane == null) {
            Pokecobbleclaim.LOGGER.debug("Cannot render: contentLoaded=" + contentLoaded + ", browserPanel=" + (browserPanel != null) + ", editorPane=" + (editorPane != null));
            return null;
        }

        try {
            final BufferedImage[] imageHolder = new BufferedImage[1];

            SwingUtilities.invokeAndWait(() -> {
                try {
                    // Get dimensions
                    int width = browserPanel.getWidth();
                    int height = browserPanel.getHeight();

                    if (width <= 0 || height <= 0) {
                        width = 800;
                        height = 600;
                        browserPanel.setSize(width, height);
                        browserPanel.setPreferredSize(new Dimension(width, height));
                        editorPane.setSize(width, height);
                        editorPane.setPreferredSize(new Dimension(width, height));
                    }

                    // Force layout and validation
                    browserPanel.doLayout();
                    browserPanel.validate();
                    browserPanel.repaint();

                    // Create image with white background
                    BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
                    Graphics2D g2d = image.createGraphics();

                    // Fill with white background first
                    g2d.setColor(Color.WHITE);
                    g2d.fillRect(0, 0, width, height);

                    // Enable anti-aliasing for better text rendering
                    g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                    g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
                    g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);

                    // Try painting the editorPane directly instead of the panel
                    if (editorPane.isDisplayable()) {
                        editorPane.paint(g2d);
                        Pokecobbleclaim.LOGGER.debug("Painted editorPane directly");
                    } else {
                        // Paint the panel
                        browserPanel.paint(g2d);
                        Pokecobbleclaim.LOGGER.debug("Painted browserPanel");
                    }

                    // Check if anything was actually painted
                    boolean hasContent = false;
                    for (int checkY = 0; checkY < height && !hasContent; checkY += 10) {
                        for (int checkX = 0; checkX < width && !hasContent; checkX += 10) {
                            int pixel = image.getRGB(checkX, checkY);
                            if (pixel != 0xFFFFFFFF) { // Not white
                                hasContent = true;
                            }
                        }
                    }

                    g2d.dispose();

                    imageHolder[0] = image;
                    Pokecobbleclaim.LOGGER.debug("Successfully rendered browser to image: " + width + "x" + height + ", hasContent: " + hasContent);

                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.error("Error rendering browser to image", e);
                    imageHolder[0] = null;
                }
            });

            renderedImage = imageHolder[0];
            return renderedImage;

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error in renderToImage", e);
            return null;
        }
    }
    
    /**
     * Gets the last rendered image.
     */
    public BufferedImage getRenderedImage() {
        return renderedImage;
    }
    
    /**
     * Handles mouse clicks on the browser.
     */
    public void handleMouseClick(int x, int y, int button) {
        if (!contentLoaded || editorPane == null) {
            return;
        }
        
        try {
            SwingUtilities.invokeLater(() -> {
                try {
                    // Convert coordinates and simulate click
                    Point point = new Point(x, y);
                    
                    // Find the component at the click position
                    Component component = editorPane.getComponentAt(point);
                    if (component != null) {
                        // Simulate mouse event
                        MouseEvent mouseEvent = new MouseEvent(
                            component,
                            MouseEvent.MOUSE_CLICKED,
                            System.currentTimeMillis(),
                            0,
                            x, y,
                            1,
                            false
                        );
                        
                        component.dispatchEvent(mouseEvent);
                        Pokecobbleclaim.LOGGER.debug("Mouse click dispatched to browser at: " + x + ", " + y);
                    }
                    
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.error("Error handling mouse click", e);
                }
            });
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error in handleMouseClick", e);
        }
    }
    
    /**
     * Executes JavaScript in the browser.
     * Note: Swing's HTMLEditorKit has limited JavaScript support.
     */
    public void executeJavaScript(String script) {
        Pokecobbleclaim.LOGGER.warn("JavaScript execution not fully supported in Swing HTMLEditorKit");
        // Swing's HTMLEditorKit has very limited JavaScript support
        // For full JavaScript support, we would need JavaFX WebView or CEF
    }
    
    /**
     * Reloads the current content.
     */
    public void reload() {
        if (currentUrl != null) {
            loadHtmlFromResource(currentUrl);
        }
    }
    
    /**
     * Checks if content is loaded.
     */
    public boolean isContentLoaded() {
        return contentLoaded;
    }
    
    /**
     * Gets the browser panel for embedding.
     */
    public JPanel getBrowserPanel() {
        return browserPanel;
    }
    
    /**
     * Sets the size of the browser.
     */
    public void setSize(int width, int height) {
        if (browserPanel != null) {
            SwingUtilities.invokeLater(() -> {
                browserPanel.setSize(width, height);
                browserPanel.setPreferredSize(new Dimension(width, height));
                browserPanel.revalidate();
            });
        }
    }
    
    /**
     * Disposes of the browser resources.
     */
    public void dispose() {
        try {
            SwingUtilities.invokeAndWait(() -> {
                if (browserPanel != null) {
                    browserPanel.removeAll();
                }
                editorPane = null;
                scrollPane = null;
                browserPanel = null;
                renderedImage = null;
                contentLoaded = false;
            });
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error disposing browser", e);
        }
    }
}
