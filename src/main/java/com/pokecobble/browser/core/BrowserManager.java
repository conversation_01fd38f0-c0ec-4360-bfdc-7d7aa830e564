package com.pokecobble.browser.core;

import com.pokecobble.Pokecobbleclaim;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Manages browser instances and their lifecycle.
 * Uses SwingBrowser for HTML rendering with future MCEF support planned.
 */
public class BrowserManager {
    private static BrowserManager instance;
    private final Map<String, SwingBrowser> activeBrowsers = new ConcurrentHashMap<>();
    private final AtomicInteger browserIdCounter = new AtomicInteger(0);
    
    // Configuration
    private static final int MAX_BROWSERS = 5; // Limit concurrent browsers for performance
    private static final String RESOURCE_BASE_URL = "file:///"; // Will be updated to point to mod resources
    
    private BrowserManager() {
        // Private constructor for singleton
    }
    
    /**
     * Gets the singleton instance of the browser manager.
     */
    public static synchronized BrowserManager getInstance() {
        if (instance == null) {
            instance = new BrowserManager();
        }
        return instance;
    }
    
    /**
     * Creates or retrieves a browser instance for a specific screen.
     * @param screenId Unique identifier for the screen
     * @param htmlFile The HTML file to load (relative to resources)
     * @return SwingBrowser instance or null if creation failed
     */
    public SwingBrowser getBrowserForScreen(String screenId, String htmlFile) {
        // Check if we already have a browser for this screen
        SwingBrowser existingBrowser = activeBrowsers.get(screenId);
        if (existingBrowser != null) {
            Pokecobbleclaim.LOGGER.debug("Reusing existing browser for screen: " + screenId);
            return existingBrowser;
        }

        // Check browser limit
        if (activeBrowsers.size() >= MAX_BROWSERS) {
            Pokecobbleclaim.LOGGER.warn("Maximum browser limit reached (" + MAX_BROWSERS + "), cannot create new browser");
            return null;
        }

        // Create new browser
        return createNewBrowser(screenId, htmlFile);
    }
    
    /**
     * Creates a new browser instance.
     */
    private SwingBrowser createNewBrowser(String screenId, String htmlFile) {
        BrowserConfig config = BrowserConfig.getInstance();

        try {
            // Create SwingBrowser instance
            SwingBrowser browser = new SwingBrowser(config.getBrowserWidth(), config.getBrowserHeight());

            // Load HTML content
            if (browser.loadHtmlFromResource(htmlFile)) {
                activeBrowsers.put(screenId, browser);
                Pokecobbleclaim.LOGGER.info("Created new SwingBrowser for screen: " + screenId + " with file: " + htmlFile);
                return browser;
            } else {
                Pokecobbleclaim.LOGGER.error("Failed to load HTML content for screen: " + screenId);
                browser.dispose();
                return null;
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Exception while creating browser for screen: " + screenId, e);
            return null;
        }
    }
    
    /**
     * Builds the full URL for a resource file.
     * @param htmlFile The HTML file path relative to resources
     * @return Full URL to the resource
     */
    private String buildResourceUrl(String htmlFile) {
        // For now, we'll use a simple file URL
        // Later this will be updated to properly load from mod resources
        String resourcePath = System.getProperty("user.dir") + "/src/main/resources/assets/pokecobbleclaim/html/" + htmlFile;
        return "file:///" + resourcePath.replace("\\", "/");
    }
    
    /**
     * Releases a browser instance for a specific screen.
     * @param screenId The screen identifier
     */
    public void releaseBrowser(String screenId) {
        SwingBrowser browser = activeBrowsers.remove(screenId);
        if (browser != null) {
            try {
                browser.dispose();
                Pokecobbleclaim.LOGGER.debug("Released SwingBrowser for screen: " + screenId);
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error releasing browser for screen: " + screenId, e);
            }
        }
    }

    /**
     * Reloads the browser for a specific screen.
     * @param screenId The screen identifier
     */
    public void reloadBrowser(String screenId) {
        SwingBrowser browser = activeBrowsers.get(screenId);
        if (browser != null) {
            try {
                browser.reload();
                Pokecobbleclaim.LOGGER.debug("Reloaded SwingBrowser for screen: " + screenId);
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error reloading browser for screen: " + screenId, e);
            }
        }
    }

    /**
     * Executes JavaScript in a specific browser.
     * @param screenId The screen identifier
     * @param javascript The JavaScript code to execute
     */
    public void executeJavaScript(String screenId, String javascript) {
        SwingBrowser browser = activeBrowsers.get(screenId);
        if (browser != null) {
            try {
                browser.executeJavaScript(javascript);
                Pokecobbleclaim.LOGGER.debug("Executed JavaScript in SwingBrowser for screen: " + screenId);
            } catch (Exception e) {
                Pokecobbleclaim.LOGGER.error("Error executing JavaScript in browser for screen: " + screenId, e);
            }
        } else {
            Pokecobbleclaim.LOGGER.warn("Cannot execute JavaScript: no active browser for screen: " + screenId);
        }
    }
    
    /**
     * Gets the number of active browsers.
     * Framework implementation.
     */
    public int getActiveBrowserCount() {
        return activeBrowsers.size();
    }

    /**
     * Checks if a browser exists for a specific screen.
     * @param screenId The screen identifier
     * @return true if browser exists
     */
    public boolean hasBrowserForScreen(String screenId) {
        SwingBrowser browser = activeBrowsers.get(screenId);
        return browser != null && browser.isContentLoaded();
    }

    /**
     * Gets browser information for debugging.
     */
    public String getBrowserInfo() {
        StringBuilder info = new StringBuilder();
        info.append("Active browsers: ").append(getActiveBrowserCount()).append("/").append(MAX_BROWSERS).append("\n");

        for (Map.Entry<String, SwingBrowser> entry : activeBrowsers.entrySet()) {
            SwingBrowser browser = entry.getValue();
            info.append("- ").append(entry.getKey()).append(": ");
            if (browser != null && browser.isContentLoaded()) {
                info.append("ACTIVE (SwingBrowser)");
            } else {
                info.append("INACTIVE");
            }
            info.append("\n");
        }

        return info.toString();
    }

    /**
     * Cleans up all browsers and shuts down the manager.
     */
    public void shutdown() {
        Pokecobbleclaim.LOGGER.info("Shutting down browser manager...");

        // Close all active browsers
        for (Map.Entry<String, SwingBrowser> entry : activeBrowsers.entrySet()) {
            SwingBrowser browser = entry.getValue();
            if (browser != null) {
                try {
                    browser.dispose();
                    Pokecobbleclaim.LOGGER.debug("Closed SwingBrowser for screen: " + entry.getKey());
                } catch (Exception e) {
                    Pokecobbleclaim.LOGGER.error("Error closing browser for screen: " + entry.getKey(), e);
                }
            }
        }

        activeBrowsers.clear();
        Pokecobbleclaim.LOGGER.info("Browser manager shut down successfully");
    }
}
