package com.pokecobble.browser.core;

import com.pokecobble.Pokecobbleclaim;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;

/**
 * Configuration management for the browser integration system.
 * Handles loading, saving, and managing browser-related settings.
 */
public class BrowserConfig {
    private static BrowserConfig instance;
    private static final String CONFIG_FILE = "run/pokecobbleclaim/browser_config.json";
    private static final Gson gson = new GsonBuilder().setPrettyPrinting().create();
    
    // Configuration fields with defaults
    private boolean htmlUiEnabled = false; // Default to Minecraft GUI
    private boolean enableDevTools = true; // Enable for development
    private boolean enableConsoleLogging = true;
    private boolean enableTransparency = true;
    private int maxBrowserInstances = 5;
    private int browserWidth = 1024;
    private int browserHeight = 768;
    private boolean mobileResponsive = true;
    private String theme = "dark"; // "dark" or "light"
    private boolean enableAnimations = true;
    private boolean enableFallback = true; // Fallback to MC GUI if browser fails
    private int cacheMaxSize = 100; // MB
    private boolean enableJavaScriptBridge = true;
    
    // Performance settings
    private boolean enableGpuAcceleration = true;
    private int maxFrameRate = 60;
    private boolean enableVSync = true;
    
    private BrowserConfig() {
        // Private constructor for singleton
    }
    
    /**
     * Gets the singleton instance of the browser configuration.
     */
    public static synchronized BrowserConfig getInstance() {
        if (instance == null) {
            instance = new BrowserConfig();
            instance.load();
        }
        return instance;
    }
    
    /**
     * Loads configuration from file.
     */
    public void load() {
        File configFile = new File(CONFIG_FILE);
        
        if (!configFile.exists()) {
            Pokecobbleclaim.LOGGER.info("Browser config file not found, creating with defaults");
            save(); // Create default config file
            return;
        }
        
        try (FileReader reader = new FileReader(configFile)) {
            BrowserConfig loaded = gson.fromJson(reader, BrowserConfig.class);
            if (loaded != null) {
                copyFrom(loaded);
                Pokecobbleclaim.LOGGER.info("Loaded browser configuration from: " + CONFIG_FILE);
            }
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Failed to load browser configuration", e);
        }
    }
    
    /**
     * Saves configuration to file.
     */
    public void save() {
        File configFile = new File(CONFIG_FILE);
        
        // Ensure directory exists
        File parentDir = configFile.getParentFile();
        if (!parentDir.exists()) {
            parentDir.mkdirs();
        }
        
        try (FileWriter writer = new FileWriter(configFile)) {
            gson.toJson(this, writer);
            Pokecobbleclaim.LOGGER.info("Saved browser configuration to: " + CONFIG_FILE);
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Failed to save browser configuration", e);
        }
    }
    
    /**
     * Copies configuration from another instance.
     */
    private void copyFrom(BrowserConfig other) {
        this.htmlUiEnabled = other.htmlUiEnabled;
        this.enableDevTools = other.enableDevTools;
        this.enableConsoleLogging = other.enableConsoleLogging;
        this.enableTransparency = other.enableTransparency;
        this.maxBrowserInstances = other.maxBrowserInstances;
        this.browserWidth = other.browserWidth;
        this.browserHeight = other.browserHeight;
        this.mobileResponsive = other.mobileResponsive;
        this.theme = other.theme;
        this.enableAnimations = other.enableAnimations;
        this.enableFallback = other.enableFallback;
        this.cacheMaxSize = other.cacheMaxSize;
        this.enableJavaScriptBridge = other.enableJavaScriptBridge;
        this.enableGpuAcceleration = other.enableGpuAcceleration;
        this.maxFrameRate = other.maxFrameRate;
        this.enableVSync = other.enableVSync;
    }
    
    // Getters and setters
    
    public boolean isHtmlUiEnabled() {
        return htmlUiEnabled;
    }
    
    public void setHtmlUiEnabled(boolean htmlUiEnabled) {
        this.htmlUiEnabled = htmlUiEnabled;
    }
    
    public boolean isEnableDevTools() {
        return enableDevTools;
    }
    
    public void setEnableDevTools(boolean enableDevTools) {
        this.enableDevTools = enableDevTools;
    }
    
    public boolean isEnableConsoleLogging() {
        return enableConsoleLogging;
    }
    
    public void setEnableConsoleLogging(boolean enableConsoleLogging) {
        this.enableConsoleLogging = enableConsoleLogging;
    }
    
    public boolean isEnableTransparency() {
        return enableTransparency;
    }
    
    public void setEnableTransparency(boolean enableTransparency) {
        this.enableTransparency = enableTransparency;
    }
    
    public int getMaxBrowserInstances() {
        return maxBrowserInstances;
    }
    
    public void setMaxBrowserInstances(int maxBrowserInstances) {
        this.maxBrowserInstances = Math.max(1, Math.min(10, maxBrowserInstances)); // Clamp between 1-10
    }
    
    public int getBrowserWidth() {
        return browserWidth;
    }
    
    public void setBrowserWidth(int browserWidth) {
        this.browserWidth = Math.max(800, browserWidth); // Minimum 800px width
    }
    
    public int getBrowserHeight() {
        return browserHeight;
    }
    
    public void setBrowserHeight(int browserHeight) {
        this.browserHeight = Math.max(600, browserHeight); // Minimum 600px height
    }
    
    public boolean isMobileResponsive() {
        return mobileResponsive;
    }
    
    public void setMobileResponsive(boolean mobileResponsive) {
        this.mobileResponsive = mobileResponsive;
    }
    
    public String getTheme() {
        return theme;
    }
    
    public void setTheme(String theme) {
        if ("dark".equals(theme) || "light".equals(theme)) {
            this.theme = theme;
        }
    }
    
    public boolean isEnableAnimations() {
        return enableAnimations;
    }
    
    public void setEnableAnimations(boolean enableAnimations) {
        this.enableAnimations = enableAnimations;
    }
    
    public boolean isEnableFallback() {
        return enableFallback;
    }
    
    public void setEnableFallback(boolean enableFallback) {
        this.enableFallback = enableFallback;
    }
    
    public int getCacheMaxSize() {
        return cacheMaxSize;
    }
    
    public void setCacheMaxSize(int cacheMaxSize) {
        this.cacheMaxSize = Math.max(10, Math.min(500, cacheMaxSize)); // Clamp between 10-500 MB
    }
    
    public boolean isEnableJavaScriptBridge() {
        return enableJavaScriptBridge;
    }
    
    public void setEnableJavaScriptBridge(boolean enableJavaScriptBridge) {
        this.enableJavaScriptBridge = enableJavaScriptBridge;
    }
    
    public boolean isEnableGpuAcceleration() {
        return enableGpuAcceleration;
    }
    
    public void setEnableGpuAcceleration(boolean enableGpuAcceleration) {
        this.enableGpuAcceleration = enableGpuAcceleration;
    }
    
    public int getMaxFrameRate() {
        return maxFrameRate;
    }
    
    public void setMaxFrameRate(int maxFrameRate) {
        this.maxFrameRate = Math.max(30, Math.min(120, maxFrameRate)); // Clamp between 30-120 FPS
    }
    
    public boolean isEnableVSync() {
        return enableVSync;
    }
    
    public void setEnableVSync(boolean enableVSync) {
        this.enableVSync = enableVSync;
    }
    
    /**
     * Toggles between HTML UI and Minecraft GUI.
     * @return true if now using HTML UI, false if using Minecraft GUI
     */
    public boolean toggleUiMode() {
        htmlUiEnabled = !htmlUiEnabled;
        save(); // Auto-save when toggling
        Pokecobbleclaim.LOGGER.info("UI mode toggled to: " + (htmlUiEnabled ? "HTML" : "Minecraft GUI"));
        return htmlUiEnabled;
    }
    
    /**
     * Gets a summary of current configuration for debugging.
     */
    public String getConfigSummary() {
        return String.format(
            "Browser Config: UI=%s, DevTools=%s, Theme=%s, Size=%dx%d, MaxBrowsers=%d",
            htmlUiEnabled ? "HTML" : "MC GUI",
            enableDevTools,
            theme,
            browserWidth,
            browserHeight,
            maxBrowserInstances
        );
    }
}
