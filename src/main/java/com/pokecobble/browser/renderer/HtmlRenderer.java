package com.pokecobble.browser.renderer;

import com.pokecobble.Pokecobbleclaim;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.font.TextRenderer;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Simple HTML renderer for Minecraft GUI.
 * Parses and renders basic HTML content within Minecraft's rendering system.
 */
public class HtmlRenderer {
    private final TextRenderer textRenderer;
    private String htmlContent;
    private final Map<String, String> cssStyles = new HashMap<>();
    private final List<HtmlElement> elements = new ArrayList<>();
    
    // Color mappings
    private static final Map<String, Integer> COLOR_MAP = new HashMap<>();
    static {
        COLOR_MAP.put("white", 0xFFFFFFFF);
        COLOR_MAP.put("black", 0xFF000000);
        COLOR_MAP.put("red", 0xFFFF0000);
        COLOR_MAP.put("green", 0xFF00FF00);
        COLOR_MAP.put("blue", 0xFF0000FF);
        COLOR_MAP.put("yellow", 0xFFFFFF00);
        COLOR_MAP.put("cyan", 0xFF00FFFF);
        COLOR_MAP.put("magenta", 0xFFFF00FF);
        COLOR_MAP.put("gray", 0xFF808080);
        COLOR_MAP.put("darkgray", 0xFF404040);
        COLOR_MAP.put("lightgray", 0xFFC0C0C0);
    }
    
    public HtmlRenderer(TextRenderer textRenderer) {
        this.textRenderer = textRenderer;
    }
    
    /**
     * Loads HTML content from a resource file.
     */
    public boolean loadHtmlFromResource(String resourcePath) {
        try {
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream("assets/pokecobbleclaim/html/" + resourcePath);
            if (inputStream == null) {
                Pokecobbleclaim.LOGGER.error("Could not find HTML resource: " + resourcePath);
                return false;
            }
            
            StringBuilder content = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    content.append(line).append("\n");
                }
            }
            
            this.htmlContent = content.toString();
            parseHtml();
            Pokecobbleclaim.LOGGER.info("Loaded HTML content from: " + resourcePath);
            return true;
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error loading HTML resource: " + resourcePath, e);
            return false;
        }
    }
    
    /**
     * Sets HTML content directly.
     */
    public void setHtmlContent(String htmlContent) {
        this.htmlContent = htmlContent;
        parseHtml();
    }
    
    /**
     * Parses the HTML content into renderable elements.
     */
    private void parseHtml() {
        elements.clear();
        
        if (htmlContent == null || htmlContent.isEmpty()) {
            return;
        }
        
        try {
            // Extract CSS styles
            extractCssStyles();
            
            // Parse body content
            String bodyContent = extractBodyContent();
            if (bodyContent != null) {
                parseElements(bodyContent, 0, 0);
            }
            
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error parsing HTML content", e);
        }
    }
    
    /**
     * Extracts CSS styles from the HTML.
     */
    private void extractCssStyles() {
        // Simple CSS extraction - look for <style> tags
        Pattern stylePattern = Pattern.compile("<style[^>]*>(.*?)</style>", Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
        Matcher matcher = stylePattern.matcher(htmlContent);
        
        while (matcher.find()) {
            String cssContent = matcher.group(1);
            parseCssContent(cssContent);
        }
    }
    
    /**
     * Parses CSS content into style rules.
     */
    private void parseCssContent(String cssContent) {
        // Very basic CSS parsing - just extract color and background-color properties
        Pattern rulePattern = Pattern.compile("([^{]+)\\{([^}]+)\\}", Pattern.DOTALL);
        Matcher matcher = rulePattern.matcher(cssContent);
        
        while (matcher.find()) {
            String selector = matcher.group(1).trim();
            String properties = matcher.group(2);
            
            // Parse properties
            String[] props = properties.split(";");
            for (String prop : props) {
                String[] parts = prop.split(":");
                if (parts.length == 2) {
                    String property = parts[0].trim();
                    String value = parts[1].trim();
                    cssStyles.put(selector + "." + property, value);
                }
            }
        }
    }
    
    /**
     * Extracts content from the body tag.
     */
    private String extractBodyContent() {
        Pattern bodyPattern = Pattern.compile("<body[^>]*>(.*?)</body>", Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
        Matcher matcher = bodyPattern.matcher(htmlContent);
        
        if (matcher.find()) {
            return matcher.group(1);
        }
        
        // If no body tag, return the whole content
        return htmlContent;
    }
    
    /**
     * Parses HTML elements recursively.
     */
    private void parseElements(String content, int x, int y) {
        // Simple element parsing - look for common tags
        parseHeaders(content, x, y);
        parseButtons(content, x, y);
        parseDivs(content, x, y);
        parseText(content, x, y);
    }
    
    /**
     * Parses header elements (h1, h2, h3).
     */
    private void parseHeaders(String content, int baseX, int baseY) {
        Pattern headerPattern = Pattern.compile("<h([1-6])[^>]*>(.*?)</h[1-6]>", Pattern.CASE_INSENSITIVE);
        Matcher matcher = headerPattern.matcher(content);
        
        int yOffset = 0;
        while (matcher.find()) {
            int level = Integer.parseInt(matcher.group(1));
            String text = matcher.group(2).replaceAll("<[^>]+>", ""); // Strip HTML tags
            
            HtmlElement element = new HtmlElement();
            element.type = "header";
            element.text = text;
            element.x = baseX + 20;
            element.y = baseY + 20 + yOffset;
            element.color = 0xFFFFFFFF;
            element.fontSize = Math.max(1, 4 - level); // Larger font for lower level headers
            
            elements.add(element);
            yOffset += 25 + (element.fontSize * 5);
        }
    }
    
    /**
     * Parses button elements.
     */
    private void parseButtons(String content, int baseX, int baseY) {
        Pattern buttonPattern = Pattern.compile("<button[^>]*class=\"([^\"]*)[^>]*>(.*?)</button>", Pattern.CASE_INSENSITIVE);
        Matcher matcher = buttonPattern.matcher(content);
        
        int yOffset = 100; // Start below headers
        int xOffset = 0;
        
        while (matcher.find()) {
            String className = matcher.group(1);
            String text = matcher.group(2).replaceAll("<[^>]+>", "");
            
            HtmlElement element = new HtmlElement();
            element.type = "button";
            element.text = text;
            element.x = baseX + 20 + xOffset;
            element.y = baseY + yOffset;
            element.width = Math.max(80, textRenderer.getWidth(text) + 20);
            element.height = 25;
            element.color = 0xFFFFFFFF;
            element.backgroundColor = getButtonColor(className);
            element.className = className;
            
            elements.add(element);
            
            xOffset += element.width + 10;
            if (xOffset > 400) { // Wrap to next line
                xOffset = 0;
                yOffset += 35;
            }
        }
    }
    
    /**
     * Parses div elements.
     */
    private void parseDivs(String content, int baseX, int baseY) {
        Pattern divPattern = Pattern.compile("<div[^>]*class=\"([^\"]*)[^>]*>(.*?)</div>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
        Matcher matcher = divPattern.matcher(content);
        
        int yOffset = 200; // Start below buttons
        
        while (matcher.find()) {
            String className = matcher.group(1);
            String innerContent = matcher.group(2);
            
            if (className.contains("town-card")) {
                parseTownCard(innerContent, baseX + 20, baseY + yOffset);
                yOffset += 80;
            }
        }
    }
    
    /**
     * Parses a town card div.
     */
    private void parseTownCard(String content, int x, int y) {
        HtmlElement card = new HtmlElement();
        card.type = "div";
        card.className = "town-card";
        card.x = x;
        card.y = y;
        card.width = 400;
        card.height = 70;
        card.backgroundColor = 0xFF3C3C3C;
        elements.add(card);
        
        // Extract town name
        Pattern namePattern = Pattern.compile("<h3[^>]*class=\"town-name[^>]*>(.*?)</h3>", Pattern.CASE_INSENSITIVE);
        Matcher nameMatcher = namePattern.matcher(content);
        if (nameMatcher.find()) {
            HtmlElement nameElement = new HtmlElement();
            nameElement.type = "text";
            nameElement.text = nameMatcher.group(1);
            nameElement.x = x + 10;
            nameElement.y = y + 10;
            nameElement.color = 0xFFFFFFFF;
            nameElement.fontSize = 2;
            elements.add(nameElement);
        }
        
        // Extract description
        Pattern descPattern = Pattern.compile("<div[^>]*class=\"town-description[^>]*>(.*?)</div>", Pattern.CASE_INSENSITIVE);
        Matcher descMatcher = descPattern.matcher(content);
        if (descMatcher.find()) {
            HtmlElement descElement = new HtmlElement();
            descElement.type = "text";
            descElement.text = descMatcher.group(1);
            descElement.x = x + 10;
            descElement.y = y + 30;
            descElement.color = 0xFFCCCCCC;
            descElement.fontSize = 1;
            elements.add(descElement);
        }
    }
    
    /**
     * Parses plain text content.
     */
    private void parseText(String content, int baseX, int baseY) {
        // Remove all HTML tags and render remaining text
        String plainText = content.replaceAll("<[^>]+>", "").trim();
        if (!plainText.isEmpty() && plainText.length() > 10) { // Only render substantial text
            HtmlElement element = new HtmlElement();
            element.type = "text";
            element.text = plainText.substring(0, Math.min(100, plainText.length())); // Limit length
            element.x = baseX + 20;
            element.y = baseY + 300;
            element.color = 0xFFCCCCCC;
            elements.add(element);
        }
    }
    
    /**
     * Gets button color based on CSS class.
     */
    private int getButtonColor(String className) {
        if (className.contains("btn-primary")) return 0xFF007ACC;
        if (className.contains("btn-secondary")) return 0xFF6C757D;
        if (className.contains("btn-success")) return 0xFF28A745;
        if (className.contains("btn-danger")) return 0xFFDC3545;
        if (className.contains("btn-warning")) return 0xFFFFC107;
        return 0xFF6C757D; // Default
    }
    
    /**
     * Renders the parsed HTML content.
     */
    public void render(DrawContext context, int offsetX, int offsetY, int maxWidth, int maxHeight) {
        for (HtmlElement element : elements) {
            // Check if element is within bounds
            if (element.x + offsetX > maxWidth || element.y + offsetY > maxHeight) {
                continue;
            }
            
            renderElement(context, element, offsetX, offsetY);
        }
    }
    
    /**
     * Renders a single HTML element.
     */
    private void renderElement(DrawContext context, HtmlElement element, int offsetX, int offsetY) {
        int x = element.x + offsetX;
        int y = element.y + offsetY;
        
        switch (element.type) {
            case "button":
                // Draw button background
                context.fill(x, y, x + element.width, y + element.height, element.backgroundColor);
                context.drawBorder(x, y, element.width, element.height, 0xFF555555);
                
                // Draw button text
                int textWidth = textRenderer.getWidth(element.text);
                int textX = x + (element.width - textWidth) / 2;
                int textY = y + (element.height - 8) / 2;
                context.drawText(textRenderer, element.text, textX, textY, element.color, false);
                break;
                
            case "div":
                // Draw div background
                if (element.backgroundColor != 0) {
                    context.fill(x, y, x + element.width, y + element.height, element.backgroundColor);
                    context.drawBorder(x, y, element.width, element.height, 0xFF555555);
                }
                break;
                
            case "text":
            case "header":
                // Draw text
                context.drawText(textRenderer, element.text, x, y, element.color, false);
                break;
        }
    }
    
    /**
     * Gets elements at a specific position for click handling.
     */
    public List<HtmlElement> getElementsAt(int x, int y, int offsetX, int offsetY) {
        List<HtmlElement> result = new ArrayList<>();
        
        for (HtmlElement element : elements) {
            int elemX = element.x + offsetX;
            int elemY = element.y + offsetY;
            
            if (x >= elemX && x <= elemX + element.width && 
                y >= elemY && y <= elemY + element.height) {
                result.add(element);
            }
        }
        
        return result;
    }
    
    /**
     * Represents a parsed HTML element.
     */
    public static class HtmlElement {
        public String type;
        public String text;
        public String className;
        public int x, y;
        public int width = 0, height = 0;
        public int color = 0xFFFFFFFF;
        public int backgroundColor = 0;
        public int fontSize = 1;
    }
}
