package com.pokecobble.util;

import net.minecraft.network.PacketByteBuf;

import java.util.Map;
import java.util.UUID;

/**
 * Utility methods for network operations.
 * This class provides common methods for reading and writing data to packet buffers.
 */
public class NetworkUtils {
    
    /**
     * Writes a map of string to boolean values to a packet buffer.
     * 
     * @param buf The packet buffer to write to
     * @param map The map to write
     */
    public static void writeStringBooleanMap(PacketByteBuf buf, Map<String, Boolean> map) {
        buf.writeInt(map.size());
        for (Map.Entry<String, Boolean> entry : map.entrySet()) {
            buf.writeString(entry.getKey());
            buf.writeBoolean(entry.getValue());
        }
    }
    
    /**
     * Reads a map of string to boolean values from a packet buffer.
     * 
     * @param buf The packet buffer to read from
     * @return The map that was read
     */
    public static Map<String, Boolean> readStringBooleanMap(PacketByteBuf buf) {
        int size = buf.readInt();
        Map<String, Boolean> map = new java.util.HashMap<>();
        for (int i = 0; i < size; i++) {
            String key = buf.readString();
            boolean value = buf.readBoolean();
            map.put(key, value);
        }
        return map;
    }
    
    /**
     * Checks if we're running on the client side.
     *
     * @return true if we're on the client side, false if we're on the server side
     */
    public static boolean isClient() {
        try {
            // This will throw a ClassNotFoundException on the server
            Class.forName("net.minecraft.client.MinecraftClient");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }
    
    /**
     * Generates a unique identifier for a player's data.
     * This can be used to track when data has changed.
     * 
     * @param playerId The player's UUID
     * @param dataType The type of data (e.g., "permissions", "rank")
     * @return A unique identifier for the player's data
     */
    public static String generateDataKey(UUID playerId, String dataType) {
        return playerId.toString() + "_" + dataType;
    }
}
