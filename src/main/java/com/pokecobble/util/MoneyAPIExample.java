package com.pokecobble.util;

import com.pokecobble.Pokecobbleclaim;
import net.minecraft.server.network.ServerPlayerEntity;

/**
 * Example class demonstrating how to use the MoneyAPI.
 * This class provides examples of common money operations.
 */
public class MoneyAPIExample {

    /**
     * Example of checking a player's balance.
     *
     * @param player The player to check
     * @return The player's balance
     */
    public static long checkPlayerBalance(ServerPlayerEntity player) {
        long balance = MoneyAPI.getBalance(player);
        Pokecobbleclaim.LOGGER.info("Player " + player.getName().getString() + " has " + balance + " money");
        return balance;
    }

    /**
     * Example of adding money to a player.
     *
     * @param player The player to add money to
     * @param amount The amount to add
     * @return true if successful, false otherwise
     */
    public static boolean givePlayerMoney(ServerPlayerEntity player, long amount) {
        Pokecobbleclaim.LOGGER.info("Attempting to give " + amount + " money to " + player.getName().getString());
        
        // Check balance before
        long balanceBefore = MoneyAPI.getBalance(player);
        Pokecobbleclaim.LOGGER.info("Balance before: " + balanceBefore);
        
        // Add money
        boolean success = MoneyAPI.addMoney(player, amount);
        
        // Check balance after
        if (success) {
            long balanceAfter = MoneyAPI.getBalance(player);
            Pokecobbleclaim.LOGGER.info("Successfully added money. New balance: " + balanceAfter);
        } else {
            Pokecobbleclaim.LOGGER.warn("Failed to add money to player");
        }
        
        return success;
    }

    /**
     * Example of removing money from a player.
     *
     * @param player The player to remove money from
     * @param amount The amount to remove
     * @return true if successful, false otherwise
     */
    public static boolean takePlayerMoney(ServerPlayerEntity player, long amount) {
        Pokecobbleclaim.LOGGER.info("Attempting to take " + amount + " money from " + player.getName().getString());
        
        // Check balance before
        long balanceBefore = MoneyAPI.getBalance(player);
        Pokecobbleclaim.LOGGER.info("Balance before: " + balanceBefore);
        
        // Check if player has enough money
        if (balanceBefore < amount) {
            Pokecobbleclaim.LOGGER.warn("Player doesn't have enough money. Has: " + balanceBefore + ", Needs: " + amount);
            return false;
        }
        
        // Remove money
        boolean success = MoneyAPI.removeMoney(player, amount);
        
        // Check balance after
        if (success) {
            long balanceAfter = MoneyAPI.getBalance(player);
            Pokecobbleclaim.LOGGER.info("Successfully removed money. New balance: " + balanceAfter);
        } else {
            Pokecobbleclaim.LOGGER.warn("Failed to remove money from player");
        }
        
        return success;
    }

    /**
     * Example of a transaction between two players.
     *
     * @param sender The player sending money
     * @param receiver The player receiving money
     * @param amount The amount to transfer
     * @return true if successful, false otherwise
     */
    public static boolean transferMoney(ServerPlayerEntity sender, ServerPlayerEntity receiver, long amount) {
        Pokecobbleclaim.LOGGER.info("Attempting to transfer " + amount + " money from " + 
                                   sender.getName().getString() + " to " + receiver.getName().getString());
        
        // Check sender's balance
        long senderBalance = MoneyAPI.getBalance(sender);
        if (senderBalance < amount) {
            Pokecobbleclaim.LOGGER.warn("Sender doesn't have enough money. Has: " + senderBalance + ", Needs: " + amount);
            return false;
        }
        
        // Remove money from sender
        boolean removedFromSender = MoneyAPI.removeMoney(sender, amount);
        if (!removedFromSender) {
            Pokecobbleclaim.LOGGER.error("Failed to remove money from sender");
            return false;
        }
        
        // Add money to receiver
        boolean addedToReceiver = MoneyAPI.addMoney(receiver, amount);
        if (!addedToReceiver) {
            // If adding to receiver fails, refund the sender
            Pokecobbleclaim.LOGGER.error("Failed to add money to receiver, refunding sender");
            MoneyAPI.addMoney(sender, amount);
            return false;
        }
        
        Pokecobbleclaim.LOGGER.info("Successfully transferred " + amount + " money from " + 
                                   sender.getName().getString() + " to " + receiver.getName().getString());
        return true;
    }
}
