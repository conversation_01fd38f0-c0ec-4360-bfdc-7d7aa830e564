package com.pokecobble.util;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.logging.ErrorLogger;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.server.network.ServerPlayerEntity;
import net.sixik.sdmshoprework.SDMShopR;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * API for interacting with the money system from SDMShopR mod.
 * Provides methods to check, add, and remove money with validation.
 */
public class MoneyAPI {
    // Source category for error logging
    private static final String ERROR_SOURCE = "Money";

    /**
     * Private constructor to prevent instantiation.
     * This is a utility class with static methods only.
     */
    private MoneyAPI() {
        // Private constructor to prevent instantiation
    }

    /**
     * Gets the player's current money balance.
     *
     * @param player The player to check
     * @return The player's current money balance
     */
    // Cache for player balances to reduce API calls
    private static final Map<UUID, CachedBalance> balanceCache = new HashMap<>();

    // Cache expiration time in milliseconds (5 seconds)
    private static final long CACHE_EXPIRATION_MS = 5000;

    // Class to store cached balance with timestamp
    private static class CachedBalance {
        final long balance;
        final long timestamp;

        CachedBalance(long balance) {
            this.balance = balance;
            this.timestamp = System.currentTimeMillis();
        }

        boolean isExpired() {
            return System.currentTimeMillis() - timestamp > CACHE_EXPIRATION_MS;
        }
    }

    /**
     * Gets the player's current money balance.
     * Uses caching to reduce API calls and handle SDMShopR unavailability.
     *
     * @param player The player to check
     * @return The player's current money balance
     */
    public static long getBalance(PlayerEntity player) {
        if (player == null) {
            return 0;
        }

        UUID playerId = player.getUuid();

        // Check if we have a valid cached balance
        CachedBalance cached = balanceCache.get(playerId);
        if (cached != null && !cached.isExpired()) {
            return cached.balance;
        }

        // Try to get balance from SDMShopR
        try {
            long balance = SDMShopR.getMoney(player);
            // Cache the result
            balanceCache.put(playerId, new CachedBalance(balance));
            return balance;
        } catch (Throwable e) {
            // Handle any error, including NoClassDefFoundError if SDMShopR is not available
            // Only log once per player to avoid spam
            if (cached == null || cached.isExpired()) {
                Pokecobbleclaim.LOGGER.debug("Failed to get money balance: " + e.getMessage());
                ErrorLogger.getInstance().logError(
                        "Failed to get money balance",
                        e,
                        ERROR_SOURCE,
                        ErrorLogger.ErrorSeverity.ERROR,
                        player.getName().getString(),
                        playerId
                );
            }

            // Return cached value if available, otherwise 0
            return cached != null ? cached.balance : 0;
        }
    }

    /**
     * Adds money to a player's balance with validation.
     *
     * @param player The server player to add money to
     * @param amount The amount of money to add (must be positive)
     * @return true if the operation was successful, false otherwise
     */
    public static boolean addMoney(ServerPlayerEntity player, long amount) {
        if (player == null) {
            ErrorLogger.getInstance().logError(
                    "Cannot add money to null player",
                    null,
                    ERROR_SOURCE,
                    ErrorLogger.ErrorSeverity.ERROR
            );
            return false;
        }

        if (amount <= 0) {
            ErrorLogger.getInstance().logError(
                    "Cannot add non-positive amount: " + amount,
                    null,
                    ERROR_SOURCE,
                    ErrorLogger.ErrorSeverity.ERROR,
                    player.getName().getString(),
                    player.getUuid()
            );
            return false;
        }

        try {
            // Get balance before operation
            long balanceBefore = getBalance(player);
            Pokecobbleclaim.LOGGER.debug("Player " + player.getName().getString() + " balance before adding money: " + balanceBefore);

            // Add money
            SDMShopR.addMoney(player, amount);

            // Get balance after operation
            long balanceAfter = getBalance(player);
            Pokecobbleclaim.LOGGER.debug("Player " + player.getName().getString() + " balance after adding money: " + balanceAfter);

            // Verify the operation was successful
            if (balanceAfter != balanceBefore + amount) {
                ErrorLogger.getInstance().logError(
                        "Money add operation failed: Expected balance " + (balanceBefore + amount) + " but got " + balanceAfter,
                        null,
                        ERROR_SOURCE,
                        ErrorLogger.ErrorSeverity.ERROR,
                        player.getName().getString(),
                        player.getUuid()
                );
                return false;
            }

            return true;
        } catch (Exception e) {
            ErrorLogger.getInstance().logError(
                    "Failed to add money: " + amount,
                    e,
                    ERROR_SOURCE,
                    ErrorLogger.ErrorSeverity.ERROR,
                    player.getName().getString(),
                    player.getUuid()
            );
            return false;
        }
    }

    /**
     * Removes money from a player's balance with validation.
     *
     * @param player The server player to remove money from
     * @param amount The amount of money to remove (must be positive)
     * @return true if the operation was successful, false otherwise
     */
    public static boolean removeMoney(ServerPlayerEntity player, long amount) {
        if (player == null) {
            ErrorLogger.getInstance().logError(
                    "Cannot remove money from null player",
                    null,
                    ERROR_SOURCE,
                    ErrorLogger.ErrorSeverity.ERROR
            );
            return false;
        }

        if (amount <= 0) {
            ErrorLogger.getInstance().logError(
                    "Cannot remove non-positive amount: " + amount,
                    null,
                    ERROR_SOURCE,
                    ErrorLogger.ErrorSeverity.ERROR,
                    player.getName().getString(),
                    player.getUuid()
            );
            return false;
        }

        try {
            // Get balance before operation
            long balanceBefore = getBalance(player);
            Pokecobbleclaim.LOGGER.debug("Player " + player.getName().getString() + " balance before removing money: " + balanceBefore);

            // Check if player has enough money
            if (balanceBefore < amount) {
                ErrorLogger.getInstance().logError(
                        "Insufficient funds: Player has " + balanceBefore + " but tried to remove " + amount,
                        null,
                        ERROR_SOURCE,
                        ErrorLogger.ErrorSeverity.WARNING,
                        player.getName().getString(),
                        player.getUuid()
                );
                return false;
            }

            // Remove money by setting the new balance
            long newBalance = balanceBefore - amount;
            SDMShopR.setMoney(player, newBalance);

            // Get balance after operation
            long balanceAfter = getBalance(player);
            Pokecobbleclaim.LOGGER.debug("Player " + player.getName().getString() + " balance after removing money: " + balanceAfter);

            // Verify the operation was successful
            if (balanceAfter != newBalance) {
                ErrorLogger.getInstance().logError(
                        "Money remove operation failed: Expected balance " + newBalance + " but got " + balanceAfter,
                        null,
                        ERROR_SOURCE,
                        ErrorLogger.ErrorSeverity.ERROR,
                        player.getName().getString(),
                        player.getUuid()
                );
                return false;
            }

            return true;
        } catch (Exception e) {
            ErrorLogger.getInstance().logError(
                    "Failed to remove money: " + amount,
                    e,
                    ERROR_SOURCE,
                    ErrorLogger.ErrorSeverity.ERROR,
                    player.getName().getString(),
                    player.getUuid()
            );
            return false;
        }
    }

    /**
     * Sets a player's money balance with validation.
     *
     * @param player The server player to set money for
     * @param amount The amount to set (must be non-negative)
     * @return true if the operation was successful, false otherwise
     */
    public static boolean setMoney(ServerPlayerEntity player, long amount) {
        if (player == null) {
            ErrorLogger.getInstance().logError(
                    "Cannot set money for null player",
                    null,
                    ERROR_SOURCE,
                    ErrorLogger.ErrorSeverity.ERROR
            );
            return false;
        }

        if (amount < 0) {
            ErrorLogger.getInstance().logError(
                    "Cannot set negative amount: " + amount,
                    null,
                    ERROR_SOURCE,
                    ErrorLogger.ErrorSeverity.ERROR,
                    player.getName().getString(),
                    player.getUuid()
            );
            return false;
        }

        try {
            // Get balance before operation
            long balanceBefore = getBalance(player);
            Pokecobbleclaim.LOGGER.debug("Player " + player.getName().getString() + " balance before setting money: " + balanceBefore);

            // Set money
            SDMShopR.setMoney(player, amount);

            // Get balance after operation
            long balanceAfter = getBalance(player);
            Pokecobbleclaim.LOGGER.debug("Player " + player.getName().getString() + " balance after setting money: " + balanceAfter);

            // Verify the operation was successful
            if (balanceAfter != amount) {
                ErrorLogger.getInstance().logError(
                        "Money set operation failed: Expected balance " + amount + " but got " + balanceAfter,
                        null,
                        ERROR_SOURCE,
                        ErrorLogger.ErrorSeverity.ERROR,
                        player.getName().getString(),
                        player.getUuid()
                );
                return false;
            }

            return true;
        } catch (Exception e) {
            ErrorLogger.getInstance().logError(
                    "Failed to set money: " + amount,
                    e,
                    ERROR_SOURCE,
                    ErrorLogger.ErrorSeverity.ERROR,
                    player.getName().getString(),
                    player.getUuid()
            );
            return false;
        }
    }
}
