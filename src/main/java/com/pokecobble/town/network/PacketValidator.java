package com.pokecobble.town.network;

import com.pokecobble.Pokecobbleclaim;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.util.math.ChunkPos;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * Utility class for validating packet data.
 * Helps prevent malicious packets and ensures data integrity.
 */
public class PacketValidator {

    // Regular expression for validating player names
    private static final Pattern PLAYER_NAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_]{3,16}$");

    // Regular expression for validating town names
    private static final Pattern TOWN_NAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_\\s]{3,32}$");

    // Maximum allowed packet size (1MB)
    private static final int MAX_PACKET_SIZE = 1024 * 1024;

    /**
     * Validates a string to ensure it's not too long and doesn't contain invalid characters.
     *
     * @param str The string to validate
     * @param maxLength The maximum allowed length
     * @param allowedPattern A regex pattern of allowed characters, or null to skip this check
     * @return The validated string, truncated if necessary
     * @throws IllegalArgumentException if the string is null or contains invalid characters
     */
    public static String validateString(String str, int maxLength, Pattern allowedPattern) {
        if (str == null) {
            throw new IllegalArgumentException("String cannot be null");
        }

        // Check for invalid characters
        if (allowedPattern != null && !allowedPattern.matcher(str).matches()) {
            throw new IllegalArgumentException("String contains invalid characters: " + str);
        }

        // Check for control characters
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (Character.isISOControl(c) && c != '\n' && c != '\r' && c != '\t') {
                throw new IllegalArgumentException("String contains control characters");
            }
        }

        // Truncate if too long
        if (str.length() > maxLength) {
            return str.substring(0, maxLength);
        }

        return str;
    }

    /**
     * Validates a string to ensure it's not too long.
     *
     * @param str The string to validate
     * @param maxLength The maximum allowed length
     * @return The validated string, truncated if necessary
     * @throws IllegalArgumentException if the string is null
     */
    public static String validateString(String str, int maxLength) {
        return validateString(str, maxLength, null);
    }

    /**
     * Validates a string using the default maximum length.
     *
     * @param str The string to validate
     * @return The validated string, truncated if necessary
     */
    public static String validateString(String str) {
        return validateString(str, NetworkConstants.MAX_STRING_LENGTH);
    }

    /**
     * Validates a UUID to ensure it's not null and is valid.
     *
     * @param uuid The UUID to validate
     * @throws IllegalArgumentException if the UUID is null or invalid
     */
    public static void validateUUID(UUID uuid) {
        if (uuid == null) {
            throw new IllegalArgumentException("UUID cannot be null");
        }

        // Check if UUID is all zeros (invalid)
        if (uuid.equals(new UUID(0, 0))) {
            throw new IllegalArgumentException("Invalid UUID: " + uuid);
        }
    }

    /**
     * Validates a UUID string to ensure it's a valid UUID.
     *
     * @param uuidStr The UUID string to validate
     * @return The parsed UUID
     * @throws IllegalArgumentException if the string is not a valid UUID
     */
    public static UUID validateUUIDString(String uuidStr) {
        if (uuidStr == null || uuidStr.isEmpty()) {
            throw new IllegalArgumentException("UUID string cannot be null or empty");
        }

        try {
            UUID uuid = UUID.fromString(uuidStr);
            validateUUID(uuid);
            return uuid;
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid UUID string: " + uuidStr);
        }
    }

    /**
     * Validates a chunk position to ensure it's within reasonable bounds.
     *
     * @param chunkPos The chunk position to validate
     * @throws IllegalArgumentException if the chunk position is null or out of bounds
     */
    public static void validateChunkPos(ChunkPos chunkPos) {
        if (chunkPos == null) {
            throw new IllegalArgumentException("ChunkPos cannot be null");
        }

        // Check for unreasonable chunk coordinates (extremely far from spawn)
        if (Math.abs(chunkPos.x) > 100000 || Math.abs(chunkPos.z) > 100000) {
            throw new IllegalArgumentException("ChunkPos out of reasonable bounds");
        }
    }

    /**
     * Validates that a player has permission to perform an action.
     *
     * @param player The player to check
     * @param permissionCategory The permission category
     * @param permission The specific permission
     * @return True if the player has permission, false otherwise
     */
    public static boolean validatePermission(ServerPlayerEntity player, String permissionCategory, String permission) {
        // For now, just check if the player is an operator
        // In a real implementation, this would check the player's permissions
        return player.hasPermissionLevel(2);
    }

    /**
     * Validates a packet buffer to ensure it's not too large.
     *
     * @param buf The packet buffer to validate
     * @throws IllegalArgumentException if the buffer is null or too large
     */
    public static void validatePacketSize(PacketByteBuf buf) {
        if (buf == null) {
            throw new IllegalArgumentException("PacketByteBuf cannot be null");
        }

        if (buf.readableBytes() > MAX_PACKET_SIZE) {
            throw new IllegalArgumentException("Packet too large: " + buf.readableBytes() + " bytes");
        }
    }

    /**
     * Validates a player name.
     *
     * @param playerName The player name to validate
     * @return The validated player name
     * @throws IllegalArgumentException if the player name is invalid
     */
    public static String validatePlayerName(String playerName) {
        return validateString(playerName, 16, PLAYER_NAME_PATTERN);
    }

    /**
     * Validates a town name.
     *
     * @param townName The town name to validate
     * @return The validated town name
     * @throws IllegalArgumentException if the town name is invalid
     */
    public static String validateTownName(String townName) {
        return validateString(townName, 32, TOWN_NAME_PATTERN);
    }

    /**
     * Validates a player entity to ensure it's not null and has a valid UUID.
     *
     * @param player The player entity to validate
     * @throws IllegalArgumentException if the player is null or has an invalid UUID
     */
    public static void validatePlayer(ServerPlayerEntity player) {
        if (player == null) {
            throw new IllegalArgumentException("Player cannot be null");
        }

        validateUUID(player.getUuid());
    }

    /**
     * Validates a map of permissions to ensure it doesn't contain invalid entries.
     *
     * @param permissions The permissions map to validate
     * @throws IllegalArgumentException if the permissions map contains invalid entries
     */
    public static void validatePermissions(Map<String, Map<String, Boolean>> permissions) {
        if (permissions == null) {
            throw new IllegalArgumentException("Permissions map cannot be null");
        }

        for (Map.Entry<String, Map<String, Boolean>> entry : permissions.entrySet()) {
            String category = entry.getKey();
            Map<String, Boolean> categoryPermissions = entry.getValue();

            // Validate category name
            validateString(category, 32);

            if (categoryPermissions == null) {
                throw new IllegalArgumentException("Permissions for category " + category + " cannot be null");
            }

            // Validate permission names
            for (String permission : categoryPermissions.keySet()) {
                validateString(permission, 32);
            }
        }
    }

    /**
     * Validates a list size to ensure it's not too large.
     *
     * @param size The size of the list
     * @param maxSize The maximum allowed size
     * @throws IllegalArgumentException if the size is negative or too large
     */
    public static void validateListSize(int size, int maxSize) {
        if (size < 0) {
            throw new IllegalArgumentException("List size cannot be negative");
        }

        if (size > maxSize) {
            throw new IllegalArgumentException("List size exceeds maximum allowed size");
        }
    }

    /**
     * Validates a list size using the default maximum size.
     *
     * @param size The size of the list
     * @throws IllegalArgumentException if the size is negative or too large
     */
    public static void validateListSize(int size) {
        validateListSize(size, NetworkConstants.MAX_LIST_SIZE);
    }
}
