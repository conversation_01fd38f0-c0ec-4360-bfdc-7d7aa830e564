package com.pokecobble.town.network.player;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.gui.NotificationManager;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;
import com.pokecobble.town.network.PacketValidator;
import com.pokecobble.town.network.security.PacketAuthenticator;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketSender;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayNetworkHandler;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Handles network communication for player-related operations.
 */
public class PlayerNetworkHandler {

    /**
     * Registers client-side packet handlers.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Register player data response handler
        ClientPlayNetworking.registerGlobalReceiver(
                NetworkConstants.PLAYER_DATA_RESPONSE,
                PlayerNetworkHandler::handlePlayerDataResponse
        );

        // Register player permissions update handler
        ClientPlayNetworking.registerGlobalReceiver(
                NetworkConstants.PLAYER_PERMISSIONS_UPDATE,
                PlayerNetworkHandler::handlePlayerPermissionsUpdate
        );

        // Register player data sync handler
        ClientPlayNetworking.registerGlobalReceiver(
                NetworkConstants.PLAYER_DATA_SYNC,
                PlayerNetworkHandler::handlePlayerDataSync
        );
    }

    /**
     * Registers server-side packet handlers.
     */
    public static void registerServerHandlers() {
        // Register player data request handler
        ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.PLAYER_DATA_REQUEST,
                PlayerNetworkHandler::handlePlayerDataRequest
        );

        // Register player permissions update handler
        ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.PLAYER_PERMISSIONS_UPDATE,
                PlayerNetworkHandler::handlePlayerPermissionsUpdate
        );
    }

    // Client-side methods

    /**
     * Requests player data from the server.
     *
     * @param playerId The UUID of the player to request data for
     */
    @Environment(EnvType.CLIENT)
    public static void requestPlayerData(UUID playerId) {
        try {
            // Validate UUID
            PacketValidator.validateUUID(playerId);

            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Add player UUID for validation
            if (MinecraftClient.getInstance().player != null) {
                buf.writeUuid(MinecraftClient.getInstance().player.getUuid());
            } else {
                return; // Can't send without a player
            }

            // Add target player UUID
            buf.writeUuid(playerId);

            // Send packet to server
            NetworkManager.sendToServer(NetworkConstants.PLAYER_DATA_REQUEST, buf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting player data: " + e.getMessage());
        }
    }

    /**
     * Updates player permissions on the server.
     *
     * @param playerId The UUID of the player to update permissions for
     * @param category The permission category
     * @param permissions The permissions to set
     */
    @Environment(EnvType.CLIENT)
    public static void updatePlayerPermissions(UUID playerId, String category, Map<String, Boolean> permissions) {
        try {
            // Validate parameters
            PacketValidator.validateUUID(playerId);
            String validatedCategory = PacketValidator.validateString(category);

            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Add player UUID for validation
            if (MinecraftClient.getInstance().player != null) {
                buf.writeUuid(MinecraftClient.getInstance().player.getUuid());
            } else {
                return; // Can't send without a player
            }

            // Add target player UUID
            buf.writeUuid(playerId);

            // Add permission category
            buf.writeString(validatedCategory);

            // Add permissions
            buf.writeInt(permissions.size());
            for (Map.Entry<String, Boolean> entry : permissions.entrySet()) {
                buf.writeString(PacketValidator.validateString(entry.getKey()));
                buf.writeBoolean(entry.getValue());
            }

            // Send packet to server
            NetworkManager.sendToServer(NetworkConstants.PLAYER_PERMISSIONS_UPDATE, buf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error updating player permissions: " + e.getMessage());
        }
    }

    // Client-side packet handlers

    /**
     * Handles player data response packets.
     */
    @Environment(EnvType.CLIENT)
    private static void handlePlayerDataResponse(MinecraftClient client, ClientPlayNetworkHandler handler,
                                               PacketByteBuf buf, PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read player UUID
            UUID playerId = buf.readUuid();

            // Read player name
            String playerName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

            // Read player rank
            int rankOrdinal = buf.readInt();
            TownPlayerRank playerRank = TownPlayerRank.values()[rankOrdinal];

            // Read player permissions
            int permissionCategoryCount = buf.readInt();
            Map<String, Map<String, Boolean>> permissions = new HashMap<>();

            for (int i = 0; i < permissionCategoryCount; i++) {
                String category = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                int permissionCount = buf.readInt();
                Map<String, Boolean> categoryPermissions = new HashMap<>();

                for (int j = 0; j < permissionCount; j++) {
                    String permission = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                    boolean value = buf.readBoolean();
                    categoryPermissions.put(permission, value);
                }

                permissions.put(category, categoryPermissions);
            }

            // Read town UUID
            UUID townId = buf.readUuid();

            // Update player data in TownManager
            Town town = TownManager.getInstance().getTownById(townId);

            if (town != null) {
                TownPlayer townPlayer = new TownPlayer(playerId, playerName, playerRank);

                // Set permissions
                for (Map.Entry<String, Map<String, Boolean>> entry : permissions.entrySet()) {
                    townPlayer.setCategoryPermissions(entry.getKey(), entry.getValue());
                }

                town.addPlayer(townPlayer);

                // Notify the player
                NotificationManager.getInstance().addInfoNotification("Player data updated: " + playerName);
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling player data response: " + e.getMessage());
        }
    }

    /**
     * Handles player permissions update packets.
     */
    @Environment(EnvType.CLIENT)
    private static void handlePlayerPermissionsUpdate(MinecraftClient client, ClientPlayNetworkHandler handler,
                                                    PacketByteBuf buf, PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read player UUID
            UUID playerId = buf.readUuid();

            // Read permission category
            String category = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

            // Read permissions
            int permissionCount = buf.readInt();
            Map<String, Boolean> permissions = new HashMap<>();

            for (int i = 0; i < permissionCount; i++) {
                String permission = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                boolean value = buf.readBoolean();
                permissions.put(permission, value);
            }

            // Update player permissions in TownManager
            if (client.player != null && playerId.equals(client.player.getUuid())) {
                Town playerTown = TownManager.getInstance().getPlayerTown(playerId);

                if (playerTown != null) {
                    TownPlayer townPlayer = playerTown.getPlayer(playerId);

                    if (townPlayer != null) {
                        townPlayer.setCategoryPermissions(category, permissions);

                        // Notify the player
                        NotificationManager.getInstance().addInfoNotification("Your permissions have been updated");
                    }
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling player permissions update: " + e.getMessage());
        }
    }

    /**
     * Handles player data sync packets.
     */
    @Environment(EnvType.CLIENT)
    public static void handlePlayerDataSync(MinecraftClient client, ClientPlayNetworkHandler handler,
                                           PacketByteBuf buf, PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Store the reader index to reset after authentication check
            int readerIndex = buf.readerIndex();

            // Verify packet authentication if the client player exists
            if (client.player != null) {
                // Skip to the end of the data to find the authentication signature
                // We'll reset the reader index after verification
                if (!PacketAuthenticator.verifyPacket(buf, client.player)) {
                    Pokecobbleclaim.LOGGER.error("Received player data sync packet with invalid authentication");
                    return;
                }

                // Reset reader index to read the actual data
                buf.readerIndex(readerIndex);
            }

            // Read player UUID
            UUID playerId = buf.readUuid();

            // Read player name
            String playerName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

            // Read player rank
            int rankOrdinal = buf.readInt();
            TownPlayerRank playerRank = TownPlayerRank.values()[rankOrdinal];

            // Read player permissions
            int permissionCategoryCount = buf.readInt();
            Map<String, Map<String, Boolean>> permissions = new HashMap<>();

            for (int i = 0; i < permissionCategoryCount; i++) {
                String category = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                int permissionCount = buf.readInt();
                Map<String, Boolean> categoryPermissions = new HashMap<>();

                for (int j = 0; j < permissionCount; j++) {
                    String permission = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                    boolean value = buf.readBoolean();
                    categoryPermissions.put(permission, value);
                }

                permissions.put(category, categoryPermissions);
            }

            // Read town UUID (or null)
            boolean hasTown = buf.readBoolean();
            UUID townId = hasTown ? buf.readUuid() : null;

            // Read data version
            int dataVersion = buf.readInt();

            // Create or update player data
            TownPlayer townPlayer = new TownPlayer(playerId, playerName, playerRank);
            townPlayer.setOnline(true); // Player is online if we're receiving data

            // Set permissions
            for (Map.Entry<String, Map<String, Boolean>> entry : permissions.entrySet()) {
                townPlayer.setCategoryPermissions(entry.getKey(), entry.getValue());
            }

            // Update town data if player is in a town
            if (hasTown) {
                Town town = TownManager.getInstance().getTownById(townId);
                if (town != null) {
                    town.addPlayer(townPlayer);
                }
            }

            // Only show notification if this is the current player
            if (client.player != null && playerId.equals(client.player.getUuid())) {
                NotificationManager.getInstance().addInfoNotification("Your player data has been synchronized");
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling player data sync: " + e.getMessage());
        }
    }

    // Server-side packet handlers

    /**
     * Handles player data request packets.
     */
    private static void handlePlayerDataRequest(MinecraftServer server, ServerPlayerEntity player,
                                              ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                              PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read player UUID for validation
            UUID requestingPlayerId = buf.readUuid();

            // Validate that the requesting player is the same as the sender
            if (!player.getUuid().equals(requestingPlayerId)) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " tried to request player data for another player");
                return;
            }

            // Read target player UUID
            UUID targetPlayerId = buf.readUuid();

            // Get the player's town
            Town playerTown = TownManager.getInstance().getPlayerTown(player.getUuid());

            // Check if the target player is in the same town
            if (playerTown == null || !playerTown.getPlayers().contains(targetPlayerId)) {
                // Target player is not in the same town
                player.sendMessage(Text.literal("Player is not in your town").formatted(Formatting.RED), false);
                return;
            }

            // Get the target player
            TownPlayer targetPlayer = playerTown.getPlayer(targetPlayerId);

            if (targetPlayer == null) {
                // Target player not found
                player.sendMessage(Text.literal("Player not found").formatted(Formatting.RED), false);
                return;
            }

            // Create response packet
            PacketByteBuf responseBuf = NetworkManager.createPacket();

            // Write player UUID
            responseBuf.writeUuid(targetPlayerId);

            // Write player name
            responseBuf.writeString(targetPlayer.getName());

            // Write player rank
            responseBuf.writeInt(targetPlayer.getRank().ordinal());

            // Write player permissions
            Map<String, Map<String, Boolean>> permissions = targetPlayer.getAllPermissions();

            responseBuf.writeInt(permissions.size());

            for (Map.Entry<String, Map<String, Boolean>> entry : permissions.entrySet()) {
                responseBuf.writeString(entry.getKey());

                Map<String, Boolean> categoryPermissions = entry.getValue();
                responseBuf.writeInt(categoryPermissions.size());

                for (Map.Entry<String, Boolean> permEntry : categoryPermissions.entrySet()) {
                    responseBuf.writeString(permEntry.getKey());
                    responseBuf.writeBoolean(permEntry.getValue());
                }
            }

            // Write town UUID
            responseBuf.writeUuid(playerTown.getId());

            // Send response to player
            NetworkManager.sendToPlayer(player, NetworkConstants.PLAYER_DATA_RESPONSE, responseBuf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling player data request: " + e.getMessage());
        }
    }

    /**
     * Handles player permissions update packets.
     */
    private static void handlePlayerPermissionsUpdate(MinecraftServer server, ServerPlayerEntity player,
                                                    ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                                    PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read player UUID for validation
            UUID requestingPlayerId = buf.readUuid();

            // Validate that the requesting player is the same as the sender
            if (!player.getUuid().equals(requestingPlayerId)) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " tried to update permissions for another player");
                return;
            }

            // Read target player UUID
            UUID targetPlayerId = buf.readUuid();

            // Read permission category
            String category = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

            // Read permissions
            int permissionCount = buf.readInt();
            PacketValidator.validateListSize(permissionCount);

            Map<String, Boolean> permissions = new HashMap<>();

            for (int i = 0; i < permissionCount; i++) {
                String permission = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                boolean value = buf.readBoolean();
                permissions.put(permission, value);
            }

            // Get the player's town
            Town playerTown = TownManager.getInstance().getPlayerTown(player.getUuid());

            // Check if the player has permission to update permissions
            if (playerTown == null) {
                // Player is not in a town
                player.sendMessage(Text.literal("You are not in a town").formatted(Formatting.RED), false);
                return;
            }

            // Check if the target player is in the same town
            if (!playerTown.getPlayers().contains(targetPlayerId)) {
                // Target player is not in the same town
                player.sendMessage(Text.literal("Player is not in your town").formatted(Formatting.RED), false);
                return;
            }

            // Check if the player has permission to update permissions
            TownPlayer townPlayer = playerTown.getPlayer(player.getUuid());

            if (townPlayer == null) {
                // Player not found in town
                player.sendMessage(Text.literal("You are not in this town").formatted(Formatting.RED), false);
                return;
            }

            // Check if the player is the owner or has permission to manage player permissions
            boolean canManagePermissions = townPlayer.getRank() == TownPlayerRank.OWNER ||
                                          townPlayer.hasPermission("Player Management", "Can manage player permissions");

            if (!canManagePermissions) {
                // Player doesn't have permission
                player.sendMessage(Text.literal("You don't have permission to manage player permissions").formatted(Formatting.RED), false);
                return;
            }

            // Get the target player
            TownPlayer targetTownPlayer = playerTown.getPlayer(targetPlayerId);

            if (targetTownPlayer == null) {
                // Target player not found
                player.sendMessage(Text.literal("Player not found").formatted(Formatting.RED), false);
                return;
            }

            // Check if the target player is the owner
            if (targetTownPlayer.getRank() == TownPlayerRank.OWNER && !player.getUuid().equals(targetPlayerId)) {
                // Can't modify owner permissions
                player.sendMessage(Text.literal("You can't modify the owner's permissions").formatted(Formatting.RED), false);
                return;
            }

            // Update permissions
            targetTownPlayer.setCategoryPermissions(category, permissions);

            // Save the town
            TownManager.getInstance().saveTown(playerTown);

            // Notify the player
            player.sendMessage(Text.literal("Permissions updated for " + targetTownPlayer.getName()).formatted(Formatting.GREEN), false);

            // Notify the target player if they're online
            ServerPlayerEntity targetPlayer = server.getPlayerManager().getPlayer(targetPlayerId);

            if (targetPlayer != null) {
                targetPlayer.sendMessage(Text.literal("Your permissions have been updated by " + player.getName().getString()).formatted(Formatting.GOLD), false);

                // Send updated permissions to the target player
                PacketByteBuf responseBuf = NetworkManager.createPacket();

                // Write player UUID
                responseBuf.writeUuid(targetPlayerId);

                // Write permission category
                responseBuf.writeString(category);

                // Write permissions
                responseBuf.writeInt(permissions.size());

                for (Map.Entry<String, Boolean> entry : permissions.entrySet()) {
                    responseBuf.writeString(entry.getKey());
                    responseBuf.writeBoolean(entry.getValue());
                }

                // Send response to target player
                NetworkManager.sendToPlayer(targetPlayer, NetworkConstants.PLAYER_PERMISSIONS_UPDATE, responseBuf);
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling player permissions update: " + e.getMessage());
        }
    }
}
