package com.pokecobble.town.network.money;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.phone.gui.BankAppScreen;
import com.pokecobble.town.logging.ErrorLogger;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;
import com.pokecobble.util.MoneyAPI;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketSender;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayNetworkHandler;
import net.minecraft.server.network.ServerPlayerEntity;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * Handles network communication for money-related operations.
 * Implements an optimized approach where:
 * 1. Client requests balance when opening the bank app
 * 2. Server sends balance only if it has changed
 * 3. Client registers for balance updates while bank app is open
 * 4. Client unregisters when bank app is closed
 */
public class MoneyNetworkHandler {
    // Store player balances on the client side
    private static final Map<UUID, Long> clientPlayerBalances = new HashMap<>();

    // Store player balances on the server side for comparison
    private static final Map<UUID, Long> serverPlayerBalances = new HashMap<>();

    // Set of players who are currently watching their balance (have bank app open)
    private static final Set<UUID> watchingPlayers = new HashSet<>();

    /**
     * Registers client-side packet handlers.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Register money balance sync handler
        ClientPlayNetworking.registerGlobalReceiver(
                NetworkConstants.MONEY_BALANCE_SYNC,
                MoneyNetworkHandler::handleMoneyBalanceSync
        );

        // Register money transfer response handler
        ClientPlayNetworking.registerGlobalReceiver(
                NetworkConstants.MONEY_TRANSFER_RESPONSE,
                MoneyNetworkHandler::handleMoneyTransferResponse
        );
    }

    /**
     * Registers server-side packet handlers.
     */
    public static void registerServerHandlers() {
        // Register money balance request handler
        ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.MONEY_BALANCE_REQUEST,
                MoneyNetworkHandler::handleMoneyBalanceRequest
        );

        // Register money watch start handler
        ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.MONEY_WATCH_START,
                MoneyNetworkHandler::handleMoneyWatchStart
        );

        // Register money watch stop handler
        ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.MONEY_WATCH_STOP,
                MoneyNetworkHandler::handleMoneyWatchStop
        );

        // Register money transfer request handler
        ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.MONEY_TRANSFER_REQUEST,
                MoneyNetworkHandler::handleMoneyTransferRequest
        );
    }

    /**
     * Handles money balance sync packets on the client side.
     *
     * @param client The client instance
     * @param handler The network handler
     * @param buf The packet buffer
     * @param responseSender The packet sender
     */
    @Environment(EnvType.CLIENT)
    private static void handleMoneyBalanceSync(MinecraftClient client, ClientPlayNetworkHandler handler,
                                              PacketByteBuf buf, PacketSender responseSender) {
        try {
            // Read player UUID
            UUID playerId = buf.readUuid();

            // Read balance
            long balance = buf.readLong();

            // Store balance
            clientPlayerBalances.put(playerId, balance);

            Pokecobbleclaim.LOGGER.debug("Received money balance sync: " + balance + " for player " + playerId);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling money balance sync: " + e.getMessage());
            ErrorLogger.getInstance().logError(
                "Error handling money balance sync",
                e,
                "Money",
                ErrorLogger.ErrorSeverity.ERROR
            );
        }
    }

    /**
     * Handles money transfer response packets on the client side.
     *
     * @param client The client instance
     * @param handler The network handler
     * @param buf The packet buffer
     * @param responseSender The packet sender
     */
    @Environment(EnvType.CLIENT)
    private static void handleMoneyTransferResponse(MinecraftClient client, ClientPlayNetworkHandler handler,
                                                  PacketByteBuf buf, PacketSender responseSender) {
        try {
            // Read transfer result
            int resultCode = buf.readInt();
            String message = buf.readString();

            Pokecobbleclaim.LOGGER.debug("Received money transfer response: " + resultCode + " - " + message);

            // Handle the response on the main thread
            client.execute(() -> {
                handleTransferResult(resultCode, message);
            });
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling money transfer response: " + e.getMessage());
            ErrorLogger.getInstance().logError(
                "Error handling money transfer response",
                e,
                "Money",
                ErrorLogger.ErrorSeverity.ERROR
            );
        }
    }

    /**
     * Handles money balance request packets on the server side.
     *
     * @param server The server instance
     * @param player The player who sent the request
     * @param handler The network handler
     * @param buf The packet buffer
     * @param responseSender The packet sender
     */
    private static void handleMoneyBalanceRequest(MinecraftServer server, ServerPlayerEntity player,
                                                ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                                PacketSender responseSender) {
        try {
            // Get player UUID
            UUID playerId = player.getUuid();

            // Get current balance
            long currentBalance = MoneyAPI.getBalance(player);

            // Get previous balance
            Long previousBalance = serverPlayerBalances.get(playerId);

            // Check if balance has changed or if this is the first request
            if (previousBalance == null || previousBalance != currentBalance) {
                // Update stored balance
                serverPlayerBalances.put(playerId, currentBalance);

                // Send balance to client
                sendBalanceToClient(player, currentBalance);

                Pokecobbleclaim.LOGGER.debug("Sent money balance to " + player.getName().getString() +
                                          ": " + currentBalance + " (changed from " + previousBalance + ")");
            } else {
                Pokecobbleclaim.LOGGER.debug("Balance unchanged for " + player.getName().getString() +
                                          ", not sending update");
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling money balance request: " + e.getMessage());
            ErrorLogger.getInstance().logError(
                "Error handling money balance request",
                e,
                "Money",
                ErrorLogger.ErrorSeverity.ERROR,
                player.getName().getString(),
                player.getUuid()
            );
        }
    }

    /**
     * Handles money watch start packets on the server side.
     *
     * @param server The server instance
     * @param player The player who sent the request
     * @param handler The network handler
     * @param buf The packet buffer
     * @param responseSender The packet sender
     */
    private static void handleMoneyWatchStart(MinecraftServer server, ServerPlayerEntity player,
                                            ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                            PacketSender responseSender) {
        try {
            // Add player to watching set
            watchingPlayers.add(player.getUuid());

            Pokecobbleclaim.LOGGER.debug("Player " + player.getName().getString() + " started watching money balance");

            // Send initial balance
            long currentBalance = MoneyAPI.getBalance(player);
            serverPlayerBalances.put(player.getUuid(), currentBalance);
            sendBalanceToClient(player, currentBalance);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling money watch start: " + e.getMessage());
            ErrorLogger.getInstance().logError(
                "Error handling money watch start",
                e,
                "Money",
                ErrorLogger.ErrorSeverity.ERROR,
                player.getName().getString(),
                player.getUuid()
            );
        }
    }

    /**
     * Handles money watch stop packets on the server side.
     *
     * @param server The server instance
     * @param player The player who sent the request
     * @param handler The network handler
     * @param buf The packet buffer
     * @param responseSender The packet sender
     */
    private static void handleMoneyWatchStop(MinecraftServer server, ServerPlayerEntity player,
                                           ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                           PacketSender responseSender) {
        try {
            // Remove player from watching set
            watchingPlayers.remove(player.getUuid());

            Pokecobbleclaim.LOGGER.debug("Player " + player.getName().getString() + " stopped watching money balance");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling money watch stop: " + e.getMessage());
            ErrorLogger.getInstance().logError(
                "Error handling money watch stop",
                e,
                "Money",
                ErrorLogger.ErrorSeverity.ERROR,
                player.getName().getString(),
                player.getUuid()
            );
        }
    }

    /**
     * Sends a player's balance to the client.
     *
     * @param player The player to send the balance to
     * @param balance The balance to send
     */
    private static void sendBalanceToClient(ServerPlayerEntity player, long balance) {
        try {
            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Write player UUID
            buf.writeUuid(player.getUuid());

            // Write balance
            buf.writeLong(balance);

            // Send packet to player
            NetworkManager.sendToPlayer(player, NetworkConstants.MONEY_BALANCE_SYNC, buf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending balance to client: " + e.getMessage());
            ErrorLogger.getInstance().logError(
                "Error sending balance to client",
                e,
                "Money",
                ErrorLogger.ErrorSeverity.ERROR,
                player.getName().getString(),
                player.getUuid()
            );
        }
    }

    /**
     * Handles money transfer request packets on the server side.
     *
     * @param server The server instance
     * @param player The player who sent the request
     * @param handler The network handler
     * @param buf The packet buffer
     * @param responseSender The packet sender
     */
    private static void handleMoneyTransferRequest(MinecraftServer server, ServerPlayerEntity player,
                                                 ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                                 PacketSender responseSender) {
        try {
            // Read transfer data
            String targetPlayerName = buf.readString();
            long amount = buf.readLong();

            Pokecobbleclaim.LOGGER.debug("Transfer request: " + player.getName().getString() +
                                       " wants to send " + amount + " to " + targetPlayerName);

            // Validate amount
            if (amount <= 0) {
                String errorMsg = "Invalid transfer amount: " + amount;
                ErrorLogger.getInstance().logError(
                    errorMsg,
                    null,
                    "Money",
                    ErrorLogger.ErrorSeverity.WARNING,
                    player.getName().getString(),
                    player.getUuid()
                );
                sendTransferResponse(player, TRANSFER_RESULT_ERROR, "Invalid amount");
                return;
            }

            // Find target player
            ServerPlayerEntity targetPlayer = server.getPlayerManager().getPlayer(targetPlayerName);
            if (targetPlayer == null) {
                String errorMsg = "Transfer failed: Target player '" + targetPlayerName + "' not found";
                ErrorLogger.getInstance().logError(
                    errorMsg,
                    null,
                    "Money",
                    ErrorLogger.ErrorSeverity.WARNING,
                    player.getName().getString(),
                    player.getUuid()
                );
                sendTransferResponse(player, TRANSFER_RESULT_ERROR, "Player not found");
                return;
            }

            // Check if trying to send to self
            if (player.getUuid().equals(targetPlayer.getUuid())) {
                String errorMsg = "Transfer failed: Player attempted to transfer to themselves";
                ErrorLogger.getInstance().logError(
                    errorMsg,
                    null,
                    "Money",
                    ErrorLogger.ErrorSeverity.WARNING,
                    player.getName().getString(),
                    player.getUuid()
                );
                sendTransferResponse(player, TRANSFER_RESULT_ERROR, "Cannot transfer to yourself");
                return;
            }

            // Check sender's balance
            long senderBalance = MoneyAPI.getBalance(player);
            if (senderBalance < amount) {
                String errorMsg = "Transfer failed: Insufficient funds. Has: " + senderBalance + ", Needs: " + amount;
                ErrorLogger.getInstance().logError(
                    errorMsg,
                    null,
                    "Money",
                    ErrorLogger.ErrorSeverity.WARNING,
                    player.getName().getString(),
                    player.getUuid()
                );
                sendTransferResponse(player, TRANSFER_RESULT_INSUFFICIENT_FUNDS, "Insufficient funds");
                return;
            }

            // Perform the transfer using the existing transfer logic
            boolean success = performTransfer(player, targetPlayer, amount);

            if (success) {
                // Log successful transfer
                String successMsg = "Transfer successful: " + amount + " from " + player.getName().getString() +
                                  " to " + targetPlayer.getName().getString();
                ErrorLogger.getInstance().logInfo(successMsg, "Money");

                sendTransferResponse(player, TRANSFER_RESULT_SUCCESS, "Transfer successful");

                // Update balances for both players if they're watching
                if (watchingPlayers.contains(player.getUuid())) {
                    long newSenderBalance = MoneyAPI.getBalance(player);
                    serverPlayerBalances.put(player.getUuid(), newSenderBalance);
                    sendBalanceToClient(player, newSenderBalance);
                }

                if (watchingPlayers.contains(targetPlayer.getUuid())) {
                    long newTargetBalance = MoneyAPI.getBalance(targetPlayer);
                    serverPlayerBalances.put(targetPlayer.getUuid(), newTargetBalance);
                    sendBalanceToClient(targetPlayer, newTargetBalance);
                }
            } else {
                // Log transfer failure
                String errorMsg = "Transfer failed: " + amount + " from " + player.getName().getString() +
                                " to " + targetPlayer.getName().getString() + " - API operation failed";
                ErrorLogger.getInstance().logError(
                    errorMsg,
                    null,
                    "Money",
                    ErrorLogger.ErrorSeverity.ERROR,
                    player.getName().getString(),
                    player.getUuid()
                );
                sendTransferResponse(player, TRANSFER_RESULT_ERROR, "Transfer failed");
            }

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling money transfer request: " + e.getMessage());
            ErrorLogger.getInstance().logError(
                "Error handling money transfer request",
                e,
                "Money",
                ErrorLogger.ErrorSeverity.ERROR,
                player.getName().getString(),
                player.getUuid()
            );
            sendTransferResponse(player, TRANSFER_RESULT_ERROR, "Internal error");
        }
    }

    // Transfer result constants
    public static final int TRANSFER_RESULT_SUCCESS = 0;
    public static final int TRANSFER_RESULT_INSUFFICIENT_FUNDS = 1;
    public static final int TRANSFER_RESULT_ERROR = 2;

    /**
     * Performs a money transfer between two players with proper error handling.
     *
     * @param sender The player sending money
     * @param receiver The player receiving money
     * @param amount The amount to transfer
     * @return true if successful, false otherwise
     */
    private static boolean performTransfer(ServerPlayerEntity sender, ServerPlayerEntity receiver, long amount) {
        try {
            // Get initial balances
            long senderBalanceBefore = MoneyAPI.getBalance(sender);
            long receiverBalanceBefore = MoneyAPI.getBalance(receiver);

            Pokecobbleclaim.LOGGER.debug("Transfer attempt: " + sender.getName().getString() +
                                       " (" + senderBalanceBefore + ") -> " + receiver.getName().getString() +
                                       " (" + receiverBalanceBefore + ") amount: " + amount);

            // Check sender's balance again
            if (senderBalanceBefore < amount) {
                String errorMsg = "Transfer validation failed: Sender balance insufficient during transfer execution. Has: " +
                                senderBalanceBefore + ", Needs: " + amount;
                ErrorLogger.getInstance().logError(
                    errorMsg,
                    null,
                    "Money",
                    ErrorLogger.ErrorSeverity.ERROR,
                    sender.getName().getString(),
                    sender.getUuid()
                );
                Pokecobbleclaim.LOGGER.warn("Sender doesn't have enough money. Has: " + senderBalanceBefore + ", Needs: " + amount);
                return false;
            }

            // Remove money from sender
            boolean removedFromSender = MoneyAPI.removeMoney(sender, amount);
            if (!removedFromSender) {
                String errorMsg = "Transfer failed: Unable to remove " + amount + " from sender's account";
                ErrorLogger.getInstance().logError(
                    errorMsg,
                    null,
                    "Money",
                    ErrorLogger.ErrorSeverity.ERROR,
                    sender.getName().getString(),
                    sender.getUuid()
                );
                Pokecobbleclaim.LOGGER.error("Failed to remove money from sender");
                return false;
            }

            // Verify money was removed
            long senderBalanceAfterRemoval = MoneyAPI.getBalance(sender);
            if (senderBalanceAfterRemoval != senderBalanceBefore - amount) {
                String errorMsg = "Transfer failed: Money removal verification failed. Expected: " +
                                (senderBalanceBefore - amount) + ", Got: " + senderBalanceAfterRemoval +
                                ". Attempting refund.";
                ErrorLogger.getInstance().logError(
                    errorMsg,
                    null,
                    "Money",
                    ErrorLogger.ErrorSeverity.CRITICAL,
                    sender.getName().getString(),
                    sender.getUuid()
                );
                Pokecobbleclaim.LOGGER.error("Money removal verification failed. Expected: " +
                                           (senderBalanceBefore - amount) + ", Got: " + senderBalanceAfterRemoval);
                // Try to refund
                MoneyAPI.addMoney(sender, amount);
                return false;
            }

            // Add money to receiver
            boolean addedToReceiver = MoneyAPI.addMoney(receiver, amount);
            if (!addedToReceiver) {
                String errorMsg = "Transfer failed: Unable to add " + amount + " to receiver's account. Refunding sender.";
                ErrorLogger.getInstance().logError(
                    errorMsg,
                    null,
                    "Money",
                    ErrorLogger.ErrorSeverity.CRITICAL,
                    sender.getName().getString(),
                    sender.getUuid()
                );
                Pokecobbleclaim.LOGGER.error("Failed to add money to receiver, refunding sender");
                // Refund the sender
                MoneyAPI.addMoney(sender, amount);
                return false;
            }

            // Verify money was added
            long receiverBalanceAfterAddition = MoneyAPI.getBalance(receiver);
            if (receiverBalanceAfterAddition != receiverBalanceBefore + amount) {
                String errorMsg = "Transfer failed: Money addition verification failed. Expected: " +
                                (receiverBalanceBefore + amount) + ", Got: " + receiverBalanceAfterAddition +
                                ". Attempting rollback.";
                ErrorLogger.getInstance().logError(
                    errorMsg,
                    null,
                    "Money",
                    ErrorLogger.ErrorSeverity.CRITICAL,
                    sender.getName().getString(),
                    sender.getUuid()
                );
                Pokecobbleclaim.LOGGER.error("Money addition verification failed. Expected: " +
                                           (receiverBalanceBefore + amount) + ", Got: " + receiverBalanceAfterAddition);
                // Try to refund sender and remove from receiver
                MoneyAPI.addMoney(sender, amount);
                MoneyAPI.removeMoney(receiver, amount);
                return false;
            }

            // Log successful transfer completion
            String successMsg = "Transfer completed successfully: " + amount + " transferred from " +
                              sender.getName().getString() + " (balance: " + senderBalanceBefore + " -> " +
                              senderBalanceAfterRemoval + ") to " + receiver.getName().getString() +
                              " (balance: " + receiverBalanceBefore + " -> " + receiverBalanceAfterAddition + ")";
            ErrorLogger.getInstance().logInfo(successMsg, "Money");

            Pokecobbleclaim.LOGGER.info("Successfully transferred " + amount + " money from " +
                                       sender.getName().getString() + " to " + receiver.getName().getString());
            return true;

        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error during money transfer: " + e.getMessage());
            ErrorLogger.getInstance().logError(
                "Error during money transfer",
                e,
                "Money",
                ErrorLogger.ErrorSeverity.ERROR,
                sender.getName().getString(),
                sender.getUuid()
            );
            return false;
        }
    }

    /**
     * Sends a transfer response to the client.
     *
     * @param player The player to send the response to
     * @param resultCode The result code
     * @param message The result message
     */
    private static void sendTransferResponse(ServerPlayerEntity player, int resultCode, String message) {
        try {
            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Write result data
            buf.writeInt(resultCode);
            buf.writeString(message);

            // Send packet to player
            NetworkManager.sendToPlayer(player, NetworkConstants.MONEY_TRANSFER_RESPONSE, buf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending transfer response: " + e.getMessage());
            ErrorLogger.getInstance().logError(
                "Error sending transfer response",
                e,
                "Money",
                ErrorLogger.ErrorSeverity.ERROR,
                player.getName().getString(),
                player.getUuid()
            );
        }
    }

    // Track last check time to limit check frequency
    private static long lastCheckTime = 0;

    // Check interval in milliseconds (500ms)
    private static final long CHECK_INTERVAL_MS = 500;

    // Track error counts to avoid spam
    private static final Map<UUID, Integer> errorCounts = new HashMap<>();

    // Maximum errors before stopping checks for a player
    private static final int MAX_ERRORS = 3;

    /**
     * Checks and updates balances for all watching players.
     * This should be called periodically from the server tick event.
     * Optimized to reduce lag and handle errors gracefully.
     *
     * @param server The server instance
     */
    public static void checkWatchingPlayersBalances(MinecraftServer server) {
        // Skip if no players are watching
        if (watchingPlayers.isEmpty()) {
            return;
        }

        // Limit check frequency to reduce lag
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastCheckTime < CHECK_INTERVAL_MS) {
            return;
        }
        lastCheckTime = currentTime;

        // Create a copy of the set to avoid concurrent modification
        Set<UUID> playersToCheck = new HashSet<>(watchingPlayers);

        // Check each watching player
        for (UUID playerId : playersToCheck) {
            ServerPlayerEntity player = server.getPlayerManager().getPlayer(playerId);
            if (player == null) {
                // Player is offline, remove from watching
                watchingPlayers.remove(playerId);
                serverPlayerBalances.remove(playerId);
                errorCounts.remove(playerId);
                continue;
            }

            try {
                // Get current balance
                long currentBalance = MoneyAPI.getBalance(player);

                // Get previous balance
                Long previousBalance = serverPlayerBalances.get(playerId);

                // Check if balance has changed
                if (previousBalance == null || previousBalance != currentBalance) {
                    // Update stored balance
                    serverPlayerBalances.put(playerId, currentBalance);

                    // Send balance to client
                    sendBalanceToClient(player, currentBalance);

                    // Reset error count on success
                    errorCounts.remove(playerId);

                    if (previousBalance != null) {
                        Pokecobbleclaim.LOGGER.debug("Balance changed for " + player.getName().getString() +
                                                  ": " + previousBalance + " -> " + currentBalance);
                    }
                }
            } catch (Exception e) {
                // Track errors for this player
                int errorCount = errorCounts.getOrDefault(playerId, 0) + 1;
                errorCounts.put(playerId, errorCount);

                // Log only the first error to avoid spam
                if (errorCount == 1) {
                    Pokecobbleclaim.LOGGER.error("Error checking balance for " + player.getName().getString() +
                                              ": " + e.getMessage());
                    ErrorLogger.getInstance().logError(
                        "Error checking player balance",
                        e,
                        "Money",
                        ErrorLogger.ErrorSeverity.ERROR,
                        player.getName().getString(),
                        playerId
                    );
                }

                // If too many errors, stop watching this player
                if (errorCount >= MAX_ERRORS) {
                    Pokecobbleclaim.LOGGER.warn("Too many errors checking balance for " +
                                             player.getName().getString() + ", stopping balance updates");
                    watchingPlayers.remove(playerId);
                }
            }
        }
    }

    /**
     * Requests the player's balance from the server.
     * This should be called when the bank app is opened.
     */
    @Environment(EnvType.CLIENT)
    public static void requestBalance() {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player == null) {
                return;
            }

            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Send packet to server
            ClientPlayNetworking.send(NetworkConstants.MONEY_BALANCE_REQUEST, buf);

            Pokecobbleclaim.LOGGER.debug("Requested money balance from server");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting money balance: " + e.getMessage());
            ErrorLogger.getInstance().logError(
                "Error requesting money balance",
                e,
                "Money",
                ErrorLogger.ErrorSeverity.ERROR
            );
        }
    }

    /**
     * Starts watching the player's balance.
     * This should be called when the bank app is opened.
     */
    @Environment(EnvType.CLIENT)
    public static void startWatchingBalance() {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player == null) {
                return;
            }

            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Send packet to server
            ClientPlayNetworking.send(NetworkConstants.MONEY_WATCH_START, buf);

            Pokecobbleclaim.LOGGER.debug("Started watching money balance");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error starting money watch: " + e.getMessage());
            ErrorLogger.getInstance().logError(
                "Error starting money watch",
                e,
                "Money",
                ErrorLogger.ErrorSeverity.ERROR
            );
        }
    }

    /**
     * Stops watching the player's balance.
     * This should be called when the bank app is closed.
     */
    @Environment(EnvType.CLIENT)
    public static void stopWatchingBalance() {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player == null) {
                return;
            }

            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Send packet to server
            ClientPlayNetworking.send(NetworkConstants.MONEY_WATCH_STOP, buf);

            Pokecobbleclaim.LOGGER.debug("Stopped watching money balance");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error stopping money watch: " + e.getMessage());
            ErrorLogger.getInstance().logError(
                "Error stopping money watch",
                e,
                "Money",
                ErrorLogger.ErrorSeverity.ERROR
            );
        }
    }

    /**
     * Gets the current player's cached balance on the client side.
     *
     * @return The current player's balance, or 0 if not available
     */
    @Environment(EnvType.CLIENT)
    public static long getCurrentPlayerBalance() {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player == null) {
                return 0;
            }
            return clientPlayerBalances.getOrDefault(client.player.getUuid(), 0L);
        } catch (Exception e) {
            // If there's any error (including if SDMShopR is not available), return 0
            Pokecobbleclaim.LOGGER.error("Error getting player balance: " + e.getMessage());
            return 0;
        }
    }

    /**
     * Sends a money transfer request to the server.
     *
     * @param targetPlayerName The name of the player to send money to
     * @param amount The amount to transfer
     */
    @Environment(EnvType.CLIENT)
    public static void requestMoneyTransfer(String targetPlayerName, long amount) {
        try {
            MinecraftClient client = MinecraftClient.getInstance();
            if (client.player == null) {
                ErrorLogger.getInstance().logError(
                    "Transfer request failed: No player instance available",
                    null,
                    "Money",
                    ErrorLogger.ErrorSeverity.ERROR
                );
                return;
            }

            // Log transfer request initiation
            String requestMsg = "Initiating transfer request: " + amount + " to " + targetPlayerName;
            ErrorLogger.getInstance().logInfo(requestMsg, "Money");

            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Write transfer data
            buf.writeString(targetPlayerName);
            buf.writeLong(amount);

            // Send packet to server
            ClientPlayNetworking.send(NetworkConstants.MONEY_TRANSFER_REQUEST, buf);

            Pokecobbleclaim.LOGGER.debug("Requested money transfer: " + amount + " to " + targetPlayerName);
        } catch (Exception e) {
            String errorMsg = "Error requesting money transfer to " + targetPlayerName + " for amount " + amount;
            Pokecobbleclaim.LOGGER.error("Error requesting money transfer: " + e.getMessage());
            ErrorLogger.getInstance().logError(
                errorMsg,
                e,
                "Money",
                ErrorLogger.ErrorSeverity.ERROR
            );
        }
    }

    /**
     * Handles the transfer result on the client side.
     *
     * @param resultCode The result code from the server
     * @param message The result message
     */
    @Environment(EnvType.CLIENT)
    private static void handleTransferResult(int resultCode, String message) {
        try {
            // Log the transfer result
            String resultMsg = "Transfer result received: ";
            ErrorLogger.ErrorSeverity severity;

            switch (resultCode) {
                case TRANSFER_RESULT_SUCCESS:
                    resultMsg += "SUCCESS - " + message;
                    severity = ErrorLogger.ErrorSeverity.INFO;
                    break;
                case TRANSFER_RESULT_INSUFFICIENT_FUNDS:
                    resultMsg += "INSUFFICIENT_FUNDS - " + message;
                    severity = ErrorLogger.ErrorSeverity.WARNING;
                    break;
                case TRANSFER_RESULT_ERROR:
                default:
                    resultMsg += "ERROR - " + message;
                    severity = ErrorLogger.ErrorSeverity.ERROR;
                    break;
            }

            ErrorLogger.getInstance().logError(resultMsg, null, "Money", severity);

            MinecraftClient client = MinecraftClient.getInstance();
            if (client.currentScreen instanceof BankAppScreen) {
                BankAppScreen bankScreen = (BankAppScreen) client.currentScreen;
                bankScreen.handleTransferResult(resultCode, message);
            } else {
                ErrorLogger.getInstance().logError(
                    "Transfer result received but bank app screen not active",
                    null,
                    "Money",
                    ErrorLogger.ErrorSeverity.WARNING
                );
            }
        } catch (Exception e) {
            String errorMsg = "Error handling transfer result: " + resultCode + " - " + message;
            Pokecobbleclaim.LOGGER.error("Error handling transfer result: " + e.getMessage());
            ErrorLogger.getInstance().logError(
                errorMsg,
                e,
                "Money",
                ErrorLogger.ErrorSeverity.ERROR
            );
        }
    }
}
