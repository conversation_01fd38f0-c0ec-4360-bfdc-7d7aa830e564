package com.pokecobble.town.network.security;

import com.pokecobble.Pokecobbleclaim;
import net.minecraft.network.PacketByteBuf;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.client.network.ClientPlayerEntity;
import net.minecraft.server.network.ServerPlayerEntity;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Base64;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Handles authentication of network packets to prevent spoofing and tampering.
 * Uses HMAC-SHA256 for message authentication.
 */
public class PacketAuthenticator {
    private static final String HMAC_ALGORITHM = "HmacSHA256";
    private static final int TOKEN_LENGTH = 16; // 128 bits
    private static final long TOKEN_EXPIRY_MS = 30 * 60 * 1000; // 30 minutes

    // Map of player UUIDs to their session tokens
    private static final Map<UUID, SessionToken> playerTokens = new ConcurrentHashMap<>();

    // Secure random for token generation
    private static final SecureRandom secureRandom = new SecureRandom();

    /**
     * Generates a new session token for a player.
     *
     * @param playerId The UUID of the player
     * @return The generated token
     */
    public static byte[] generateToken(UUID playerId) {
        byte[] token = new byte[TOKEN_LENGTH];
        secureRandom.nextBytes(token);

        // Store token with creation time
        playerTokens.put(playerId, new SessionToken(token, System.currentTimeMillis()));

        return token;
    }

    /**
     * Adds authentication data to a packet.
     *
     * @param buf The packet buffer
     * @param playerId The UUID of the player
     * @return True if authentication data was added, false otherwise
     */
    public static boolean authenticatePacket(PacketByteBuf buf, UUID playerId) {
        try {
            // Get player's token
            SessionToken sessionToken = playerTokens.get(playerId);
            if (sessionToken == null) {
                Pokecobbleclaim.LOGGER.warn("No token found for player " + playerId);
                return false;
            }

            // Check if token has expired
            if (System.currentTimeMillis() - sessionToken.creationTime > TOKEN_EXPIRY_MS) {
                Pokecobbleclaim.LOGGER.warn("Token expired for player " + playerId);
                playerTokens.remove(playerId);
                return false;
            }

            // Create HMAC
            Mac hmac = Mac.getInstance(HMAC_ALGORITHM);
            SecretKeySpec keySpec = new SecretKeySpec(sessionToken.token, HMAC_ALGORITHM);
            hmac.init(keySpec);

            // Get current position
            int readerIndex = buf.readerIndex();
            int writerIndex = buf.writerIndex();

            // Compute HMAC of packet data
            byte[] data = new byte[writerIndex - readerIndex];
            buf.getBytes(readerIndex, data);
            byte[] signature = hmac.doFinal(data);

            // Write signature to packet
            buf.writeBytes(signature);

            return true;
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            Pokecobbleclaim.LOGGER.error("Error authenticating packet: " + e.getMessage());
            return false;
        }
    }

    /**
     * Verifies the authentication of a packet.
     *
     * @param buf The packet buffer
     * @param player The player who sent the packet
     * @return True if the packet is authentic, false otherwise
     */
    public static boolean verifyPacket(PacketByteBuf buf, ServerPlayerEntity player) {
        try {
            UUID playerId = player.getUuid();

            // Get player's token
            SessionToken sessionToken = playerTokens.get(playerId);
            if (sessionToken == null) {
                Pokecobbleclaim.LOGGER.warn("No token found for player " + playerId);
                return false;
            }

            // Check if token has expired
            if (System.currentTimeMillis() - sessionToken.creationTime > TOKEN_EXPIRY_MS) {
                Pokecobbleclaim.LOGGER.warn("Token expired for player " + playerId);
                playerTokens.remove(playerId);
                return false;
            }

            // Create HMAC
            Mac hmac = Mac.getInstance(HMAC_ALGORITHM);
            SecretKeySpec keySpec = new SecretKeySpec(sessionToken.token, HMAC_ALGORITHM);
            hmac.init(keySpec);

            // Get signature from packet
            int signatureLength = hmac.getMacLength();
            int dataLength = buf.writerIndex() - buf.readerIndex() - signatureLength;

            if (dataLength < 0) {
                Pokecobbleclaim.LOGGER.warn("Packet too small to contain signature");
                return false;
            }

            byte[] data = new byte[dataLength];
            byte[] signature = new byte[signatureLength];

            buf.getBytes(buf.readerIndex(), data);
            buf.getBytes(buf.readerIndex() + dataLength, signature);

            // Compute expected signature
            byte[] expectedSignature = hmac.doFinal(data);

            // Compare signatures
            return Arrays.equals(signature, expectedSignature);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            Pokecobbleclaim.LOGGER.error("Error verifying packet: " + e.getMessage());
            return false;
        }
    }

    /**
     * Verifies the authentication of a packet on the client side.
     * This is a simplified version that doesn't require token validation.
     *
     * @param buf The packet buffer
     * @param player The client player entity
     * @return True if the packet is authentic, false otherwise
     */
    @Environment(EnvType.CLIENT)
    public static boolean verifyPacket(PacketByteBuf buf, ClientPlayerEntity player) {
        // On the client side, we don't have access to the server's tokens
        // So we just check if the packet has a valid structure
        try {
            // Skip verification in singleplayer mode
            if (player.getWorld().isClient()) {
                return true;
            }

            // In multiplayer, we do a basic structure check
            int readerIndex = buf.readerIndex();
            int writerIndex = buf.writerIndex();

            // Check if the packet is large enough to contain a signature
            if (writerIndex - readerIndex < 32) { // HMAC-SHA256 produces 32 bytes
                Pokecobbleclaim.LOGGER.warn("Packet too small to contain signature");
                return false;
            }

            return true;
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error verifying packet on client: " + e.getMessage());
            return false;
        }
    }

    /**
     * Removes a player's token.
     *
     * @param playerId The UUID of the player
     */
    public static void removeToken(UUID playerId) {
        playerTokens.remove(playerId);
    }

    /**
     * Clears all tokens.
     */
    public static void clearTokens() {
        playerTokens.clear();
    }

    /**
     * Represents a session token with its creation time.
     */
    private static class SessionToken {
        private final byte[] token;
        private final long creationTime;

        public SessionToken(byte[] token, long creationTime) {
            this.token = token;
            this.creationTime = creationTime;
        }
    }
}
