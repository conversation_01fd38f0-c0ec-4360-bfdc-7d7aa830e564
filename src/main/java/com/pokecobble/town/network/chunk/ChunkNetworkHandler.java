package com.pokecobble.town.network.chunk;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.claim.ChunkPermissionManager;
import com.pokecobble.town.claim.ClaimTag;
import com.pokecobble.town.gui.NotificationManager;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;
import com.pokecobble.town.network.PacketValidator;
import com.pokecobble.town.network.chunk.ChunkDataSynchronizer;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketSender;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayNetworkHandler;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;
import net.minecraft.util.math.ChunkPos;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Handles network communication for chunk-related operations.
 */
public class ChunkNetworkHandler {

    /**
     * Registers client-side packet handlers.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Register chunk data response handler
        ClientPlayNetworking.registerGlobalReceiver(
                NetworkConstants.CHUNK_DATA_RESPONSE,
                ChunkNetworkHandler::handleChunkDataResponse
        );

        // Register chunk claim response handler
        ClientPlayNetworking.registerGlobalReceiver(
                NetworkConstants.CHUNK_CLAIM_RESPONSE,
                ChunkNetworkHandler::handleChunkClaimResponse
        );
    }

    /**
     * Registers server-side packet handlers.
     */
    public static void registerServerHandlers() {
        // Register chunk data request handler
        ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.CHUNK_DATA_REQUEST,
                ChunkNetworkHandler::handleChunkDataRequest
        );

        // Register chunk claim request handler
        ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.CHUNK_CLAIM_REQUEST,
                ChunkNetworkHandler::handleChunkClaimRequest
        );
    }

    // Client-side methods

    /**
     * Requests chunk data from the server.
     *
     * @param chunkPos The chunk position to request data for
     */
    @Environment(EnvType.CLIENT)
    public static void requestChunkData(ChunkPos chunkPos) {
        try {
            // Validate chunk position
            PacketValidator.validateChunkPos(chunkPos);

            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Add player UUID for validation
            if (MinecraftClient.getInstance().player != null) {
                buf.writeUuid(MinecraftClient.getInstance().player.getUuid());
            } else {
                return; // Can't send without a player
            }

            // Add chunk position
            buf.writeInt(chunkPos.x);
            buf.writeInt(chunkPos.z);

            // Send packet to server
            NetworkManager.sendToServer(NetworkConstants.CHUNK_DATA_REQUEST, buf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting chunk data: " + e.getMessage());
        }
    }

    /**
     * Requests to claim chunks for a town.
     *
     * @param townId The UUID of the town to claim chunks for
     * @param chunks The chunks to claim
     * @param tags The tags to apply to each chunk
     */
    @Environment(EnvType.CLIENT)
    public static void requestChunkClaim(UUID townId, List<ChunkPos> chunks, Map<ChunkPos, ClaimTag> tags) {
        try {
            // Validate parameters
            PacketValidator.validateUUID(townId);
            PacketValidator.validateListSize(chunks.size());

            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Add player UUID for validation
            if (MinecraftClient.getInstance().player != null) {
                buf.writeUuid(MinecraftClient.getInstance().player.getUuid());
            } else {
                return; // Can't send without a player
            }

            // Add town UUID
            buf.writeUuid(townId);

            // Add chunk count
            buf.writeInt(chunks.size());

            // Add chunks and tags
            for (ChunkPos chunk : chunks) {
                // Validate chunk position
                PacketValidator.validateChunkPos(chunk);

                // Add chunk position
                buf.writeInt(chunk.x);
                buf.writeInt(chunk.z);

                // Add tag if available
                ClaimTag tag = tags.get(chunk);

                if (tag != null) {
                    // Has tag
                    buf.writeBoolean(true);

                    // Add tag name
                    buf.writeString(tag.getName());

                    // Add tag color
                    buf.writeInt(tag.getColor());

                    // Add tag permissions
                    for (TownPlayerRank rank : TownPlayerRank.values()) {
                        boolean[] permissions = tag.getPermissionsForRank(rank);

                        for (boolean permission : permissions) {
                            buf.writeBoolean(permission);
                        }
                    }

                    // Add non-member permissions
                    boolean[] nonMemberPermissions = tag.getPermissionsForRank(null);

                    for (boolean permission : nonMemberPermissions) {
                        buf.writeBoolean(permission);
                    }
                } else {
                    // No tag
                    buf.writeBoolean(false);
                }
            }

            // Send packet to server
            NetworkManager.sendToServer(NetworkConstants.CHUNK_CLAIM_REQUEST, buf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting chunk claim: " + e.getMessage());
        }
    }

    // Client-side packet handlers

    /**
     * Handles chunk data response packets.
     */
    @Environment(EnvType.CLIENT)
    private static void handleChunkDataResponse(MinecraftClient client, ClientPlayNetworkHandler handler,
                                              PacketByteBuf buf, PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read chunk position
            int chunkX = buf.readInt();
            int chunkZ = buf.readInt();
            ChunkPos chunkPos = new ChunkPos(chunkX, chunkZ);

            // Read if the chunk is claimed
            boolean isClaimed = buf.readBoolean();

            if (isClaimed) {
                // Read town UUID
                UUID townId = buf.readUuid();

                // Read town name
                String townName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

                // Read if the chunk has a tag
                boolean hasTag = buf.readBoolean();

                if (hasTag) {
                    // Read tag name
                    String tagName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

                    // Read tag color
                    int tagColor = buf.readInt();

                    // Create tag
                    ClaimTag tag = new ClaimTag(tagName, tagColor);

                    // Read tag permissions
                    for (TownPlayerRank rank : TownPlayerRank.values()) {
                        boolean[] permissions = new boolean[8];

                        for (int i = 0; i < permissions.length; i++) {
                            permissions[i] = buf.readBoolean();
                        }

                        // Set permissions for rank
                        for (int i = 0; i < permissions.length; i++) {
                            tag.getRankPermissions().setPermission(rank, i, permissions[i]);
                        }
                    }

                    // Read non-member permissions
                    boolean[] nonMemberPermissions = new boolean[8];

                    for (int i = 0; i < nonMemberPermissions.length; i++) {
                        nonMemberPermissions[i] = buf.readBoolean();
                    }

                    // Set permissions for non-members
                    for (int i = 0; i < nonMemberPermissions.length; i++) {
                        tag.getRankPermissions().setPermission(null, i, nonMemberPermissions[i]);
                    }

                    // Get or create town
                    Town town = TownManager.getInstance().getTownById(townId);

                    if (town == null) {
                        town = new Town(townId, townName);
                        TownManager.getInstance().addTown(town);
                    }

                    // Update chunk data in ChunkPermissionManager
                    ChunkPermissionManager.getInstance().setChunkTag(chunkPos, town, tag);

                    // Update claim tool if active
                    if (com.pokecobble.town.claim.ClaimTool.getInstance().isActive()) {
                        com.pokecobble.town.claim.ClaimTool.getInstance().updateChunkData(chunkPos, town, tag);
                    }
                } else {
                    // Get or create town
                    Town town = TownManager.getInstance().getTownById(townId);

                    if (town == null) {
                        town = new Town(townId, townName);
                        TownManager.getInstance().addTown(town);
                    }

                    // Update chunk data in ChunkPermissionManager without a tag
                    ChunkPermissionManager.getInstance().setChunkTag(chunkPos, town, null);

                    // Update claim tool if active
                    if (com.pokecobble.town.claim.ClaimTool.getInstance().isActive()) {
                        com.pokecobble.town.claim.ClaimTool.getInstance().updateChunkData(chunkPos, town, null);
                    }
                }
            } else {
                // Chunk is not claimed, remove from ChunkPermissionManager
                ChunkPermissionManager.getInstance().removeChunkClaim(chunkPos);

                // Update claim tool if active
                if (com.pokecobble.town.claim.ClaimTool.getInstance().isActive()) {
                    com.pokecobble.town.claim.ClaimTool.getInstance().updateChunkData(chunkPos, null, null);
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling chunk data response: " + e.getMessage());
        }
    }

    /**
     * Handles chunk claim response packets.
     */
    @Environment(EnvType.CLIENT)
    private static void handleChunkClaimResponse(MinecraftClient client, ClientPlayNetworkHandler handler,
                                               PacketByteBuf buf, PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read town UUID
            UUID townId = buf.readUuid();

            // Read success
            boolean success = buf.readBoolean();

            // Read chunk count
            int chunkCount = buf.readInt();
            PacketValidator.validateListSize(chunkCount);

            if (success) {
                // Read chunks
                List<ChunkPos> chunks = new ArrayList<>();
                Map<ChunkPos, ClaimTag> tags = new HashMap<>();

                for (int i = 0; i < chunkCount; i++) {
                    int chunkX = buf.readInt();
                    int chunkZ = buf.readInt();
                    ChunkPos chunkPos = new ChunkPos(chunkX, chunkZ);
                    chunks.add(chunkPos);

                    // Read tag data if available
                    boolean hasTag = buf.readBoolean();
                    if (hasTag) {
                        String tagName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                        int tagColor = buf.readInt();

                        // Create tag
                        ClaimTag tag = new ClaimTag(tagName, tagColor);
                        tags.put(chunkPos, tag);
                    }
                }

                // Get town
                Town town = TownManager.getInstance().getTownById(townId);

                // Update local chunk data
                if (town != null) {
                    // Update the claim tool if active
                    com.pokecobble.town.claim.ClaimTool claimTool = com.pokecobble.town.claim.ClaimTool.getInstance();

                    for (ChunkPos chunkPos : chunks) {
                        ClaimTag tag = tags.get(chunkPos);

                        // Update the chunk data in the claim tool
                        claimTool.updateChunkData(chunkPos, town, tag);

                        // Update the chunk permission manager
                        ChunkPermissionManager.getInstance().setChunkTag(chunkPos, town, tag);
                    }

                    // Only show notification if this is the player's town and they initiated the claim
                    if (client.player != null && town.getPlayers().contains(client.player.getUuid())) {
                        // Notify the player
                        NotificationManager.getInstance().addSuccessNotification("Successfully claimed " + chunkCount + " chunks for " + town.getName());
                    }
                } else {
                    // This is a notification about another town's claim
                    // Just update the local data without showing a notification
                    com.pokecobble.town.claim.ClaimTool claimTool = com.pokecobble.town.claim.ClaimTool.getInstance();

                    for (ChunkPos chunkPos : chunks) {
                        // Mark the chunk as claimed by an unknown town
                        claimTool.setChunkClaimed(chunkPos, "Unknown Town");
                    }
                }
            } else {
                // Read error message
                String errorMessage = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

                // Notify the player
                NotificationManager.getInstance().addErrorNotification("Failed to claim chunks: " + errorMessage);
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling chunk claim response: " + e.getMessage());
        }
    }

    // Server-side packet handlers

    /**
     * Handles chunk data request packets.
     */
    private static void handleChunkDataRequest(MinecraftServer server, ServerPlayerEntity player,
                                             ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                             PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read player UUID for validation
            UUID requestingPlayerId = buf.readUuid();

            // Validate that the requesting player is the same as the sender
            if (!player.getUuid().equals(requestingPlayerId)) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " tried to request chunk data for another player");
                return;
            }

            // Read chunk position
            int chunkX = buf.readInt();
            int chunkZ = buf.readInt();
            ChunkPos chunkPos = new ChunkPos(chunkX, chunkZ);

            // Get chunk data
            Town town = ChunkPermissionManager.getInstance().getTownForChunk(chunkPos);
            ClaimTag tag = ChunkPermissionManager.getInstance().getTagForChunk(chunkPos);

            // Create response packet
            PacketByteBuf responseBuf = NetworkManager.createPacket();

            // Write chunk position
            responseBuf.writeInt(chunkX);
            responseBuf.writeInt(chunkZ);

            if (town != null) {
                // Chunk is claimed
                responseBuf.writeBoolean(true);

                // Write town UUID
                responseBuf.writeUuid(town.getId());

                // Write town name
                responseBuf.writeString(town.getName());

                if (tag != null) {
                    // Chunk has a tag
                    responseBuf.writeBoolean(true);

                    // Write tag name
                    responseBuf.writeString(tag.getName());

                    // Write tag color
                    responseBuf.writeInt(tag.getColor());

                    // Write tag permissions
                    for (TownPlayerRank rank : TownPlayerRank.values()) {
                        boolean[] permissions = tag.getPermissionsForRank(rank);

                        for (boolean permission : permissions) {
                            responseBuf.writeBoolean(permission);
                        }
                    }

                    // Write non-member permissions
                    boolean[] nonMemberPermissions = tag.getPermissionsForRank(null);

                    for (boolean permission : nonMemberPermissions) {
                        responseBuf.writeBoolean(permission);
                    }
                } else {
                    // Chunk doesn't have a tag
                    responseBuf.writeBoolean(false);
                }
            } else {
                // Chunk is not claimed
                responseBuf.writeBoolean(false);
            }

            // Send response to player
            NetworkManager.sendToPlayer(player, NetworkConstants.CHUNK_DATA_RESPONSE, responseBuf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling chunk data request: " + e.getMessage());
        }
    }

    /**
     * Handles chunk claim request packets.
     */
    private static void handleChunkClaimRequest(MinecraftServer server, ServerPlayerEntity player,
                                              ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                              PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read player UUID for validation
            UUID requestingPlayerId = buf.readUuid();

            // Validate that the requesting player is the same as the sender
            if (!player.getUuid().equals(requestingPlayerId)) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " tried to claim chunks for another player");
                return;
            }

            // Read town UUID
            UUID townId = buf.readUuid();

            // Read chunk count
            int chunkCount = buf.readInt();
            PacketValidator.validateListSize(chunkCount);

            // Read chunks and tags
            List<ChunkPos> chunks = new ArrayList<>();
            Map<ChunkPos, ClaimTag> tags = new HashMap<>();

            for (int i = 0; i < chunkCount; i++) {
                // Read chunk position
                int chunkX = buf.readInt();
                int chunkZ = buf.readInt();
                ChunkPos chunkPos = new ChunkPos(chunkX, chunkZ);

                chunks.add(chunkPos);

                // Read if the chunk has a tag
                boolean hasTag = buf.readBoolean();

                if (hasTag) {
                    // Read tag name
                    String tagName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

                    // Read tag color
                    int tagColor = buf.readInt();

                    // Create tag
                    ClaimTag tag = new ClaimTag(tagName, tagColor);

                    // Read tag permissions
                    for (TownPlayerRank rank : TownPlayerRank.values()) {
                        boolean[] permissions = new boolean[8];

                        for (int j = 0; j < permissions.length; j++) {
                            permissions[j] = buf.readBoolean();
                        }

                        // Set permissions for rank
                        for (int j = 0; j < permissions.length; j++) {
                            tag.getRankPermissions().setPermission(rank, j, permissions[j]);
                        }
                    }

                    // Read non-member permissions
                    boolean[] nonMemberPermissions = new boolean[8];

                    for (int j = 0; j < nonMemberPermissions.length; j++) {
                        nonMemberPermissions[j] = buf.readBoolean();
                    }

                    // Set permissions for non-members
                    for (int j = 0; j < nonMemberPermissions.length; j++) {
                        tag.getRankPermissions().setPermission(null, j, nonMemberPermissions[j]);
                    }

                    tags.put(chunkPos, tag);
                }
            }

            // Get the town
            Town town = TownManager.getInstance().getTownById(townId);

            if (town == null) {
                // Town not found
                sendChunkClaimResponse(player, townId, false, 0, "Town not found");
                return;
            }

            // Check if the player is in the town
            if (!town.getPlayers().contains(player.getUuid())) {
                // Player is not in the town
                sendChunkClaimResponse(player, townId, false, 0, "You are not in this town");
                return;
            }

            // Check if the player has permission to claim chunks
            TownPlayer townPlayer = town.getPlayer(player.getUuid());

            if (townPlayer == null) {
                // Player not found in town
                sendChunkClaimResponse(player, townId, false, 0, "You are not in this town");
                return;
            }

            // Check if the player is the owner or has permission to claim chunks
            boolean canClaimChunks = townPlayer.getRank() == TownPlayerRank.OWNER ||
                                    townPlayer.hasPermission("Claims", "Can claim chunks");

            if (!canClaimChunks) {
                // Player doesn't have permission
                sendChunkClaimResponse(player, townId, false, 0, "You don't have permission to claim chunks");
                return;
            }

            // Check if the town has enough claim slots
            int availableClaims = town.getMaxClaims() - town.getClaimCount();

            if (availableClaims < chunks.size()) {
                // Not enough claim slots
                sendChunkClaimResponse(player, townId, false, 0, "Not enough claim slots available");
                return;
            }

            // Check if any of the chunks are already claimed
            for (ChunkPos chunk : chunks) {
                Town chunkTown = ChunkPermissionManager.getInstance().getTownForChunk(chunk);

                if (chunkTown != null && !chunkTown.getId().equals(townId)) {
                    // Chunk is already claimed by another town
                    sendChunkClaimResponse(player, townId, false, 0, "Some chunks are already claimed by another town");
                    return;
                }
            }

            // Claim the chunks
            for (ChunkPos chunk : chunks) {
                ClaimTag tag = tags.get(chunk);
                ChunkPermissionManager.getInstance().setChunkTag(chunk, town, tag);

                // Record claim action in history
                town.recordClaimAction(
                    com.pokecobble.town.claim.ClaimHistoryEntry.ActionType.CLAIM,
                    chunk,
                    player.getUuid(),
                    player.getName().getString(),
                    tag
                );
            }

            // Update the town's claim count
            town.setClaimCount(town.getClaimCount() + chunks.size());

            // Save the town
            TownManager.getInstance().saveTown(town);

            // Use the ChunkDataSynchronizer to efficiently sync the chunks to all players
            ChunkDataSynchronizer.syncChunks(server, chunks, town, tags);

            // Sync claim history to all town players
            com.pokecobble.town.network.town.ClaimHistorySynchronizer.syncClaimHistory(server, town);

            // Send response to player for immediate feedback
            sendChunkClaimResponse(player, townId, true, chunks.size(), chunks, tags);

            // Notify all online players in the town with a message
            for (UUID playerId : town.getPlayers()) {
                if (!playerId.equals(player.getUuid())) {
                    ServerPlayerEntity playerInTown = server.getPlayerManager().getPlayer(playerId);
                    if (playerInTown != null) {
                        playerInTown.sendMessage(Text.literal(player.getName().getString() + " claimed " + chunks.size() + " chunks for your town").formatted(Formatting.GOLD), false);
                    }
                }
            }

            // Log the claim
            Pokecobbleclaim.LOGGER.info("Player " + player.getName().getString() + " claimed " + chunks.size() + " chunks for town " + town.getName());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling chunk claim request: " + e.getMessage());

            // Send error response to player
            sendChunkClaimResponse(player, null, false, 0, "An error occurred: " + e.getMessage());
        }
    }

    /**
     * Sends a chunk claim response to a player.
     */
    private static void sendChunkClaimResponse(ServerPlayerEntity player, UUID townId, boolean success, int chunkCount, String errorMessage) {
        sendChunkClaimResponse(player, townId, success, chunkCount, null, null, errorMessage);
    }

    /**
     * Sends a chunk claim response to a player with actual chunk data.
     */
    private static void sendChunkClaimResponse(ServerPlayerEntity player, UUID townId, boolean success, int chunkCount,
                                             List<ChunkPos> chunks, Map<ChunkPos, ClaimTag> tags) {
        sendChunkClaimResponse(player, townId, success, chunkCount, chunks, tags, null);
    }

    /**
     * Sends a chunk claim response to a player with all parameters.
     */
    private static void sendChunkClaimResponse(ServerPlayerEntity player, UUID townId, boolean success, int chunkCount,
                                             List<ChunkPos> chunks, Map<ChunkPos, ClaimTag> tags, String errorMessage) {
        try {
            // Create response packet
            PacketByteBuf responseBuf = NetworkManager.createPacket();

            // Write town UUID
            if (townId != null) {
                responseBuf.writeUuid(townId);
            } else {
                responseBuf.writeUuid(new UUID(0, 0)); // Empty UUID
            }

            // Write success
            responseBuf.writeBoolean(success);

            if (success) {
                // Write chunk count
                responseBuf.writeInt(chunkCount);

                // Write actual chunks if available
                if (chunks != null && tags != null) {
                    for (ChunkPos chunk : chunks) {
                        // Write chunk position
                        responseBuf.writeInt(chunk.x);
                        responseBuf.writeInt(chunk.z);

                        // Write tag data if available
                        ClaimTag tag = tags.get(chunk);
                        if (tag != null) {
                            responseBuf.writeBoolean(true); // Has tag
                            responseBuf.writeString(tag.getName());
                            responseBuf.writeInt(tag.getColor());
                        } else {
                            responseBuf.writeBoolean(false); // No tag
                        }
                    }
                } else {
                    // Write placeholder chunks if no actual chunks are provided
                    for (int i = 0; i < chunkCount; i++) {
                        responseBuf.writeInt(0); // Placeholder chunk X
                        responseBuf.writeInt(0); // Placeholder chunk Z
                        responseBuf.writeBoolean(false); // No tag
                    }
                }
            } else {
                // Write chunk count (0 for failure)
                responseBuf.writeInt(0);

                // Write error message
                responseBuf.writeString(errorMessage != null ? errorMessage : "Unknown error");
            }

            // Send response to player
            NetworkManager.sendToPlayer(player, NetworkConstants.CHUNK_CLAIM_RESPONSE, responseBuf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending chunk claim response: " + e.getMessage());
        }
    }
}
