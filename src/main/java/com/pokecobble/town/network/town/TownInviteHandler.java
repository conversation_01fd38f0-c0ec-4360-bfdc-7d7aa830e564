package com.pokecobble.town.network.town;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.client.InviteNotification;
import com.pokecobble.phone.notification.PhoneNotificationManager;
import com.pokecobble.town.network.NetworkManager;
import com.pokecobble.town.network.PacketValidator;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.PacketSender;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayNetworkHandler;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;
import net.minecraft.util.Identifier;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Handles network communication for town invitations and responses.
 */
public class TownInviteHandler {

    // Callback interface for invite updates
    public interface InviteUpdateCallback {
        void onInviteUpdate(UUID townId, String townName, boolean isInvite);
    }

    // List of registered callbacks
    private static final List<InviteUpdateCallback> inviteUpdateCallbacks = new ArrayList<>();

    /**
     * Registers a callback to be notified when invite status is updated.
     *
     * @param callback The callback to register
     */
    public static void registerInviteUpdateCallback(InviteUpdateCallback callback) {
        inviteUpdateCallbacks.add(callback);
    }

    /**
     * Unregisters a previously registered callback.
     *
     * @param callback The callback to unregister
     */
    public static void unregisterInviteUpdateCallback(InviteUpdateCallback callback) {
        inviteUpdateCallbacks.remove(callback);
    }

    /**
     * Notifies all registered callbacks that an invite has been received or processed.
     *
     * @param townId The ID of the town
     * @param townName The name of the town
     * @param isInvite True if this is a new invite, false if the invite was processed
     */
    public static void notifyInviteUpdated(UUID townId, String townName, boolean isInvite) {
        for (InviteUpdateCallback callback : inviteUpdateCallbacks) {
            callback.onInviteUpdate(townId, townName, isInvite);
        }
    }
    // Packet identifiers
    public static final Identifier TOWN_INVITE = new Identifier(Pokecobbleclaim.MOD_ID, "town_invite");
    public static final Identifier JOIN_TOWN = new Identifier(Pokecobbleclaim.MOD_ID, "join_town");
    public static final Identifier DECLINE_INVITE = new Identifier(Pokecobbleclaim.MOD_ID, "decline_invite");

    /**
     * Registers client-side packet handlers.
     */
    public static void registerClientHandlers() {
        // Register town invite handler
        ClientPlayNetworking.registerGlobalReceiver(TOWN_INVITE, TownInviteHandler::handleTownInvite);
    }

    /**
     * Registers server-side packet handlers.
     */
    public static void registerServerHandlers() {
        // Register join town handler
        ServerPlayNetworking.registerGlobalReceiver(JOIN_TOWN, TownInviteHandler::handleJoinTown);

        // Register decline invite handler
        ServerPlayNetworking.registerGlobalReceiver(DECLINE_INVITE, TownInviteHandler::handleDeclineInvite);
    }

    /**
     * Sends a town invitation to a player.
     *
     * @param server The server instance
     * @param player The player to send the invitation to
     * @param town The town sending the invitation
     */
    public static void sendTownInvite(MinecraftServer server, ServerPlayerEntity player, Town town) {
        try {
            Pokecobbleclaim.LOGGER.info("Preparing to send town invitation to " + player.getName().getString() + " from town " + town.getName());

            // Create packet buffer
            PacketByteBuf buf = PacketByteBufs.create();

            // Write town ID
            buf.writeUuid(town.getId());
            Pokecobbleclaim.LOGGER.info("Town ID: " + town.getId());

            // Write town name
            buf.writeString(town.getName());
            Pokecobbleclaim.LOGGER.info("Town name: " + town.getName());

            // Send packet to player
            ServerPlayNetworking.send(player, TOWN_INVITE, buf);

            // Also send a chat message as a backup
            player.sendMessage(Text.literal("You have been invited to join " + town.getName() + "!").formatted(Formatting.GOLD), false);

            // Log the invitation
            Pokecobbleclaim.LOGGER.info("Sent town invitation to " + player.getName().getString() + " from town " + town.getName());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending town invitation: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Handles town invite packets.
     */
    @Environment(EnvType.CLIENT)
    private static void handleTownInvite(MinecraftClient client, ClientPlayNetworkHandler handler,
                                       PacketByteBuf buf, PacketSender sender) {
        try {
            // Log that we received the packet
            Pokecobbleclaim.LOGGER.info("Received town invite packet");

            // Read town ID
            UUID townId = buf.readUuid();

            // Read town name
            String townName = buf.readString();

            // Check if this is an invite or a response (default to invite if not specified)
            final boolean isInvite = buf.isReadable() ? buf.readBoolean() : true;

            Pokecobbleclaim.LOGGER.info("Received invitation from town: " + townName + " (" + townId + ")");

            // Handle based on whether this is an invite or a response
            client.execute(() -> {
                if (isInvite) {
                    // This is a new invitation
                    Pokecobbleclaim.LOGGER.info("Setting pending invite for town: " + townName);
                    InviteNotification.setPendingInvite(townId, townName);

                    // Show phone notification instead of old notification
                    PhoneNotificationManager.getInstance().addTownInviteNotification(townName, townId.toString());

                    // Also send a chat message as a backup
                    if (client.player != null) {
                        client.player.sendMessage(Text.literal("You have been invited to join " + townName + "!").formatted(Formatting.GOLD), false);
                    }
                } else {
                    // This is a response to an invitation (join or decline)
                    Pokecobbleclaim.LOGGER.info("Clearing pending invite for town: " + townName);
                    InviteNotification.clearPendingInvite();
                }

                // Notify callbacks in either case
                notifyInviteUpdated(townId, townName, isInvite);
            });
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town invite: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Handles join town packets.
     */
    private static void handleJoinTown(MinecraftServer server, ServerPlayerEntity player,
                                     ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                     PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read town ID
            UUID townId = buf.readUuid();

            // Get the town
            Town town = TownManager.getInstance().getTown(townId);
            if (town == null) {
                player.sendMessage(Text.literal("Town not found").formatted(Formatting.RED), false);
                return;
            }

            // Check if the player is already in a town
            if (TownManager.getInstance().getPlayerTown(player.getUuid()) != null) {
                player.sendMessage(Text.literal("You are already in a town").formatted(Formatting.RED), false);
                return;
            }

            // Check if the town is full
            if (town.getPlayerCount() >= town.getMaxPlayers()) {
                player.sendMessage(Text.literal("Town is full").formatted(Formatting.RED), false);
                return;
            }

            // Add the player to the town
            boolean added = TownManager.getInstance().addPlayerToTown(player.getUuid(), townId);
            if (!added) {
                player.sendMessage(Text.literal("Failed to join town").formatted(Formatting.RED), false);
                return;
            }

            // Send success message to the player
            player.sendMessage(Text.literal("You have joined " + town.getName()).formatted(Formatting.GREEN), false);

            // Notify all players in the town
            for (UUID playerId : town.getPlayers()) {
                if (!playerId.equals(player.getUuid())) { // Skip the joining player
                    ServerPlayerEntity townPlayer = server.getPlayerManager().getPlayer(playerId);
                    if (townPlayer != null) {
                        townPlayer.sendMessage(Text.literal(player.getName().getString() + " has joined the town").formatted(Formatting.GREEN), false);
                    }
                }
            }

            // Notify callbacks on the client side
            // This is done by sending a special packet to the client
            PacketByteBuf responseBuf = PacketByteBufs.create();
            responseBuf.writeUuid(townId);
            responseBuf.writeString(town.getName());
            responseBuf.writeBoolean(false); // Not an invite, but a join
            ServerPlayNetworking.send(player, TOWN_INVITE, responseBuf); // Reuse the invite packet for simplicity

            // Log the join
            Pokecobbleclaim.LOGGER.info("Player " + player.getName().getString() + " joined town " + town.getName());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling join town: " + e.getMessage());
            player.sendMessage(Text.literal("Error joining town: " + e.getMessage()).formatted(Formatting.RED), false);
        }
    }

    /**
     * Handles decline invite packets.
     */
    private static void handleDeclineInvite(MinecraftServer server, ServerPlayerEntity player,
                                          ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                          PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read town ID
            UUID townId = buf.readUuid();

            // Get the town
            Town town = TownManager.getInstance().getTown(townId);
            if (town == null) {
                return; // Town not found, nothing to do
            }

            // Log the decline
            Pokecobbleclaim.LOGGER.info("Player " + player.getName().getString() + " declined invitation from town " + town.getName());

            // Notify the town mayor
            UUID mayorId = town.getPlayers().isEmpty() ? null : town.getPlayers().get(0);
            if (mayorId != null) {
                ServerPlayerEntity mayor = server.getPlayerManager().getPlayer(mayorId);
                if (mayor != null) {
                    mayor.sendMessage(Text.literal(player.getName().getString() + " declined your town invitation").formatted(Formatting.YELLOW), false);
                }
            }

            // Notify callbacks on the client side
            // This is done by sending a special packet to the client
            PacketByteBuf responseBuf = PacketByteBufs.create();
            responseBuf.writeUuid(townId);
            responseBuf.writeString(town.getName());
            responseBuf.writeBoolean(false); // Not an invite, but a decline
            ServerPlayNetworking.send(player, TOWN_INVITE, responseBuf); // Reuse the invite packet for simplicity
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling decline invite: " + e.getMessage());
        }
    }
}
