package com.pokecobble.town.network.town;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.gui.NotificationManager;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;
import com.pokecobble.town.network.PacketValidator;
import com.pokecobble.town.network.town.TownInviteHandler;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.PacketSender;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayNetworkHandler;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;
import net.minecraft.util.Identifier;

import java.util.UUID;

/**
 * Handles network communication for town invitations.
 */
public class InviteNetworkHandler {
    // Packet identifiers
    public static final Identifier INVITE_PLAYER = new Identifier(Pokecobbleclaim.MOD_ID, "invite_player");
    public static final Identifier INVITE_RESPONSE = new Identifier(Pokecobbleclaim.MOD_ID, "invite_response");

    /**
     * Registers client-side packet handlers.
     */
    public static void registerClientHandlers() {
        // Register invite response handler
        ClientPlayNetworking.registerGlobalReceiver(INVITE_RESPONSE, InviteNetworkHandler::handleInviteResponse);
    }

    /**
     * Registers server-side packet handlers.
     */
    public static void registerServerHandlers() {
        // Register invite player handler
        ServerPlayNetworking.registerGlobalReceiver(INVITE_PLAYER, InviteNetworkHandler::handleInvitePlayer);
    }

    /**
     * Sends an invitation to a player.
     *
     * @param townId The ID of the town
     * @param playerId The ID of the player to invite
     */
    @Environment(EnvType.CLIENT)
    public static void invitePlayer(UUID townId, UUID playerId) {
        try {
            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Add player UUID for validation
            if (MinecraftClient.getInstance().player != null) {
                buf.writeUuid(MinecraftClient.getInstance().player.getUuid());
            } else {
                return; // Can't send without a player
            }

            // Write town ID
            buf.writeUuid(townId);

            // Write player ID
            buf.writeUuid(playerId);

            // Send packet to server
            NetworkManager.sendToServer(INVITE_PLAYER, buf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error inviting player: " + e.getMessage());
        }
    }

    /**
     * Handles invite player packets.
     */
    private static void handleInvitePlayer(MinecraftServer server, ServerPlayerEntity player,
                                          ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                          PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read player UUID for validation
            UUID senderId = buf.readUuid();

            // Validate that the requesting player is the same as the sender
            if (!player.getUuid().equals(senderId)) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " tried to invite a player as someone else");
                return;
            }

            // Read town ID
            UUID townId = buf.readUuid();

            // Read player ID
            UUID targetPlayerId = buf.readUuid();

            // Get the town
            Town town = TownManager.getInstance().getTown(townId);
            if (town == null) {
                sendInviteResponse(player, false, "Unknown Player");
                return;
            }

            // Check if the sender is in the town and has permission to invite
            if (!town.getPlayers().contains(senderId)) {
                sendInviteResponse(player, false, "Unknown Player");
                return;
            }

            // Check if the sender has permission to invite
            boolean canInvite = false;
            if (town.getPlayerRank(senderId).ordinal() <= com.pokecobble.town.TownPlayerRank.ADMIN.ordinal()) {
                canInvite = true;
            } else {
                // Check specific permission
                com.pokecobble.town.TownPlayer townPlayer = town.getPlayer(senderId);
                if (townPlayer != null) {
                    canInvite = townPlayer.hasPermission("Player Management", "Can invite players");
                }
            }

            if (!canInvite) {
                sendInviteResponse(player, false, "Unknown Player");
                return;
            }

            // Check if the target player is online
            ServerPlayerEntity targetPlayer = server.getPlayerManager().getPlayer(targetPlayerId);
            if (targetPlayer == null) {
                sendInviteResponse(player, false, "Unknown Player");
                return;
            }

            // Check if the target player is already in a town
            if (TownManager.getInstance().getPlayerTown(targetPlayerId) != null) {
                sendInviteResponse(player, false, targetPlayer.getName().getString());
                return;
            }

            // Check if the town is full
            if (town.getPlayerCount() >= town.getMaxPlayers()) {
                sendInviteResponse(player, false, targetPlayer.getName().getString());
                return;
            }

            // Check if the town is invite-only or open
            if (town.getJoinType() != Town.JoinType.INVITE_ONLY && town.getJoinType() != Town.JoinType.OPEN) {
                sendInviteResponse(player, false, targetPlayer.getName().getString());
                return;
            }

            // Send invitation to the target player using TownInviteHandler
            TownInviteHandler.sendTownInvite(server, targetPlayer, town);

            // Send response to the sender
            sendInviteResponse(player, true, targetPlayer.getName().getString());

            // Log the invitation
            Pokecobbleclaim.LOGGER.info("Player " + player.getName().getString() + " invited " + targetPlayer.getName().getString() + " to town " + town.getName());
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling invite player: " + e.getMessage());
        }
    }

    /**
     * Sends an invite response to a player.
     *
     * @param player The player to send the response to
     * @param success Whether the invitation was successful
     * @param playerName The name of the invited player
     */
    private static void sendInviteResponse(ServerPlayerEntity player, boolean success, String playerName) {
        try {
            // Create packet buffer
            PacketByteBuf buf = PacketByteBufs.create();

            // Write success flag
            buf.writeBoolean(success);

            // Write player name
            buf.writeString(playerName);

            // Send packet to player
            ServerPlayNetworking.send(player, INVITE_RESPONSE, buf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending invite response: " + e.getMessage());
        }
    }

    /**
     * Handles invite response packets.
     */
    @Environment(EnvType.CLIENT)
    private static void handleInviteResponse(MinecraftClient client, ClientPlayNetworkHandler handler,
                                           PacketByteBuf buf, PacketSender sender) {
        try {
            // Read success flag
            boolean success = buf.readBoolean();

            // Read player name
            String playerName = buf.readString();

            // Show notification
            client.execute(() -> {
                if (success) {
                    NotificationManager.getInstance().addSuccessNotification("Invited " + playerName + " to your town!");
                } else {
                    NotificationManager.getInstance().addErrorNotification("Failed to invite " + playerName);
                }
            });
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling invite response: " + e.getMessage());
        }
    }
}
