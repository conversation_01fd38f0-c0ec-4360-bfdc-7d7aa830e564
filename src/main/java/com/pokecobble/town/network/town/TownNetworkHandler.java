package com.pokecobble.town.network.town;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.gui.NotificationManager;
import com.pokecobble.town.network.town.TownDataSynchronizer;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.NetworkManager;
import com.pokecobble.town.network.PacketValidator;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketSender;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.network.ServerPlayNetworkHandler;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Handles network communication for town-related operations.
 */
public class TownNetworkHandler {

    /**
     * Registers client-side packet handlers.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Register town data response handler
        ClientPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_DATA_RESPONSE,
                TownNetworkHandler::handleTownDataResponse
        );

        // Register town list response handler
        ClientPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_LIST_RESPONSE,
                TownNetworkHandler::handleTownListResponse
        );
    }

    /**
     * Registers server-side packet handlers.
     */
    public static void registerServerHandlers() {
        // Register town data request handler
        ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_DATA_REQUEST,
                TownNetworkHandler::handleTownDataRequest
        );

        // Register town list request handler
        ServerPlayNetworking.registerGlobalReceiver(
                NetworkConstants.TOWN_LIST_REQUEST,
                TownNetworkHandler::handleTownListRequest
        );
    }

    // Client-side methods

    /**
     * Requests town data from the server.
     */
    @Environment(EnvType.CLIENT)
    public static void requestTownData() {
        try {
            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Add player UUID for validation
            if (MinecraftClient.getInstance().player != null) {
                buf.writeUuid(MinecraftClient.getInstance().player.getUuid());
            } else {
                return; // Can't send without a player
            }

            // Send packet to server
            NetworkManager.sendToServer(NetworkConstants.TOWN_DATA_REQUEST, buf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting town data: " + e.getMessage());
        }
    }

    /**
     * Requests town list from the server.
     */
    @Environment(EnvType.CLIENT)
    public static void requestTownList() {
        try {
            // Create packet buffer
            PacketByteBuf buf = NetworkManager.createPacket();

            // Add player UUID for validation
            if (MinecraftClient.getInstance().player != null) {
                buf.writeUuid(MinecraftClient.getInstance().player.getUuid());
            } else {
                return; // Can't send without a player
            }

            // Send packet to server
            NetworkManager.sendToServer(NetworkConstants.TOWN_LIST_REQUEST, buf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error requesting town list: " + e.getMessage());
        }
    }

    // Client-side packet handlers

    /**
     * Handles town data response packets.
     */
    @Environment(EnvType.CLIENT)
    private static void handleTownDataResponse(MinecraftClient client, ClientPlayNetworkHandler handler,
                                              PacketByteBuf buf, PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read town data
            boolean hasTown = buf.readBoolean();

            if (hasTown) {
                // Read town UUID
                UUID townId = buf.readUuid();

                // Read town name
                String townName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

                // Read town description
                String townDescription = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

                // Read town status
                boolean isOpen = buf.readBoolean();

                // Read player count
                int playerCount = buf.readInt();

                // Read max players
                int maxPlayers = buf.readInt();

                // Read player rank
                int rankOrdinal = buf.readInt();
                TownPlayerRank playerRank = TownPlayerRank.values()[rankOrdinal];

                // Read player permissions
                int permissionCategoryCount = buf.readInt();
                Map<String, Map<String, Boolean>> permissions = new HashMap<>();

                for (int i = 0; i < permissionCategoryCount; i++) {
                    String category = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                    int permissionCount = buf.readInt();
                    Map<String, Boolean> categoryPermissions = new HashMap<>();

                    for (int j = 0; j < permissionCount; j++) {
                        String permission = buf.readString(NetworkConstants.MAX_STRING_LENGTH);
                        boolean value = buf.readBoolean();
                        categoryPermissions.put(permission, value);
                    }

                    permissions.put(category, categoryPermissions);
                }

                // Update town data in TownManager
                Town town = new Town(townId, townName);
                town.setDescription(townDescription);
                town.setOpen(isOpen);
                town.setMaxPlayers(maxPlayers);

                // Add the current player to the town
                if (client.player != null) {
                    TownPlayer townPlayer = new TownPlayer(client.player.getUuid(), client.player.getName().getString(), playerRank);

                    // Set permissions
                    for (Map.Entry<String, Map<String, Boolean>> entry : permissions.entrySet()) {
                        townPlayer.setCategoryPermissions(entry.getKey(), entry.getValue());
                    }

                    town.addPlayer(townPlayer);
                }

                // Update the town in the TownManager
                TownManager.getInstance().addTown(town);

                // Notify the player
                NotificationManager.getInstance().addInfoNotification("Town data updated: " + townName);
            } else {
                // Player is not in a town
                if (client.player != null) {
                    TownManager.getInstance().removePlayerFromTown(client.player.getUuid());
                }

                // Notify the player
                NotificationManager.getInstance().addInfoNotification("You are not in a town");
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town data response: " + e.getMessage());
        }
    }

    /**
     * Handles town list response packets.
     */
    @Environment(EnvType.CLIENT)
    private static void handleTownListResponse(MinecraftClient client, ClientPlayNetworkHandler handler,
                                              PacketByteBuf buf, PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read town count
            int townCount = buf.readInt();
            PacketValidator.validateListSize(townCount);

            // Read town data
            List<Town> towns = new ArrayList<>();

            for (int i = 0; i < townCount; i++) {
                // Read town UUID
                UUID townId = buf.readUuid();

                // Read town name
                String townName = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

                // Read town description
                String townDescription = buf.readString(NetworkConstants.MAX_STRING_LENGTH);

                // Read town status
                boolean isOpen = buf.readBoolean();

                // Read player count
                int playerCount = buf.readInt();

                // Read max players
                int maxPlayers = buf.readInt();

                // Create town object
                Town town = new Town(townId, townName);
                town.setDescription(townDescription);
                town.setOpen(isOpen);
                town.setMaxPlayers(maxPlayers);

                towns.add(town);
            }

            // Update town list in TownManager
            TownManager.getInstance().setTownList(towns);

            // Notify the player
            NotificationManager.getInstance().addInfoNotification("Town list updated: " + townCount + " towns");

            // Notify town list update callbacks
            TownDataSynchronizer.notifyTownListUpdated();
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town list response: " + e.getMessage());
        }
    }

    // Server-side packet handlers

    /**
     * Handles town data request packets.
     */
    private static void handleTownDataRequest(MinecraftServer server, ServerPlayerEntity player,
                                             ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                             PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read player UUID for validation
            UUID requestingPlayerId = buf.readUuid();

            // Validate that the requesting player is the same as the sender
            if (!player.getUuid().equals(requestingPlayerId)) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " tried to request town data for another player");
                return;
            }

            // Get the player's town
            Town playerTown = TownManager.getInstance().getPlayerTown(player.getUuid());

            // Create response packet
            PacketByteBuf responseBuf = NetworkManager.createPacket();

            if (playerTown != null) {
                // Player is in a town
                responseBuf.writeBoolean(true);

                // Write town UUID
                responseBuf.writeUuid(playerTown.getId());

                // Write town name
                responseBuf.writeString(playerTown.getName());

                // Write town description
                responseBuf.writeString(playerTown.getDescription());

                // Write town status
                responseBuf.writeBoolean(playerTown.isOpen());

                // Write player count
                responseBuf.writeInt(playerTown.getPlayerCount());

                // Write max players
                responseBuf.writeInt(playerTown.getMaxPlayers());

                // Write player rank
                TownPlayerRank playerRank = playerTown.getPlayerRank(player.getUuid());
                responseBuf.writeInt(playerRank.ordinal());

                // Write player permissions
                TownPlayer townPlayer = playerTown.getPlayer(player.getUuid());
                Map<String, Map<String, Boolean>> permissions = townPlayer.getAllPermissions();

                responseBuf.writeInt(permissions.size());

                for (Map.Entry<String, Map<String, Boolean>> entry : permissions.entrySet()) {
                    responseBuf.writeString(entry.getKey());

                    Map<String, Boolean> categoryPermissions = entry.getValue();
                    responseBuf.writeInt(categoryPermissions.size());

                    for (Map.Entry<String, Boolean> permEntry : categoryPermissions.entrySet()) {
                        responseBuf.writeString(permEntry.getKey());
                        responseBuf.writeBoolean(permEntry.getValue());
                    }
                }
            } else {
                // Player is not in a town
                responseBuf.writeBoolean(false);
            }

            // Send response to player
            NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_DATA_RESPONSE, responseBuf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town data request: " + e.getMessage());
        }
    }

    /**
     * Handles town list request packets.
     */
    private static void handleTownListRequest(MinecraftServer server, ServerPlayerEntity player,
                                            ServerPlayNetworkHandler handler, PacketByteBuf buf,
                                            PacketSender sender) {
        try {
            // Validate packet size
            PacketValidator.validatePacketSize(buf);

            // Read player UUID for validation
            UUID requestingPlayerId = buf.readUuid();

            // Validate that the requesting player is the same as the sender
            if (!player.getUuid().equals(requestingPlayerId)) {
                Pokecobbleclaim.LOGGER.warn("Player " + player.getName().getString() + " tried to request town list for another player");
                return;
            }

            // Get all towns
            Collection<Town> townCollection = TownManager.getInstance().getAllTowns();
            List<Town> towns = new ArrayList<>(townCollection);

            // Create response packet
            PacketByteBuf responseBuf = NetworkManager.createPacket();

            // Write town count
            responseBuf.writeInt(towns.size());

            // Write town data
            for (Town town : towns) {
                // Write town UUID
                responseBuf.writeUuid(town.getId());

                // Write town name
                responseBuf.writeString(town.getName());

                // Write town description
                responseBuf.writeString(town.getDescription());

                // Write town status
                responseBuf.writeBoolean(town.isOpen());

                // Write player count
                responseBuf.writeInt(town.getPlayerCount());

                // Write max players
                responseBuf.writeInt(town.getMaxPlayers());
            }

            // Send response to player
            NetworkManager.sendToPlayer(player, NetworkConstants.TOWN_LIST_RESPONSE, responseBuf);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error handling town list request: " + e.getMessage());
        }
    }
}
