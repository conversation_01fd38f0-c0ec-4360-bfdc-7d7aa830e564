package com.pokecobble.town.network;

import net.minecraft.util.math.ChunkPos;

/**
 * Packet sent to the server to claim a chunk.
 */
public class ClaimChunkPacket {
    private final ChunkPos chunkPos;
    private final String tagName;

    /**
     * Creates a new claim chunk packet.
     *
     * @param chunkPos The chunk position
     * @param tagName The tag name
     */
    public ClaimChunkPacket(ChunkPos chunkPos, String tagName) {
        this.chunkPos = chunkPos;
        this.tagName = tagName;
    }

    /**
     * Gets the chunk position.
     *
     * @return The chunk position
     */
    public ChunkPos getChunkPos() {
        return chunkPos;
    }

    /**
     * Gets the tag name.
     *
     * @return The tag name
     */
    public String getTagName() {
        return tagName;
    }
}
