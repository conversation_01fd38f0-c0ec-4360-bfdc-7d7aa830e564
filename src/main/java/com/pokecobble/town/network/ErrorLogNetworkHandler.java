package com.pokecobble.town.network;

import com.pokecobble.Pokecobbleclaim;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.minecraft.util.Identifier;

/**
 * Handles network packets related to the error log system.
 */
public class ErrorLogNetworkHandler {
    // Packet identifiers
    public static final Identifier OPEN_ERROR_LOG = new Identifier("pokecobbleclaim", "open_error_log");

    /**
     * Registers client-side packet handlers.
     * This is only called on the client side.
     * The implementation is in PokecobbleclaimClient to avoid class loading issues on the server.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientHandlers() {
        // Implementation moved to PokecobbleclaimClient class
        // to avoid class loading issues on the server
    }
}
