package com.pokecobble.town.network;

import net.minecraft.util.math.ChunkPos;

/**
 * Packet sent to the server to tag a chunk.
 */
public class TagChunkPacket {
    private final ChunkPos chunkPos;
    private final String tagName;

    /**
     * Creates a new tag chunk packet.
     *
     * @param chunkPos The chunk position
     * @param tagName The tag name
     */
    public TagChunkPacket(ChunkPos chunkPos, String tagName) {
        this.chunkPos = chunkPos;
        this.tagName = tagName;
    }

    /**
     * Gets the chunk position.
     *
     * @return The chunk position
     */
    public ChunkPos getChunkPos() {
        return chunkPos;
    }

    /**
     * Gets the tag name.
     *
     * @return The tag name
     */
    public String getTagName() {
        return tagName;
    }
}
