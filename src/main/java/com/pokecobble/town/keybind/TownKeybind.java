package com.pokecobble.town.keybind;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.claim.ClaimTool;
import com.pokecobble.town.client.NotificationRenderer;
import com.pokecobble.town.gui.TownScreenManager;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import org.lwjgl.glfw.GLFW;

public class TownKeybind {
    private static KeyBinding townKey;

    public static void register() {
        Pokecobbleclaim.LOGGER.info("Registering town keybinding");

        // Register the keybinding (T key by default)
        townKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
                "key.pokecobbleclaim.open_town_menu",
                InputUtil.Type.KEYSYM,
                GLFW.GLFW_KEY_T,
                "category.pokecobbleclaim.town_management"
        ));

        // Register the tick event to check for key presses
        ClientTickEvents.END_CLIENT_TICK.register(client -> {
            if (townKey.wasPressed()) {
                // Check if the claim tool is active
                if (ClaimTool.getInstance().isActive()) {
                    // Don't open the town screen when the claim tool is active
                    NotificationRenderer.addNotification("Cannot open town screen while claim tool is active");
                    return;
                }

                Pokecobbleclaim.LOGGER.info("Town key pressed, opening town screen");
                TownScreenManager.openTownScreen();
            }
        });
    }
}
