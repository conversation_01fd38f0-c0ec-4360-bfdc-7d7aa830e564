package com.pokecobble.town.gui;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.gui.NotificationManager;
import com.pokecobble.town.sound.SoundUtil;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;
import org.lwjgl.glfw.GLFW;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Screen for managing a player's rank and permissions in a town.
 * Redesigned with a clean, modern interface based on AdminRoleScreen design.
 */
public class PlayerManageScreen extends Screen {
    private final Screen parent;
    private final Town town;
    private final TownPlayer targetPlayer;

    // Panel dimensions
    private int panelWidth;
    private int panelHeight;
    private int leftX;
    private int topY;

    // Colors
    private static final int BACKGROUND_COLOR = 0xE0101010;
    private static final int HEADER_COLOR = 0xFF202020;
    private static final int CATEGORY_BG_COLOR = 0x80303030;
    private static final int CATEGORY_SELECTED_COLOR = 0x80404080;
    private static final int CATEGORY_HOVER_COLOR = 0x40FFFFFF;
    private static final int TOGGLE_ON_COLOR = 0xFF4CAF50;
    private static final int TOGGLE_OFF_COLOR = 0xFFE53935;
    private static final int TOGGLE_DISABLED_COLOR = 0xFF757575;

    // Layout constants - even more compact
    private static final int ROW_HEIGHT = 16; // Slightly increased for better spacing
    private static final int CATEGORY_HEIGHT = 20;
    private static final int TOGGLE_WIDTH = 30; // Reduced width to prevent overlap
    private static final int TOGGLE_HEIGHT = 10; // Reduced height to prevent overlap
    private static final int BUTTON_SPACING = 2; // Increased spacing between elements

    // Categories and permissions
    private final List<PermissionCategory> categories = new ArrayList<>();
    private int selectedCategoryIndex = 0;

    // Scrolling
    private int scrollOffset = 0;
    private static final int MAX_SCROLL_SPEED = 15; // Increased for more responsive scrolling

    // Animation variables
    private float animationProgress = 0.0f;
    private long lastRenderTime = 0;
    private static final float ANIMATION_SPEED = 0.08f;

    // Toggle states
    private final Map<String, Boolean> interfaceAccess = new HashMap<>();
    private final Map<String, Map<String, Boolean>> permissionToggles = new HashMap<>();

    // Status message
    private String statusMessage = "";
    private int statusColor = 0xFFFFFF;
    private int statusTimer = 0;

    /**
     * Creates a new player management screen.
     *
     * @param parent The parent screen
     * @param town The town
     * @param targetPlayer The player to manage
     */
    public PlayerManageScreen(Screen parent, Town town, TownPlayer targetPlayer) {
        super(Text.literal("Manage Player"));
        this.parent = parent;
        this.town = town;
        this.targetPlayer = targetPlayer;

        // Request the latest player data from the server
        com.pokecobble.town.network.player.PlayerNetworkHandler.requestPlayerData(targetPlayer.getUuid());

        // Setup permission categories
        setupPermissionCategories();
    }

    /**
     * Sets up the permission categories and initializes toggle states.
     */
    private void setupPermissionCategories() {
        categories.clear();
        interfaceAccess.clear();
        permissionToggles.clear();

        // Add rank category
        List<String> rankOptions = new ArrayList<>();
        for (TownPlayerRank rank : TownPlayerRank.values()) {
            rankOptions.add(rank.getDisplayName());
        }
        categories.add(new PermissionCategory("Rank", "Player's position in the town hierarchy", rankOptions,
                getRankIndex(targetPlayer.getRank())));

        // Add player management category
        List<String> playerManageOptions = new ArrayList<>();
        playerManageOptions.add("Can kick players");
        playerManageOptions.add("Can change player ranks");
        playerManageOptions.add("Can manage player permissions");
        playerManageOptions.add("Can invite players");
        categories.add(new PermissionCategory("Player Management", "Permissions for managing town members", playerManageOptions, 0));

        // Initialize interface access for Player Management
        interfaceAccess.put("Player Management", targetPlayer.getRank().ordinal() <= TownPlayerRank.ADMIN.ordinal());

        // Initialize permission toggles for Player Management
        Map<String, Boolean> playerManageToggles = targetPlayer.getCategoryPermissions("Player Management");
        if (playerManageToggles == null) {
            playerManageToggles = new HashMap<>();
            playerManageToggles.put("Can kick players", targetPlayer.getRank().ordinal() <= TownPlayerRank.ADMIN.ordinal());
            playerManageToggles.put("Can change player ranks", targetPlayer.getRank().ordinal() <= TownPlayerRank.ADMIN.ordinal());
            playerManageToggles.put("Can manage player permissions", targetPlayer.getRank().ordinal() <= TownPlayerRank.ADMIN.ordinal());
            playerManageToggles.put("Can invite players", targetPlayer.getRank().ordinal() <= TownPlayerRank.OWNER.ordinal());
        } else {
            // Create a copy to avoid modifying the original map
            playerManageToggles = new HashMap<>(playerManageToggles);
        }
        permissionToggles.put("Player Management", playerManageToggles);

        // Add claim tool category
        List<String> claimOptions = new ArrayList<>();
        claimOptions.add("Can view claims");
        claimOptions.add("Can access claim tool");
        claimOptions.add("Can delete claims");
        categories.add(new PermissionCategory("Claim Tool", "Permissions for managing town territory", claimOptions, 0));

        // Initialize interface access for Claim Tool
        interfaceAccess.put("Claim Tool", targetPlayer.getRank().ordinal() <= TownPlayerRank.MEMBER.ordinal());

        // Initialize permission toggles for Claim Tool
        Map<String, Boolean> claimToggles = targetPlayer.getCategoryPermissions("Claim Tool");
        if (claimToggles == null) {
            claimToggles = new HashMap<>();
            claimToggles.put("Can view claims", true); // Everyone can view claims
            claimToggles.put("Can access claim tool", targetPlayer.getRank().ordinal() <= TownPlayerRank.OWNER.ordinal());
            claimToggles.put("Can delete claims", targetPlayer.getRank().ordinal() <= TownPlayerRank.OWNER.ordinal());
        } else {
            // Create a copy to avoid modifying the original map
            claimToggles = new HashMap<>(claimToggles);
        }
        permissionToggles.put("Claim Tool", claimToggles);

        // Add bank category
        List<String> bankOptions = new ArrayList<>();
        bankOptions.add("Can view balance");
        bankOptions.add("Can deposit money");
        bankOptions.add("Can withdraw money");
        bankOptions.add("Can view transactions");
        categories.add(new PermissionCategory("Town Bank", "Permissions for managing town finances", bankOptions, 0));

        // Add level category
        List<String> levelOptions = new ArrayList<>();
        levelOptions.add("Can view level");
        levelOptions.add("Can contribute resources");
        levelOptions.add("Can view benefits");
        categories.add(new PermissionCategory("Town Level", "Permissions for town leveling system", levelOptions, 0));

        // Initialize interface access for Town Bank
        interfaceAccess.put("Town Bank", targetPlayer.getRank().ordinal() <= TownPlayerRank.MEMBER.ordinal());

        // Initialize permission toggles for Town Bank
        Map<String, Boolean> bankToggles = targetPlayer.getCategoryPermissions("Town Bank");
        if (bankToggles == null) {
            bankToggles = new HashMap<>();
            bankToggles.put("Can view balance", targetPlayer.getRank().ordinal() <= TownPlayerRank.MEMBER.ordinal());
            bankToggles.put("Can deposit money", targetPlayer.getRank().ordinal() <= TownPlayerRank.MEMBER.ordinal());
            bankToggles.put("Can withdraw money", targetPlayer.getRank().ordinal() <= TownPlayerRank.ADMIN.ordinal());
            bankToggles.put("Can view transactions", targetPlayer.getRank().ordinal() <= TownPlayerRank.ADMIN.ordinal());
        } else {
            // Create a copy to avoid modifying the original map
            bankToggles = new HashMap<>(bankToggles);
        }
        permissionToggles.put("Town Bank", bankToggles);

        // Initialize interface access for Town Level
        interfaceAccess.put("Town Level", targetPlayer.getRank().ordinal() <= TownPlayerRank.MEMBER.ordinal());

        // Initialize permission toggles for Town Level
        Map<String, Boolean> levelToggles = targetPlayer.getCategoryPermissions("Town Level");
        if (levelToggles == null) {
            levelToggles = new HashMap<>();
            levelToggles.put("Can view level", targetPlayer.getRank().ordinal() <= TownPlayerRank.MEMBER.ordinal());
            levelToggles.put("Can contribute resources", targetPlayer.getRank().ordinal() <= TownPlayerRank.MEMBER.ordinal());
            levelToggles.put("Can view benefits", targetPlayer.getRank().ordinal() <= TownPlayerRank.MEMBER.ordinal());
        } else {
            // Create a copy to avoid modifying the original map
            levelToggles = new HashMap<>(levelToggles);
        }
        permissionToggles.put("Town Level", levelToggles);

        // Add settings category
        List<String> settingsOptions = new ArrayList<>();
        settingsOptions.add("Can view settings");
        settingsOptions.add("Can modify settings");
        settingsOptions.add("Can change town name");
        settingsOptions.add("Can change town description");
        settingsOptions.add("Can change town spawn");
        categories.add(new PermissionCategory("Town Settings", "Permissions for managing town configuration", settingsOptions, 0));

        // Initialize interface access for Town Settings
        interfaceAccess.put("Town Settings", targetPlayer.getRank().ordinal() <= TownPlayerRank.ADMIN.ordinal());

        // Initialize permission toggles for Town Settings
        Map<String, Boolean> settingsToggles = targetPlayer.getCategoryPermissions("Town Settings");
        if (settingsToggles == null) {
            settingsToggles = new HashMap<>();
            settingsToggles.put("Can view settings", targetPlayer.getRank().ordinal() <= TownPlayerRank.MEMBER.ordinal());
            settingsToggles.put("Can modify settings", targetPlayer.getRank().ordinal() <= TownPlayerRank.ADMIN.ordinal());
            settingsToggles.put("Can change town name", targetPlayer.getRank().ordinal() <= TownPlayerRank.OWNER.ordinal());
            settingsToggles.put("Can change town description", targetPlayer.getRank().ordinal() <= TownPlayerRank.ADMIN.ordinal());
            settingsToggles.put("Can change town spawn", targetPlayer.getRank().ordinal() <= TownPlayerRank.ADMIN.ordinal());
        } else {
            // Create a copy to avoid modifying the original map
            settingsToggles = new HashMap<>(settingsToggles);
        }
        permissionToggles.put("Town Settings", settingsToggles);
    }

    /**
     * Gets the index of a rank in the TownPlayerRank enum.
     */
    private int getRankIndex(TownPlayerRank rank) {
        return rank.ordinal();
    }

    @Override
    protected void init() {
        super.init();

        // Reset animation
        animationProgress = 0.0f;
        lastRenderTime = System.currentTimeMillis();

        // Calculate panel dimensions - ultra compact to fit on screen
        panelWidth = Math.min(700, width - 40);
        panelHeight = Math.min(450, height - 40);
        leftX = (width - panelWidth) / 2;
        topY = (height - panelHeight) / 2;
    }

    @Override
    public void tick() {
        super.tick();

        // Update status timer
        if (statusTimer > 0) {
            statusTimer--;
        }
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Update animation progress
        long currentTime = System.currentTimeMillis();
        float deltaTime = (currentTime - lastRenderTime) / 1000.0f;
        lastRenderTime = currentTime;

        animationProgress = Math.min(1.0f, animationProgress + ANIMATION_SPEED * deltaTime * 60);

        // Draw dark background with a subtle pattern
        this.renderBackground(context);

        // Draw panel background with gradient and border
        // Main panel background - dark translucent with gradient
        context.fillGradient(leftX, topY, leftX + panelWidth, topY + panelHeight, 0xD0101010, 0xD0202030);

        // Draw subtle pattern
        for (int y = 0; y < panelHeight; y += 4) {
            for (int x = 0; x < panelWidth; x += 4) {
                if ((x + y) % 8 == 0) {
                    context.fill(leftX + x, topY + y, leftX + x + 1, topY + y + 1, 0x10FFFFFF);
                }
            }
        }

        // Draw header - ultra compact
        int headerHeight = 25;
        context.fillGradient(leftX, topY, leftX + panelWidth, topY + headerHeight, 0xA0303050, 0xA0404060);

        // Draw title with smaller text
        String titleText = "Manage " + targetPlayer.getName();
        context.drawCenteredTextWithShadow(this.textRenderer, Text.literal(titleText).formatted(Formatting.BOLD),
            leftX + panelWidth / 2, topY + 8, 0xFFFFFF);

        // Draw player info in header to save space
        String rankText = "Current Rank: " + targetPlayer.getRank().getDisplayName();
        context.drawTextWithShadow(this.textRenderer, rankText,
            leftX + 10, topY + headerHeight + 5, 0xFFFFFF);

        // Calculate content area dimensions - minimal spacing
        int contentY = topY + headerHeight + 20;
        int contentHeight = panelHeight - headerHeight - 50;

        // Draw categories on the left - ultra narrow to maximize content area
        int categoryX = leftX + 5;
        int categoryWidth = 120;
        int categoryY = contentY;

        // Draw panel backgrounds for better visibility
        context.fill(categoryX, contentY, categoryX + categoryWidth, topY + panelHeight - 45, 0x40000000);

        for (int i = 0; i < categories.size(); i++) {
            PermissionCategory category = categories.get(i);
            boolean isSelected = i == selectedCategoryIndex;
            boolean isHovered = mouseX >= categoryX && mouseX <= categoryX + categoryWidth &&
                               mouseY >= categoryY && mouseY <= categoryY + CATEGORY_HEIGHT;

            // Apply entrance animation
            float entranceOffset = 30.0f * (1.0f - Math.min(1.0f, animationProgress * 4.0f - i * 0.15f));
            int animatedY = categoryY;
            if (entranceOffset > 0) {
                animatedY += entranceOffset;
            }

            // Draw category background with gradient
            int bgColor = isSelected ? CATEGORY_SELECTED_COLOR : CATEGORY_BG_COLOR;
            int bgColorEnd = isSelected ? 0x80505090 : 0x80404040;
            if (isHovered && !isSelected) {
                bgColor = CATEGORY_HOVER_COLOR;
                bgColorEnd = 0x60FFFFFF;
            }
            context.fillGradient(categoryX, animatedY, categoryX + categoryWidth, animatedY + CATEGORY_HEIGHT, bgColor, bgColorEnd);

            // Draw category name - ultra compact
            String icon = getCategoryIcon(category.name);

            // Scale text to 0.8 of normal size by using a smaller font renderer
            context.drawTextWithShadow(this.textRenderer, icon + " " + category.name,
                categoryX + 3, animatedY + 6, 0xFFFFFF);

            categoryY += CATEGORY_HEIGHT + 5;
        }

        // Draw content area for selected category - maximized size
        PermissionCategory selectedCategory = categories.get(selectedCategoryIndex);
        int contentX = categoryX + categoryWidth + 5;
        int contentWidth = panelWidth - categoryWidth - 15;

        // Draw content area background with gradient
        context.fillGradient(contentX, contentY, contentX + contentWidth, contentY + contentHeight, 0x40000000, 0x60000000);

        // Draw category title and description with better styling
        context.drawTextWithShadow(this.textRenderer, Text.literal(selectedCategory.name).formatted(Formatting.BOLD, Formatting.GOLD),
            contentX + 5, contentY + 5, 0xFFFFFF);
        context.drawTextWithShadow(this.textRenderer, selectedCategory.description,
            contentX + 5, contentY + 18, 0xAAAAAA);

        // Draw options based on category type
        if (selectedCategory.name.equals("Rank")) {
            // Draw rank selection - ultra compact
            int rankY = contentY + 30;

            for (int i = 0; i < selectedCategory.options.size(); i++) {
                String rankName = selectedCategory.options.get(i).toString();
                boolean isSelected = i == selectedCategory.selectedOption;
                boolean isHovered = mouseX >= contentX + 5 && mouseX <= contentX + contentWidth - 5 &&
                                   mouseY >= rankY && mouseY <= rankY + ROW_HEIGHT;

                // Draw rank option background
                int rankBgColor = isSelected ? 0x80404080 : (isHovered ? 0x40FFFFFF : 0x20FFFFFF);
                context.fill(contentX + 5, rankY, contentX + contentWidth - 5, rankY + ROW_HEIGHT, rankBgColor);

                // Draw rank name
                context.drawTextWithShadow(this.textRenderer, rankName,
                    contentX + 8, rankY + 3, 0xFFFFFF);

                // Draw selection indicator
                if (isSelected) {
                    context.drawTextWithShadow(this.textRenderer, "✓",
                        contentX + contentWidth - 15, rankY + 3, 0xFF4CAF50);
                }

                rankY += ROW_HEIGHT + 1;
            }
        } else {
            // Draw interface access toggle - ultra compact
            int toggleY = contentY + 30;

            context.drawTextWithShadow(this.textRenderer, "Interface Access:",
                contentX + 5, toggleY + 2, 0xFFFFFF);

            boolean hasAccess = interfaceAccess.getOrDefault(selectedCategory.name, false);
            boolean toggleHovered = mouseX >= contentX + 110 && mouseX <= contentX + 110 + TOGGLE_WIDTH &&
                                   mouseY >= toggleY && mouseY <= toggleY + TOGGLE_HEIGHT;

            // Draw toggle
            this.drawToggleButton(context, contentX + 110, toggleY, TOGGLE_WIDTH, TOGGLE_HEIGHT,
                           hasAccess ? TOGGLE_ON_COLOR : TOGGLE_OFF_COLOR, toggleHovered);

            // Draw toggle text
            context.drawCenteredTextWithShadow(this.textRenderer, hasAccess ? "YES" : "NO",
                contentX + 110 + TOGGLE_WIDTH / 2, toggleY + 2, 0xFFFFFF);

            // Draw permissions if interface access is enabled
            if (hasAccess) {
                int permissionY = toggleY + TOGGLE_HEIGHT + 8;

                // Draw permissions header
                context.drawTextWithShadow(this.textRenderer, Text.literal("Permissions").formatted(Formatting.BOLD),
                    contentX + 5, permissionY, 0xFFFFFF);
                permissionY += 12;

                // Draw permissions in a vertical scrollable list
                Map<String, Boolean> categoryToggles = permissionToggles.getOrDefault(selectedCategory.name, new HashMap<>());

                // Calculate visible area for scrolling
                int listStartY = permissionY;
                int listHeight = contentY + contentHeight - listStartY - 10; // Leave space at bottom
                int totalContentHeight = selectedCategory.options.size() * (ROW_HEIGHT + 4); // Increased spacing between rows

                // Draw scrollbar if needed
                boolean needsScrolling = totalContentHeight > listHeight;
                if (needsScrolling) {
                    // Draw scrollbar background
                    context.fill(contentX + contentWidth - 5, listStartY,
                               contentX + contentWidth, listStartY + listHeight, 0x40FFFFFF);

                    // Draw scrollbar handle
                    if (totalContentHeight > 0) {
                        int scrollbarHeight = Math.max(20, listHeight * listHeight / totalContentHeight);
                        int scrollbarY = listStartY + (listHeight - scrollbarHeight) * scrollOffset /
                                        Math.max(1, totalContentHeight - listHeight);
                        context.fill(contentX + contentWidth - 5, scrollbarY,
                                   contentX + contentWidth, scrollbarY + scrollbarHeight, 0xFFAAAAAA);
                    }
                }

                // Apply scissor to clip content to the visible area
                context.enableScissor(
                    contentX, listStartY,
                    contentX + contentWidth - (needsScrolling ? 6 : 0), listStartY + listHeight
                );

                // Reset toggle positions before rendering
                for (PermissionOption option : selectedCategory.options) {
                    option.toggleX = 0;
                    option.toggleY = 0;
                }

                // Draw each permission option
                for (int i = 0; i < selectedCategory.options.size(); i++) {
                    PermissionOption option = selectedCategory.options.get(i);
                    boolean isEnabled = categoryToggles.getOrDefault(option.toString(), false);

                    int currentY = listStartY + i * (ROW_HEIGHT + 4) - scrollOffset; // Increased spacing between rows

                    // Skip if outside visible area
                    if (currentY + ROW_HEIGHT < listStartY || currentY > listStartY + listHeight) {
                        continue;
                    }

                    // Apply entrance animation
                    float entranceOffset = 30.0f * (1.0f - Math.min(1.0f, animationProgress * 4.0f - i * 0.15f));
                    int animatedY = currentY;
                    if (entranceOffset > 0) {
                        animatedY += entranceOffset;
                    }

                    // Draw option background with gradient
                    context.fillGradient(contentX + 5, animatedY, contentX + contentWidth - 10, animatedY + ROW_HEIGHT,
                                       0x40FFFFFF, 0x30FFFFFF);

                    // Draw permission label
                    context.drawTextWithShadow(this.textRenderer, option.toString(),
                        contentX + 10, animatedY + 2, 0xFFFFFF);

                    // Draw toggle on the right side - ensure it's inside the permission bar
                    int permToggleX = contentX + contentWidth - TOGGLE_WIDTH - (needsScrolling ? 15 : 10);
                    int permToggleY = animatedY + 2; // Align to top with small margin

                    // Store the toggle position for this option
                    option.toggleX = permToggleX;
                    option.toggleY = permToggleY;

                    boolean permToggleHovered = mouseX >= permToggleX && mouseX <= permToggleX + TOGGLE_WIDTH &&
                                              mouseY >= permToggleY && mouseY <= permToggleY + TOGGLE_HEIGHT;

                    // Draw toggle with improved styling
                    int toggleBgColor = isEnabled ? 0x8055FF55 : 0x80FF5555;
                    int toggleBgColorEnd = isEnabled ? 0x8044DD44 : 0x80DD4444;

                    if (permToggleHovered) {
                        toggleBgColor = isEnabled ? 0xA055FF55 : 0xA0FF5555;
                        toggleBgColorEnd = isEnabled ? 0xA044DD44 : 0xA0DD4444;
                    }

                    // Draw toggle background with gradient
                    context.fillGradient(permToggleX, permToggleY, permToggleX + TOGGLE_WIDTH, permToggleY + TOGGLE_HEIGHT,
                                       toggleBgColor, toggleBgColorEnd);

                    // Draw toggle border
                    int toggleBorderColor = isEnabled ? 0xFF44AA44 : 0xFFAA4444;
                    context.drawBorder(permToggleX, permToggleY, TOGGLE_WIDTH, TOGGLE_HEIGHT, toggleBorderColor);

                    // Draw toggle text
                    context.drawCenteredTextWithShadow(this.textRenderer, isEnabled ? "YES" : "NO",
                        permToggleX + TOGGLE_WIDTH / 2, permToggleY + 2, 0xFFFFFF);
                }

                // Disable scissor
                context.disableScissor();
            } else {
                // Draw disabled message
                context.drawTextWithShadow(this.textRenderer, "All permissions are disabled when interface access is off.",
                    contentX + 5, toggleY + TOGGLE_HEIGHT + 10, 0xFFFF5555);
            }
        }

        // Draw save and back buttons at the very bottom to avoid overlap
        int buttonWidth = 60;
        int buttonHeight = 16;
        int buttonY = topY + panelHeight - buttonHeight - 5; // Very close to bottom

        // Save button on right
        int saveButtonX = leftX + panelWidth - buttonWidth - 5;
        boolean saveHovered = mouseX >= saveButtonX && mouseX <= saveButtonX + buttonWidth &&
                             mouseY >= buttonY && mouseY <= buttonY + buttonHeight;

        this.drawButton(context, saveButtonX, buttonY, buttonWidth, buttonHeight,
                 0xFF4CAF50, saveHovered, "Save");

        // Back button on left
        int backButtonX = leftX + 5;
        boolean backHovered = mouseX >= backButtonX && mouseX <= backButtonX + buttonWidth &&
                             mouseY >= buttonY && mouseY <= buttonY + buttonHeight;

        this.drawButton(context, backButtonX, buttonY, buttonWidth, buttonHeight,
                 0xFF555555, backHovered, "Back");

        // Draw status message if active - position above buttons
        if (statusTimer > 0) {
            context.drawCenteredTextWithShadow(this.textRenderer, statusMessage,
                leftX + panelWidth / 2, buttonY - 15, statusColor);
        }

        super.render(context, mouseX, mouseY, delta);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0) { // Left click
            // Check if clicked on a category
            int categoryX = leftX + 5;
            int categoryWidth = 120;
            int categoryY = topY + 45;

            for (int i = 0; i < categories.size(); i++) {
                if (mouseX >= categoryX && mouseX <= categoryX + categoryWidth &&
                    mouseY >= categoryY && mouseY <= categoryY + CATEGORY_HEIGHT) {
                    PermissionCategory category = categories.get(i);

                    // Select this category
                    selectedCategoryIndex = i;
                    // Reset scroll offset when changing categories
                    scrollOffset = 0;
                    playClickSound();
                    return true;
                }

                categoryY += CATEGORY_HEIGHT + 5;
            }

            // Get content area dimensions
            int contentY = topY + 45;
            int contentHeight = panelHeight - 100;
            int contentX = categoryX + categoryWidth + 5;
            int contentWidth = panelWidth - categoryWidth - 15;

            // Get selected category
            PermissionCategory selectedCategory = categories.get(selectedCategoryIndex);

            // Check if clicked on rank options
            if (selectedCategory.name.equals("Rank")) {
                int rankY = contentY + 30;

                for (int i = 0; i < selectedCategory.options.size(); i++) {
                    if (mouseX >= contentX + 5 && mouseX <= contentX + contentWidth - 5 &&
                        mouseY >= rankY && mouseY <= rankY + ROW_HEIGHT) {
                        // Select this rank
                        selectedCategory.selectedOption = i;
                        playClickSound();
                        return true;
                    }

                    rankY += ROW_HEIGHT + 2;
                }
            } else {
                // Check if clicked on interface access toggle
                int toggleY = contentY + 30;

                if (mouseX >= contentX + 110 && mouseX <= contentX + 110 + TOGGLE_WIDTH &&
                    mouseY >= toggleY && mouseY <= toggleY + TOGGLE_HEIGHT) {
                    // Toggle interface access
                    boolean currentAccess = interfaceAccess.getOrDefault(selectedCategory.name, false);
                    interfaceAccess.put(selectedCategory.name, !currentAccess);
                    // Reset scroll offset when toggling interface access
                    scrollOffset = 0;
                    playClickSound();
                    return true;
                }

                // Check if clicked on permission toggles
                boolean hasAccess = interfaceAccess.getOrDefault(selectedCategory.name, false);

                if (hasAccess) {
                    int permissionY = toggleY + TOGGLE_HEIGHT + 12;
                    Map<String, Boolean> categoryToggles = permissionToggles.getOrDefault(selectedCategory.name, new HashMap<>());

                    // Calculate visible area for scrolling
                    int listStartY = permissionY;
                    int listHeight = contentY + contentHeight - listStartY - 10;
                    int totalContentHeight = selectedCategory.options.size() * (ROW_HEIGHT + 4); // Increased spacing between rows
                    boolean needsScrolling = totalContentHeight > listHeight;

                    // Check if clicked on scrollbar
                    if (needsScrolling && mouseX >= contentX + contentWidth - 5 && mouseX <= contentX + contentWidth &&
                        mouseY >= listStartY && mouseY <= listStartY + listHeight) {
                        // Calculate new scroll position based on click position
                        int maxScroll = Math.max(0, totalContentHeight - listHeight);
                        int clickPos = (int)mouseY - listStartY;
                        scrollOffset = maxScroll * clickPos / listHeight;
                        scrollOffset = Math.max(0, Math.min(maxScroll, scrollOffset));
                        return true;
                    }

                    // Check if clicked on a permission toggle
                    for (int i = 0; i < selectedCategory.options.size(); i++) {
                        PermissionOption option = selectedCategory.options.get(i);

                        // Use the stored toggle position from rendering
                        int permToggleX = option.toggleX;
                        int permToggleY = option.toggleY;

                        // Only check if toggle position is valid (has been rendered)
                        if (permToggleX > 0 && permToggleY > 0) {
                            if (mouseX >= permToggleX && mouseX <= permToggleX + TOGGLE_WIDTH &&
                                mouseY >= permToggleY && mouseY <= permToggleY + TOGGLE_HEIGHT) {
                                // Toggle permission
                                boolean currentState = categoryToggles.getOrDefault(option.toString(), false);
                                categoryToggles.put(option.toString(), !currentState);
                                permissionToggles.put(selectedCategory.name, categoryToggles);
                                playClickSound();
                                return true;
                            }
                        }
                    }
                }
            }

            // Check save button
            int buttonWidth = 60;
            int buttonHeight = 16;
            int buttonY = topY + panelHeight - buttonHeight - 5;
            int saveButtonX = leftX + panelWidth - buttonWidth - 5;

            if (mouseX >= saveButtonX && mouseX <= saveButtonX + buttonWidth &&
                mouseY >= buttonY && mouseY <= buttonY + buttonHeight) {
                // Save changes
                saveChanges();
                playClickSound();
                return true;
            }

            // Check back button
            int backButtonX = leftX + 5;

            if (mouseX >= backButtonX && mouseX <= backButtonX + buttonWidth &&
                mouseY >= buttonY && mouseY <= buttonY + buttonHeight) {
                // Return to parent screen
                this.client.setScreen(parent);
                playClickSound();
                return true;
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        // Get selected category
        PermissionCategory selectedCategory = categories.get(selectedCategoryIndex);

        // Only handle scrolling for permission lists
        if (!selectedCategory.name.equals("Rank") &&
            interfaceAccess.getOrDefault(selectedCategory.name, false) &&
            selectedCategory.expanded) {

            // Calculate content area dimensions
            int contentY = topY + 45;
            int contentHeight = panelHeight - 100;
            int contentX = leftX + 5 + 120 + 5;
            int contentWidth = panelWidth - 120 - 15;

            // Calculate permission list area
            int toggleY = contentY + 30;
            int permissionY = toggleY + TOGGLE_HEIGHT + 12;
            int listStartY = permissionY;
            int listHeight = contentY + contentHeight - listStartY - 10;
            int totalContentHeight = selectedCategory.options.size() * (ROW_HEIGHT + 4); // Increased spacing between rows

            // Check if mouse is over the content area
            if (mouseX >= contentX && mouseX <= contentX + contentWidth &&
                mouseY >= listStartY && mouseY <= listStartY + listHeight) {

                // Calculate max scroll offset
                int maxScroll = Math.max(0, totalContentHeight - listHeight);

                // Update scroll offset based on scroll amount with smoother scrolling
                scrollOffset -= amount * MAX_SCROLL_SPEED;
                scrollOffset = Math.max(0, Math.min(maxScroll, scrollOffset));

                // Play a subtle sound for feedback
                playClickSound();

                return true;
            }
        }

        return super.mouseScrolled(mouseX, mouseY, amount);
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        if (keyCode == GLFW.GLFW_KEY_ESCAPE) {
            // Return to parent screen
            this.client.setScreen(parent);
            return true;
        }

        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    /**
     * Saves the changes made to the player's permissions.
     */
    private void saveChanges() {
        // Check if the current player has permission to modify settings
        boolean canModifySettings = false;
        if (client.player != null && town != null) {
            TownPlayer currentTownPlayer = town.getPlayer(client.player.getUuid());
            if (currentTownPlayer != null) {
                // Check if player is owner (always has all permissions) or has the specific permission
                canModifySettings = currentTownPlayer.getRank() == TownPlayerRank.OWNER ||
                                   currentTownPlayer.hasPermission("Town Settings", "Can modify settings");
            }
        }

        if (!canModifySettings) {
            // Show notification that player doesn't have permission to save changes
            NotificationManager.getInstance().addErrorNotification("You don't have permission to save changes.");
            return;
        }

        // Get the selected rank
        PermissionCategory rankCategory = categories.get(0); // Rank is always the first category
        TownPlayerRank selectedRank = TownPlayerRank.values()[rankCategory.selectedOption];

        // Send rank update to server
        if (selectedRank != targetPlayer.getRank()) {
            // Update player rank locally
            targetPlayer.setRank(selectedRank);

            // Send permissions update to server with the new rank
            Map<String, Boolean> rankPermissions = new HashMap<>();
            rankPermissions.put("rank", true);
            com.pokecobble.town.network.player.PlayerNetworkHandler.updatePlayerPermissions(
                targetPlayer.getUuid(), "Rank", rankPermissions);
        }

        // Update permissions for each category
        for (String categoryName : permissionToggles.keySet()) {
            boolean interfaceEnabled = interfaceAccess.getOrDefault(categoryName, false);
            Map<String, Boolean> permissions = permissionToggles.get(categoryName);

            // Check if permissions have changed
            Map<String, Boolean> currentPermissions = targetPlayer.getCategoryPermissions(categoryName);
            boolean permissionsChanged = false;

            // Compare permissions
            for (Map.Entry<String, Boolean> entry : permissions.entrySet()) {
                String permName = entry.getKey();
                boolean newValue = entry.getValue();
                boolean oldValue = currentPermissions.getOrDefault(permName, false);

                if (newValue != oldValue) {
                    permissionsChanged = true;
                    break;
                }
            }

            // If permissions have changed, send update to server
            if (permissionsChanged) {
                com.pokecobble.town.network.player.PlayerNetworkHandler.updatePlayerPermissions(
                    targetPlayer.getUuid(), categoryName, permissions);
            }
        }

        // Show success message using the notification system
        NotificationManager.getInstance().addSuccessNotification("Changes saved successfully!");

        // Play success sound
        playSuccessSound();
    }

    /**
     * Sets a status message to display.
     */
    private void setStatus(String message, int color) {
        this.statusMessage = message;
        this.statusColor = color;
        this.statusTimer = 60; // Show for 3 seconds (60 ticks)
    }

    /**
     * Gets the target player being managed.
     *
     * @return The target player
     */
    public TownPlayer getTargetPlayer() {
        return targetPlayer;
    }

    /**
     * Gets the parent screen.
     *
     * @return The parent screen
     */
    public Screen getParentScreen() {
        return parent;
    }

    /**
     * Plays a click sound.
     */
    private void playClickSound() {
        SoundUtil.playButtonClickSound();
    }

    /**
     * Plays a success sound.
     */
    private void playSuccessSound() {
        this.client.getSoundManager().play(net.minecraft.client.sound.PositionedSoundInstance.master(
            net.minecraft.sound.SoundEvents.ENTITY_PLAYER_LEVELUP, 0.8F));
    }

    /**
     * Gets an icon for a category.
     */
    private String getCategoryIcon(String categoryName) {
        switch (categoryName) {
            case "Rank": return "👑";
            case "Player Management": return "👥";
            case "Claim Tool": return "🔧";
            case "Town Bank": return "💰";
            case "Town Settings": return "⚙️";
            default: return "📋";
        }
    }

    /**
     * Draws a toggle button.
     */
    private void drawToggleButton(DrawContext context, int x, int y, int width, int height, int color, boolean isHovered) {
        // Draw button background
        int baseColor = isHovered ? lightenColor(color, 20) : color;
        context.fill(x, y, x + width, y + height, baseColor);

        // Draw button border
        context.drawBorder(x, y, width, height, 0xFFFFFFFF);
    }

    /**
     * Draws a button with text.
     */
    private void drawButton(DrawContext context, int x, int y, int width, int height, int color, boolean isHovered, String text) {
        // Draw button background
        int baseColor = isHovered ? lightenColor(color, 20) : color;
        context.fill(x, y, x + width, y + height, baseColor);

        // Draw button border
        context.drawBorder(x, y, width, height, 0xFFFFFFFF);

        // Draw button text
        context.drawCenteredTextWithShadow(this.textRenderer, text,
            x + width / 2, y + height / 2 - 4, 0xFFFFFF);
    }

    /**
     * Lightens a color by the specified amount.
     */
    private int lightenColor(int color, int amount) {
        int r = Math.min(255, ((color >> 16) & 0xFF) + amount);
        int g = Math.min(255, ((color >> 8) & 0xFF) + amount);
        int b = Math.min(255, (color & 0xFF) + amount);
        return 0xFF000000 | (r << 16) | (g << 8) | b;
    }

    /**
     * Class representing a permission category with options.
     */
    private static class PermissionCategory {
        private final String name;
        private final String description;
        private final List<PermissionOption> options;
        private int selectedOption;
        private boolean expanded = true; // Default to expanded

        public PermissionCategory(String name, String description, List<String> optionNames, int selectedOption) {
            this.name = name;
            this.description = description;
            this.options = new ArrayList<>();
            for (String optionName : optionNames) {
                this.options.add(new PermissionOption(optionName));
            }
            this.selectedOption = selectedOption;
        }
    }

    /**
     * Class representing a permission option with its toggle button position.
     */
    private static class PermissionOption {
        private final String name;
        private int toggleX;
        private int toggleY;

        public PermissionOption(String name) {
            this.name = name;
            this.toggleX = 0;
            this.toggleY = 0;
        }

        @Override
        public String toString() {
            return name;
        }
    }
}
