package com.pokecobble.town.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.gui.components.SimpleDropdown;
import com.pokecobble.town.logging.ErrorLogger;
import com.pokecobble.town.logging.ErrorLogger.ErrorEntry;
import com.pokecobble.town.logging.ErrorLogger.ErrorSeverity;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import java.util.ArrayList;
import java.util.List;

/**
 * Admin GUI for viewing error logs.
 * Shows a scrollable list of errors with filtering options.
 */
public class ErrorLogScreen extends Screen {
    private final Screen parent;

    // Panel dimensions
    private int panelWidth = 800;
    private int panelHeight = 500;
    private int leftX;
    private int topY;

    // UI colors
    private static final int BACKGROUND_COLOR = 0xCC000000; // Semi-transparent black
    private static final int PANEL_COLOR_TOP = 0xD0101010;  // Dark gradient top
    private static final int PANEL_COLOR_BOTTOM = 0xD0202030; // Dark gradient bottom
    private static final int HEADER_COLOR = 0xFF303050;     // Header background
    private static final int CONTENT_BG_COLOR = 0x30000000; // Content area background
    private static final int SCROLLBAR_TRACK = 0x20FFFFFF;  // Scrollbar track
    private static final int SCROLLBAR_THUMB = 0xC0FFFFFF;  // Scrollbar thumb

    // UI elements
    private ButtonWidget clearButton;
    private ButtonWidget refreshButton;
    private ButtonWidget filterModeButton;
    private TextFieldWidget searchField;
    private SimpleDropdown<ErrorSeverity> severityDropdown;
    private SimpleDropdown<String> sourceDropdown;

    // Scrolling
    private int scrollOffset = 0;
    private static final int SCROLL_AMOUNT = 15;
    private static final int ROW_HEIGHT = 24;
    private static final int ROW_SPACING = 2;

    // Filtering
    private ErrorSeverity minSeverity = ErrorSeverity.INFO;
    private String selectedSource = null;
    private String searchTerm = "";
    private boolean useExactSeverityFilter = true; // Set to true to show only the exact severity level

    // Expanded error details
    private ErrorEntry expandedError = null;

    // Error list
    private List<ErrorEntry> filteredErrors = new ArrayList<>();

    /**
     * Creates a new error log screen.
     *
     * @param parent The parent screen
     */
    public ErrorLogScreen(Screen parent) {
        super(Text.literal("Error Log"));
        this.parent = parent;

        // Apply initial filtering
        refreshErrorList();
    }

    @Override
    protected void init() {
        super.init();

        // Calculate panel dimensions
        panelWidth = Math.min(width - 40, 800);
        panelHeight = height - 40;

        // Calculate positions
        leftX = (width - panelWidth) / 2;
        topY = (height - panelHeight) / 2;

        // Add search field
        searchField = new TextFieldWidget(this.textRenderer, leftX + 10, topY + 10, 200, 20, Text.literal("Search"));
        searchField.setMaxLength(50);
        if (searchTerm != null && !searchTerm.isEmpty()) {
            searchField.setText(searchTerm);
        }
        searchField.setChangedListener(this::onSearchChanged);
        this.addDrawableChild(searchField);

        // Add clear button with mod styling
        clearButton = ButtonWidget.builder(Text.literal("Clear Logs"), button -> {
            ErrorLogger.getInstance().clearErrors();
            refreshErrorList();
        })
        .dimensions(leftX + panelWidth - 210, topY + 10, 100, 24)
        .build();
        this.addDrawableChild(clearButton);

        // Add refresh button with mod styling
        refreshButton = ButtonWidget.builder(Text.literal("Refresh"), button -> {
            refreshErrorList();
        })
        .dimensions(leftX + panelWidth - 100, topY + 10, 90, 24)
        .build();
        this.addDrawableChild(refreshButton);

        // Add filter mode toggle button
        filterModeButton = ButtonWidget.builder(Text.literal(useExactSeverityFilter ? "Exact Filter" : "Min Filter"), button -> {
            useExactSeverityFilter = !useExactSeverityFilter;
            refreshErrorList();
        })
        .dimensions(leftX + 300, topY + 40, 100, 24)
        .build();
        this.addDrawableChild(filterModeButton);

        // Create severity dropdown
        try {
            List<ErrorSeverity> severityOptions = new ArrayList<>();
            List<String> severityLabels = new ArrayList<>();

            for (ErrorSeverity severity : ErrorSeverity.values()) {
                severityOptions.add(severity);
                severityLabels.add(severity.getName());
            }

            severityDropdown = new SimpleDropdown<>(
                leftX + 10, topY + 40, 120, 24,
                severityOptions, severityLabels, minSeverity,
                severity -> {
                    if (severity != null) {
                        minSeverity = severity;
                        refreshErrorList();
                    }
                }
            );

            // Create source dropdown
            List<String> sourceOptions = new ArrayList<>();
            List<String> sourceLabels = new ArrayList<>();

            // Add "All Sources" option
            sourceOptions.add(null);
            sourceLabels.add("All Sources");

            // Add all sources from the error logger
            List<String> sources = ErrorLogger.getInstance().getAllSources();
            if (sources != null) {
                sourceOptions.addAll(sources);
                sourceLabels.addAll(sources);
            }

            sourceDropdown = new SimpleDropdown<>(
                leftX + 140, topY + 40, 150, 24,
                sourceOptions, sourceLabels, selectedSource,
                source -> {
                    selectedSource = source;
                    refreshErrorList();
                }
            );
        } catch (Exception e) {
            // If dropdown creation fails, log the error and continue without dropdowns
            Pokecobbleclaim.LOGGER.error("Failed to create dropdown menus: " + e.getMessage());
            e.printStackTrace();
            severityDropdown = null;
            sourceDropdown = null;
        }
    }



    /**
     * Called when the search field changes.
     *
     * @param text The new search text
     */
    private void onSearchChanged(String text) {
        searchTerm = text;
        refreshErrorList();
    }

    /**
     * Refreshes the error list based on current filters.
     */
    private void refreshErrorList() {
        if (useExactSeverityFilter) {
            // Use exact severity filtering
            filteredErrors = ErrorLogger.getInstance().getFilteredErrorsByExactSeverity(minSeverity, selectedSource, searchTerm);
        } else {
            // Use minimum severity filtering (original behavior)
            filteredErrors = ErrorLogger.getInstance().getFilteredErrors(minSeverity, selectedSource, searchTerm);
        }
        scrollOffset = 0;
        expandedError = null;
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Render dark background
        this.renderBackground(context);

        // Draw panel background with gradient
        context.fillGradient(leftX, topY, leftX + panelWidth, topY + panelHeight, PANEL_COLOR_TOP, PANEL_COLOR_BOTTOM);

        // Draw header
        context.fill(leftX, topY, leftX + panelWidth, topY + 80, HEADER_COLOR);

        // Draw title
        context.drawCenteredTextWithShadow(this.textRenderer, this.title, leftX + panelWidth / 2, topY + 5, 0xFFFFFF);

        // Draw filter labels
        context.drawTextWithShadow(this.textRenderer, "Severity:", leftX + 10, topY + 40 - 12, 0xCCCCCC);
        context.drawTextWithShadow(this.textRenderer, "Source:", leftX + 140, topY + 40 - 12, 0xCCCCCC);

        // Draw filter mode button
        boolean filterButtonHovered = mouseX >= filterModeButton.getX() && mouseX <= filterModeButton.getX() + filterModeButton.getWidth() &&
                                    mouseY >= filterModeButton.getY() && mouseY <= filterModeButton.getY() + filterModeButton.getHeight();
        drawModernButton(context, filterModeButton.getX(), filterModeButton.getY(), filterModeButton.getWidth(), filterModeButton.getHeight(),
                        0xFF4CAF50, filterButtonHovered, true);
        String filterModeText = useExactSeverityFilter ? "Exact Filter" : "Min Filter";
        context.drawCenteredTextWithShadow(this.textRenderer, filterModeText,
                                         filterModeButton.getX() + filterModeButton.getWidth() / 2,
                                         filterModeButton.getY() + 8, 0xFFFFFF);

        // Render dropdowns or fallback text
        if (severityDropdown != null) {
            severityDropdown.render(context, mouseX, mouseY);
        } else {
            // Draw fallback text showing current severity
            String severityText = minSeverity != null ? minSeverity.getName() : "All";
            drawModernButton(context, leftX + 10, topY + 40, 120, 24, 0xFF303050, false, true);
            context.drawCenteredTextWithShadow(this.textRenderer, severityText, leftX + 10 + 60, topY + 40 + 8, 0xFFFFFF);
        }

        if (sourceDropdown != null) {
            sourceDropdown.render(context, mouseX, mouseY);
        } else {
            // Draw fallback text showing current source
            String sourceText = selectedSource != null ? selectedSource : "All Sources";
            drawModernButton(context, leftX + 140, topY + 40, 150, 24, 0xFF303050, false, true);
            context.drawCenteredTextWithShadow(this.textRenderer, sourceText, leftX + 140 + 75, topY + 40 + 8, 0xFFFFFF);
        }

        // Draw content area
        int contentX = leftX + 10;
        int contentY = topY + 80;
        int contentWidth = panelWidth - 20;
        int contentHeight = panelHeight - 90;

        // Draw content background
        context.fill(contentX, contentY, contentX + contentWidth, contentY + contentHeight, CONTENT_BG_COLOR);

        // Calculate visible area for entries
        int entriesAreaY = contentY;
        int entriesAreaHeight = contentHeight;

        // Calculate total height of all entries
        int totalHeight = 0;
        for (ErrorEntry entry : filteredErrors) {
            boolean isExpanded = entry.equals(expandedError);
            totalHeight += isExpanded ? ROW_HEIGHT * 5 : ROW_HEIGHT;
            totalHeight += ROW_SPACING;
        }

        // Add extra padding at the bottom
        totalHeight += 10;

        // Calculate max scroll
        int maxScroll = Math.max(0, totalHeight - entriesAreaHeight);
        scrollOffset = Math.min(scrollOffset, maxScroll);

        // Draw scrollbar if needed
        if (maxScroll > 0) {
            // Draw scrollbar track
            context.fill(contentX + contentWidth - 8, entriesAreaY, contentX + contentWidth - 4, entriesAreaY + entriesAreaHeight, SCROLLBAR_TRACK);

            // Calculate scrollbar height and position
            int scrollbarHeight = Math.max(40, entriesAreaHeight * entriesAreaHeight / (totalHeight + entriesAreaHeight));
            float scrollRatio = (float)scrollOffset / maxScroll;
            int scrollbarY = entriesAreaY + (int)((entriesAreaHeight - scrollbarHeight) * scrollRatio);

            // Ensure scrollbar doesn't go out of bounds
            scrollbarY = Math.max(entriesAreaY, Math.min(scrollbarY, entriesAreaY + entriesAreaHeight - scrollbarHeight));

            // Draw scrollbar handle
            drawRoundedRect(context, contentX + contentWidth - 8, scrollbarY, 4, scrollbarHeight, SCROLLBAR_THUMB);
        }

        // Apply scissor to clip content to visible area
        context.enableScissor(
            contentX,
            entriesAreaY,
            contentX + contentWidth,
            entriesAreaY + entriesAreaHeight
        );

        // Draw error entries with scrolling
        int entryY = entriesAreaY - scrollOffset;

        for (ErrorEntry entry : filteredErrors) {
            boolean isExpanded = entry.equals(expandedError);
            int entryHeight = isExpanded ? ROW_HEIGHT * 5 : ROW_HEIGHT;

            // Skip if entry is completely outside visible area
            if (entryY + entryHeight < entriesAreaY || entryY > entriesAreaY + entriesAreaHeight) {
                entryY += entryHeight + ROW_SPACING;
                continue;
            }

            // Draw entry background based on severity
            int bgColor = 0x40000000;
            if (isExpanded) {
                bgColor = 0x80000000;
            }

            // Draw entry background
            drawRoundedRect(context, contentX, entryY, contentWidth - 10, entryHeight, bgColor);

            // Draw severity indicator
            int severityColor = entry.getSeverity().getColor();
            context.fill(contentX + 5, entryY + 5, contentX + 10, entryY + ROW_HEIGHT - 5, severityColor);

            // Draw timestamp
            context.drawTextWithShadow(this.textRenderer, entry.getFormattedTimestamp(), contentX + 15, entryY + 8, 0xAAAAAA);

            // Draw source
            int sourceWidth = this.textRenderer.getWidth(entry.getSource());
            context.drawTextWithShadow(this.textRenderer, entry.getSource(), contentX + 180, entryY + 8, 0xFFFFAA);

            // Draw player info if available
            if (entry.hasPlayerInfo() && !isExpanded) {
                String playerInfo = entry.getPlayerName() != null ? entry.getPlayerName() : "";
                context.drawTextWithShadow(this.textRenderer, playerInfo, contentX + 280, entryY + 8, 0xFFFF55);
            }

            // Draw message
            String message = entry.getMessage();
            if (message.length() > 60 && !isExpanded) {
                message = message.substring(0, 57) + "...";
            }
            int messageX = entry.hasPlayerInfo() && !isExpanded ? contentX + 380 : contentX + 280;
            context.drawTextWithShadow(this.textRenderer, message, messageX, entryY + 8, 0xFFFFFF);

            // Draw expanded details if expanded
            if (isExpanded) {
                // For INFO level, just show a simplified view
                if (entry.getSeverity() == ErrorLogger.ErrorSeverity.INFO) {
                    // Draw player info if available
                    if (entry.hasPlayerInfo()) {
                        StringBuilder playerText = new StringBuilder("Player: ");
                        if (entry.getPlayerName() != null) {
                            playerText.append(entry.getPlayerName());
                        }
                        context.drawTextWithShadow(this.textRenderer, playerText.toString(),
                            contentX + 15, entryY + ROW_HEIGHT + 5, 0xFFFF55);
                    }

                    // Draw "More Info" button only if needed
                    if (entry.hasPlayerInfo() || entry.getMessage().length() > 60) {
                        drawMoreInfoButton(context, entry, contentX, contentWidth, entryY, entryHeight);
                    }
                } else {
                    // For other severity levels, show more details

                    // Draw player info if available
                    if (entry.hasPlayerInfo()) {
                        StringBuilder playerText = new StringBuilder("Player: ");
                        if (entry.getPlayerName() != null) {
                            playerText.append(entry.getPlayerName());
                        }
                        if (entry.getPlayerUuid() != null) {
                            playerText.append(" (").append(entry.getPlayerUuid()).append(")");
                        }
                        context.drawTextWithShadow(this.textRenderer, playerText.toString(),
                            contentX + 15, entryY + ROW_HEIGHT + 5, 0xFFFF55);
                    }

                    // Draw exception message if available
                    int exceptionY = entry.hasPlayerInfo() ? entryY + ROW_HEIGHT + 25 : entryY + ROW_HEIGHT + 5;
                    if (entry.getExceptionMessage() != null) {
                        context.drawTextWithShadow(this.textRenderer, "Exception: " + entry.getExceptionMessage(),
                            contentX + 15, exceptionY, 0xFF5555);
                        exceptionY += 20;
                    }

                    // Draw stack trace if available
                    if (entry.getStackTrace() != null) {
                        String[] lines = entry.getStackTrace().split("\n");
                        int lineY = exceptionY;
                        for (int i = 0; i < Math.min(lines.length, 2); i++) {
                            context.drawTextWithShadow(this.textRenderer, lines[i], contentX + 15, lineY, 0xAAAAAA);
                            lineY += 15;
                        }
                    }

                    // Draw "More Info" button
                    drawMoreInfoButton(context, entry, contentX, contentWidth, entryY, entryHeight);
                }
            } else {
                // Reset button position when not expanded
                entry.moreInfoButtonX = -1;
                entry.moreInfoButtonY = -1;
            }

            entryY += entryHeight + ROW_SPACING;
        }

        context.disableScissor();

        // Draw custom styled buttons
        boolean clearHovered = mouseX >= clearButton.getX() && mouseX <= clearButton.getX() + clearButton.getWidth() &&
                             mouseY >= clearButton.getY() && mouseY <= clearButton.getY() + clearButton.getHeight();
        drawModernButton(context, clearButton.getX(), clearButton.getY(), clearButton.getWidth(), clearButton.getHeight(),
                        0xFFE53935, clearHovered, true);
        context.drawCenteredTextWithShadow(this.textRenderer, "Clear Logs",
                                         clearButton.getX() + clearButton.getWidth() / 2,
                                         clearButton.getY() + 8, 0xFFFFFF);

        boolean refreshHovered = mouseX >= refreshButton.getX() && mouseX <= refreshButton.getX() + refreshButton.getWidth() &&
                               mouseY >= refreshButton.getY() && mouseY <= refreshButton.getY() + refreshButton.getHeight();
        drawModernButton(context, refreshButton.getX(), refreshButton.getY(), refreshButton.getWidth(), refreshButton.getHeight(),
                        0xFF4CAF50, refreshHovered, true);
        context.drawCenteredTextWithShadow(this.textRenderer, "Refresh",
                                         refreshButton.getX() + refreshButton.getWidth() / 2,
                                         refreshButton.getY() + 8, 0xFFFFFF);

        // Draw search field
        searchField.render(context, mouseX, mouseY, delta);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Check for clicks on dropdowns first
        if (button == 0) { // Left click
            if (severityDropdown != null && severityDropdown.mouseClicked(mouseX, mouseY, button)) {
                return true;
            }

            if (sourceDropdown != null && sourceDropdown.mouseClicked(mouseX, mouseY, button)) {
                return true;
            }
        }

        // Check for clicks on error entries
        if (button == 0) { // Left click
            int contentX = leftX + 10;
            int contentY = topY + 80;
            int contentWidth = panelWidth - 20;

            // Calculate visible area for entries
            int entriesAreaY = contentY;
            int entriesAreaHeight = panelHeight - 90;

            // Check if mouse is in the entries area
            if (mouseX >= contentX && mouseX <= contentX + contentWidth - 10 &&
                mouseY >= entriesAreaY && mouseY <= entriesAreaY + entriesAreaHeight) {

                // Calculate positions with scrolling
                int entryY = entriesAreaY - scrollOffset;

                for (ErrorEntry entry : filteredErrors) {
                    boolean isExpanded = entry.equals(expandedError);
                    int entryHeight = isExpanded ? ROW_HEIGHT * 5 : ROW_HEIGHT;

                    // Skip if entry is completely outside visible area
                    if (entryY + entryHeight < entriesAreaY || entryY > entriesAreaY + entriesAreaHeight) {
                        entryY += entryHeight + ROW_SPACING;
                        continue;
                    }

                    // Check if click is on this entry
                    if (mouseY >= entryY && mouseY <= entryY + entryHeight) {
                        // Check if click is on the "More Info" button
                        if (isExpanded &&
                            mouseX >= entry.moreInfoButtonX && mouseX <= entry.moreInfoButtonX + entry.moreInfoButtonWidth &&
                            mouseY >= entry.moreInfoButtonY && mouseY <= entry.moreInfoButtonY + entry.moreInfoButtonHeight) {
                            // Open detailed view
                            this.client.setScreen(new ErrorDetailScreen(this, entry));
                            return true;
                        } else {
                            // Toggle expanded state
                            expandedError = isExpanded ? null : entry;
                            return true;
                        }
                    }

                    entryY += entryHeight + ROW_SPACING;
                }
            }

            // Check for clicks on scrollbar
            if (mouseX >= contentX + contentWidth - 8 && mouseX <= contentX + contentWidth - 4 &&
                mouseY >= entriesAreaY && mouseY <= entriesAreaY + entriesAreaHeight) {

                // Calculate total height of all entries
                int totalHeight = 0;
                for (ErrorEntry entry : filteredErrors) {
                    boolean isExpanded = entry.equals(expandedError);
                    totalHeight += isExpanded ? ROW_HEIGHT * 5 : ROW_HEIGHT;
                    totalHeight += ROW_SPACING;
                }

                // Add extra padding at the bottom
                totalHeight += 10;

                // Calculate max scroll
                int maxScroll = Math.max(0, totalHeight - entriesAreaHeight);

                if (maxScroll > 0) {
                    // Calculate new scroll position based on click position
                    float clickPosition = (float)(mouseY - entriesAreaY) / entriesAreaHeight;
                    scrollOffset = Math.round(clickPosition * maxScroll);
                    scrollOffset = Math.max(0, Math.min(scrollOffset, maxScroll));
                    return true;
                }
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        // Handle scrolling in the entries area
        int contentX = leftX + 10;
        int contentY = topY + 100;
        int contentWidth = panelWidth - 20;

        // Calculate visible area for entries
        int entriesAreaY = contentY;
        int entriesAreaHeight = panelHeight - 110;

        // Check if mouse is in the entries area
        if (mouseX >= contentX && mouseX <= contentX + contentWidth &&
            mouseY >= entriesAreaY && mouseY <= entriesAreaY + entriesAreaHeight) {

            // Calculate total height of all entries
            int totalHeight = 0;
            for (ErrorEntry entry : filteredErrors) {
                boolean isExpanded = entry.equals(expandedError);
                totalHeight += isExpanded ? ROW_HEIGHT * 5 : ROW_HEIGHT;
                totalHeight += ROW_SPACING;
            }

            // Add extra padding at the bottom
            totalHeight += 10;

            // Calculate max scroll
            int maxScroll = Math.max(0, totalHeight - entriesAreaHeight);

            // Update scroll offset with smoother scrolling
            scrollOffset -= (int) (amount * SCROLL_AMOUNT);

            // Ensure scroll offset stays within bounds
            scrollOffset = Math.max(0, Math.min(scrollOffset, maxScroll));

            return true;
        }

        return super.mouseScrolled(mouseX, mouseY, amount);
    }

    @Override
    public void close() {
        this.client.setScreen(parent);
    }

    /**
     * Draws the "More Info" button for an error entry.
     *
     * @param context The draw context
     * @param entry The error entry
     * @param contentX The x position of the content area
     * @param contentWidth The width of the content area
     * @param entryY The y position of the entry
     * @param entryHeight The height of the entry
     */
    private void drawMoreInfoButton(DrawContext context, ErrorEntry entry, int contentX, int contentWidth, int entryY, int entryHeight) {
        int buttonY = entryY + entryHeight - 25;
        int buttonX = contentX + contentWidth - 110;
        int buttonWidth = 100;
        int buttonHeight = 20;

        // Draw button with mod styling
        drawModernButton(context, buttonX, buttonY, buttonWidth, buttonHeight, 0xFF2196F3, false, true);

        // Draw button text
        context.drawCenteredTextWithShadow(this.textRenderer, "More Info", buttonX + buttonWidth / 2, buttonY + 6, 0xFFFFFF);

        // Store button position for click handling
        entry.moreInfoButtonX = buttonX;
        entry.moreInfoButtonY = buttonY;
        entry.moreInfoButtonWidth = buttonWidth;
        entry.moreInfoButtonHeight = buttonHeight;
    }

    /**
     * Draws a modern button with the mod's styling.
     *
     * @param context The draw context
     * @param x The x position
     * @param y The y position
     * @param width The width
     * @param height The height
     * @param color The base color
     * @param isHovered Whether the button is hovered
     * @param isActive Whether the button is active
     */
    private void drawModernButton(DrawContext context, int x, int y, int width, int height, int color, boolean isHovered, boolean isActive) {
        // Extract RGB components
        int r = (color >> 16) & 0xFF;
        int g = (color >> 8) & 0xFF;
        int b = color & 0xFF;

        // Adjust colors based on state
        if (!isActive) {
            // Desaturate and darken for inactive buttons
            int avg = (r + g + b) / 3;
            r = (r + avg) / 2;
            g = (g + avg) / 2;
            b = (b + avg) / 2;
            r = r * 3/4;
            g = g * 3/4;
            b = b * 3/4;
        } else if (isHovered) {
            // Brighten for hover state
            r = Math.min(255, r + 30);
            g = Math.min(255, g + 30);
            b = Math.min(255, b + 30);
        }

        // Reconstruct color with full alpha
        int bgColor = 0xFF000000 | (r << 16) | (g << 8) | b;

        // Draw button background
        context.fill(x, y, x + width, y + height, bgColor);

        // Draw button border
        context.drawBorder(x, y, width, height, 0x60FFFFFF);

        // Draw subtle glass highlight at the top
        context.fill(x + 1, y + 1, x + width - 1, y + 2, 0x20FFFFFF);
        context.fill(x + 1, y + 1, x + 2, y + height - 1, 0x20FFFFFF);

        // Draw subtle shadow at the bottom
        context.fill(x + 1, y + height - 2, x + width - 1, y + height - 1, 0x20000000);
        context.fill(x + width - 2, y + 1, x + width - 1, y + height - 1, 0x20000000);
    }

    /**
     * Draws a rounded rectangle.
     *
     * @param context The draw context
     * @param x The x position
     * @param y The y position
     * @param width The width
     * @param height The height
     * @param color The color
     */
    private void drawRoundedRect(DrawContext context, int x, int y, int width, int height, int color) {
        // Draw main rectangle
        context.fill(x, y, x + width, y + height, color);
    }
}
