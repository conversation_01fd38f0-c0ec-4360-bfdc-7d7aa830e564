package com.pokecobble.town.gui;

import com.pokecobble.town.claim.ClaimTool;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.text.Text;

/**
 * A confirmation screen for saving or discarding selected chunks.
 */
public class ConfirmationScreen extends Screen {
    private final Screen parent;
    private final String title;
    private final String message;
    private final Runnable onConfirm;
    private final Runnable onCancel;

    // UI elements
    private ButtonWidget confirmButton;
    private ButtonWidget cancelButton;

    // Colors
    private static final int BACKGROUND_COLOR = 0xFF101010; // Solid dark background
    private static final int PANEL_COLOR = 0xFF202030; // Solid panel background
    private static final int BORDER_COLOR = 0xFF5555FF; // Blue border
    private static final int TEXT_COLOR = 0xFFFFFFFF; // White text
    private static final int CONFIRM_BUTTON_COLOR = 0xFF55AA55; // Green confirm button
    private static final int CANCEL_BUTTON_COLOR = 0xFFAA5555; // Red cancel button

    public ConfirmationScreen(Screen parent, String title, String message, Runnable onConfirm, Runnable onCancel) {
        super(Text.literal(title));
        this.parent = parent;
        this.title = title;
        this.message = message;
        this.onConfirm = onConfirm;
        this.onCancel = onCancel;
    }

    @Override
    protected void init() {
        super.init();

        int centerX = this.width / 2;
        int centerY = this.height / 2;

        // Create buttons
        int buttonWidth = 100;
        int buttonHeight = 20;
        int buttonSpacing = 10;

        // Confirm button (Yes)
        confirmButton = ButtonWidget.builder(Text.literal("Yes"), button -> {
            if (onConfirm != null) {
                onConfirm.run();
            }
            this.close();
        })
        .dimensions(centerX - buttonWidth - buttonSpacing/2, centerY + 20, buttonWidth, buttonHeight)
        .build();

        // Cancel button (No)
        cancelButton = ButtonWidget.builder(Text.literal("No"), button -> {
            if (onCancel != null) {
                onCancel.run();
            }
            this.close();
        })
        .dimensions(centerX + buttonSpacing/2, centerY + 20, buttonWidth, buttonHeight)
        .build();

        // Add buttons to the screen
        this.addDrawableChild(confirmButton);
        this.addDrawableChild(cancelButton);
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw a solid dark background over the entire screen
        context.fill(0, 0, this.width, this.height, BACKGROUND_COLOR);

        // Draw a solid panel
        int panelWidth = 300;
        int panelHeight = 120;
        int panelX = (this.width - panelWidth) / 2;
        int panelY = (this.height - panelHeight) / 2;

        // Draw panel background
        context.fill(panelX, panelY, panelX + panelWidth, panelY + panelHeight, PANEL_COLOR);

        // Draw panel border
        context.drawBorder(panelX, panelY, panelWidth, panelHeight, BORDER_COLOR);

        // Draw title
        context.drawCenteredTextWithShadow(this.textRenderer, this.title, this.width / 2, panelY + 10, TEXT_COLOR);

        // Draw message
        context.drawCenteredTextWithShadow(this.textRenderer, this.message, this.width / 2, panelY + 30, TEXT_COLOR);

        // Draw buttons with custom colors
        super.render(context, mouseX, mouseY, delta);

        // Draw button overlays for custom colors
        drawButtonOverlay(context, confirmButton, CONFIRM_BUTTON_COLOR);
        drawButtonOverlay(context, cancelButton, CANCEL_BUTTON_COLOR);
    }

    /**
     * Draws a colored overlay on a button to customize its appearance.
     */
    private void drawButtonOverlay(DrawContext context, ButtonWidget button, int color) {
        int alpha = button.isHovered() ? 0xDD : 0xAA; // More opaque when hovered
        int overlayColor = (alpha << 24) | (color & 0x00FFFFFF);
        context.fill(button.getX(), button.getY(), button.getX() + button.getWidth(), button.getY() + button.getHeight(), overlayColor);

        // Re-draw the button text
        int textX = button.getX() + (button.getWidth() - this.textRenderer.getWidth(button.getMessage())) / 2;
        int textY = button.getY() + (button.getHeight() - 8) / 2;
        context.drawTextWithShadow(this.textRenderer, button.getMessage(), textX, textY, 0xFFFFFFFF);
    }

    @Override
    public void close() {
        MinecraftClient.getInstance().setScreen(parent);
    }

    /**
     * Handles key presses in the confirmation screen.
     * This is used to properly handle the ESC key to close the confirmation screen.
     */
    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        // Handle ESC key to close the screen (equivalent to clicking No/Cancel)
        if (keyCode == org.lwjgl.glfw.GLFW.GLFW_KEY_ESCAPE) {
            // Run the cancel action
            if (onCancel != null) {
                onCancel.run();
            }
            // Close this screen
            this.close();
            return true;
        }

        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    @Override
    public boolean shouldPause() {
        return false;
    }
}
