package com.pokecobble.town.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.logging.ErrorLogger.ErrorEntry;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import java.util.ArrayList;
import java.util.List;

/**
 * Screen for displaying detailed information about an error.
 */
public class ErrorDetailScreen extends Screen {
    private final Screen parent;
    private final ErrorEntry error;

    // Panel dimensions
    private int panelWidth = 600;
    private int panelHeight = 400;
    private int leftX;
    private int topY;

    // UI colors
    private static final int BACKGROUND_COLOR = 0xCC000000; // Semi-transparent black
    private static final int PANEL_COLOR_TOP = 0xD0101010;  // Dark gradient top
    private static final int PANEL_COLOR_BOTTOM = 0xD0202030; // Dark gradient bottom
    private static final int HEADER_COLOR = 0xFF303050;     // Header background
    private static final int CONTENT_BG_COLOR = 0x30000000; // Content area background

    // Scrolling
    private int scrollOffset = 0;
    private static final int SCROLL_AMOUNT = 15;

    // Buttons
    private ButtonWidget copyButton;
    private ButtonWidget closeButton;

    /**
     * Creates a new error detail screen.
     *
     * @param parent The parent screen
     * @param error The error entry to display
     */
    public ErrorDetailScreen(Screen parent, ErrorEntry error) {
        super(Text.literal("Error Details"));
        this.parent = parent;
        this.error = error;
    }

    @Override
    protected void init() {
        super.init();

        // Calculate panel dimensions
        panelWidth = Math.min(width - 40, 600);
        panelHeight = height - 40;

        // Calculate positions
        leftX = (width - panelWidth) / 2;
        topY = (height - panelHeight) / 2;

        // Add copy button
        copyButton = ButtonWidget.builder(Text.literal("Copy to Clipboard"), button -> {
            copyErrorToClipboard();
        })
        .dimensions(leftX + 10, topY + panelHeight - 30, 150, 20)
        .build();
        this.addDrawableChild(copyButton);

        // Add close button
        closeButton = ButtonWidget.builder(Text.literal("Close"), button -> {
            this.client.setScreen(parent);
        })
        .dimensions(leftX + panelWidth - 110, topY + panelHeight - 30, 100, 20)
        .build();
        this.addDrawableChild(closeButton);
    }

    /**
     * Copies the error details to the clipboard.
     */
    private void copyErrorToClipboard() {
        try {
            // Get the full text representation of the error
            String fullText = error.getFullText();

            // Copy to clipboard
            MinecraftClient.getInstance().keyboard.setClipboard(fullText);

            // Show success message
            showMessage("Copied to clipboard!", Formatting.GREEN);
        } catch (Exception e) {
            // Show error message
            showMessage("Failed to copy: " + e.getMessage(), Formatting.RED);
            Pokecobbleclaim.LOGGER.error("Failed to copy error to clipboard", e);
        }
    }

    /**
     * Shows a message on the screen.
     *
     * @param message The message to show
     * @param formatting The formatting to apply
     */
    private void showMessage(String message, Formatting formatting) {
        // Use the notification system if available
        try {
            com.pokecobble.town.client.NotificationRenderer.addNotification(message);
        } catch (Exception e) {
            // Fallback to console logging
            Pokecobbleclaim.LOGGER.info(message);
        }
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Render dark background
        this.renderBackground(context);

        // Draw panel background with gradient
        context.fillGradient(leftX, topY, leftX + panelWidth, topY + panelHeight, PANEL_COLOR_TOP, PANEL_COLOR_BOTTOM);

        // Draw header
        context.fill(leftX, topY, leftX + panelWidth, topY + 40, HEADER_COLOR);

        // Draw title
        context.drawCenteredTextWithShadow(this.textRenderer, this.title, leftX + panelWidth / 2, topY + 10, 0xFFFFFF);

        // Draw error ID
        String idText = "Error ID: " + error.getId();
        context.drawTextWithShadow(this.textRenderer, idText, leftX + 10, topY + 25, 0xAAAAAA);

        // Draw content area
        int contentX = leftX + 10;
        int contentY = topY + 50;
        int contentWidth = panelWidth - 20;
        int contentHeight = panelHeight - 90;

        // Draw content background
        context.fill(contentX, contentY, contentX + contentWidth, contentY + contentHeight, CONTENT_BG_COLOR);

        // Apply scissor to clip content to visible area
        context.enableScissor(
            contentX,
            contentY,
            contentX + contentWidth,
            contentY + contentHeight
        );

        // Draw error details
        int textY = contentY + 10 - scrollOffset;
        int textX = contentX + 10;
        int textWidth = contentWidth - 20;

        // Draw timestamp
        String timestampText = "Timestamp: " + error.getFormattedTimestamp();
        context.drawTextWithShadow(this.textRenderer, timestampText, textX, textY, 0xFFFFFF);
        textY += 15;

        // Draw severity
        String severityText = "Severity: " + error.getSeverity().getName();
        context.drawTextWithShadow(this.textRenderer, severityText, textX, textY, error.getSeverity().getColor());
        textY += 15;

        // Draw source
        String sourceText = "Source: " + error.getSource();
        context.drawTextWithShadow(this.textRenderer, sourceText, textX, textY, 0xFFFFFF);
        textY += 15;

        // Draw player info if available
        if (error.hasPlayerInfo()) {
            StringBuilder playerText = new StringBuilder("Player: ");
            if (error.getPlayerName() != null) {
                playerText.append(error.getPlayerName());
            }
            if (error.getPlayerUuid() != null) {
                playerText.append(" (").append(error.getPlayerUuid()).append(")");
            }
            context.drawTextWithShadow(this.textRenderer, playerText.toString(), textX, textY, 0xFFFFAA);
            textY += 15;
        }

        // Draw message
        String messageLabel = "Message:";
        context.drawTextWithShadow(this.textRenderer, messageLabel, textX, textY, 0xFFFFFF);
        textY += 15;

        // Draw message text with wrapping
        String message = error.getMessage();
        for (String line : wrapText(message, textWidth)) {
            context.drawTextWithShadow(this.textRenderer, line, textX + 10, textY, 0xFFFFFF);
            textY += 12;
        }
        textY += 5;

        // Draw exception message if available
        if (error.getExceptionMessage() != null) {
            String exceptionLabel = "Exception:";
            context.drawTextWithShadow(this.textRenderer, exceptionLabel, textX, textY, 0xFF5555);
            textY += 15;

            // Draw exception text with wrapping
            for (String line : wrapText(error.getExceptionMessage(), textWidth)) {
                context.drawTextWithShadow(this.textRenderer, line, textX + 10, textY, 0xFF5555);
                textY += 12;
            }
            textY += 5;
        }

        // Draw stack trace if available
        if (error.getStackTrace() != null) {
            String stackTraceLabel = "Stack Trace:";
            context.drawTextWithShadow(this.textRenderer, stackTraceLabel, textX, textY, 0xAAAAAA);
            textY += 15;

            // Draw stack trace lines
            String[] lines = error.getStackTrace().split("\n");
            for (String line : lines) {
                context.drawTextWithShadow(this.textRenderer, line, textX + 10, textY, 0xAAAAAA);
                textY += 12;
            }
        }

        // Calculate total content height
        int totalHeight = textY - (contentY - scrollOffset);

        // Draw scrollbar if needed
        int maxScroll = Math.max(0, totalHeight - contentHeight);
        if (maxScroll > 0) {
            // Draw scrollbar track
            context.fill(contentX + contentWidth - 8, contentY, contentX + contentWidth - 4, contentY + contentHeight, 0x20FFFFFF);

            // Calculate scrollbar height and position
            int scrollbarHeight = Math.max(40, contentHeight * contentHeight / (totalHeight + contentHeight));
            float scrollRatio = (float)scrollOffset / maxScroll;
            int scrollbarY = contentY + (int)((contentHeight - scrollbarHeight) * scrollRatio);

            // Ensure scrollbar doesn't go out of bounds
            scrollbarY = Math.max(contentY, Math.min(scrollbarY, contentY + contentHeight - scrollbarHeight));

            // Draw scrollbar handle
            drawRoundedRect(context, contentX + contentWidth - 8, scrollbarY, 4, scrollbarHeight, 0xC0FFFFFF);
        }

        context.disableScissor();

        // Draw super (buttons, etc.)
        super.render(context, mouseX, mouseY, delta);
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        // Handle scrolling in the content area
        int contentX = leftX + 10;
        int contentY = topY + 50;
        int contentWidth = panelWidth - 20;
        int contentHeight = panelHeight - 90;

        // Check if mouse is in the content area
        if (mouseX >= contentX && mouseX <= contentX + contentWidth &&
            mouseY >= contentY && mouseY <= contentY + contentHeight) {

            // Update scroll offset with smoother scrolling
            scrollOffset -= (int) (amount * SCROLL_AMOUNT);

            // Ensure scroll offset stays within bounds
            scrollOffset = Math.max(0, scrollOffset);

            return true;
        }

        return super.mouseScrolled(mouseX, mouseY, amount);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Check for clicks on scrollbar
        int contentX = leftX + 10;
        int contentY = topY + 50;
        int contentWidth = panelWidth - 20;
        int contentHeight = panelHeight - 90;

        if (button == 0 && mouseX >= contentX + contentWidth - 8 && mouseX <= contentX + contentWidth - 4 &&
            mouseY >= contentY && mouseY <= contentY + contentHeight) {

            // Calculate total content height (approximate)
            int totalHeight = 1000; // Just a large value to ensure scrolling works

            // Calculate max scroll
            int maxScroll = Math.max(0, totalHeight - contentHeight);

            if (maxScroll > 0) {
                // Calculate new scroll position based on click position
                float clickPosition = (float)(mouseY - contentY) / contentHeight;
                scrollOffset = Math.round(clickPosition * maxScroll);
                scrollOffset = Math.max(0, Math.min(scrollOffset, maxScroll));
                return true;
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public void close() {
        this.client.setScreen(parent);
    }

    /**
     * Wraps text to fit within a specified width.
     *
     * @param text The text to wrap
     * @param width The maximum width in pixels
     * @return An array of wrapped text lines
     */
    private String[] wrapText(String text, int width) {
        if (text == null || text.isEmpty()) {
            return new String[0];
        }

        // Split by existing line breaks first
        String[] lines = text.split("\n");
        List<String> result = new ArrayList<>();

        for (String line : lines) {
            // If the line is already short enough, add it as is
            if (this.textRenderer.getWidth(line) <= width) {
                result.add(line);
                continue;
            }

            // Otherwise, wrap the line
            StringBuilder currentLine = new StringBuilder();
            String[] words = line.split(" ");

            for (String word : words) {
                // Check if adding this word would exceed the width
                String testLine = currentLine.toString() + word + " ";
                if (this.textRenderer.getWidth(testLine) > width) {
                    // If the current line is not empty, add it to the result
                    if (currentLine.length() > 0) {
                        result.add(currentLine.toString().trim());
                        currentLine = new StringBuilder();
                    }

                    // If the word itself is too long, split it
                    if (this.textRenderer.getWidth(word) > width) {
                        // Add as much of the word as possible
                        int i = 0;
                        while (i < word.length()) {
                            StringBuilder partialWord = new StringBuilder();
                            while (i < word.length() && this.textRenderer.getWidth(partialWord.toString() + word.charAt(i)) <= width) {
                                partialWord.append(word.charAt(i));
                                i++;
                            }
                            result.add(partialWord.toString());
                        }
                    } else {
                        // Add the word to the new line
                        currentLine.append(word).append(" ");
                    }
                } else {
                    // Add the word to the current line
                    currentLine.append(word).append(" ");
                }
            }

            // Add the last line if not empty
            if (currentLine.length() > 0) {
                result.add(currentLine.toString().trim());
            }
        }

        return result.toArray(new String[0]);
    }

    /**
     * Draws a rounded rectangle.
     *
     * @param context The draw context
     * @param x The x position
     * @param y The y position
     * @param width The width
     * @param height The height
     * @param color The color
     */
    private void drawRoundedRect(DrawContext context, int x, int y, int width, int height, int color) {
        // Draw main rectangle
        context.fill(x, y, x + width, y + height, color);
    }
}
