package com.pokecobble.town.gui;

import com.pokecobble.town.Town;
import com.pokecobble.town.sound.SoundUtil;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;
import org.lwjgl.glfw.GLFW;

/**
 * Compact, professional screen for displaying town level benefits.
 * Matches the style of TownContributeScreen.
 */
public class TownBenefitsScreen extends Screen {
    private final Screen parent;
    private final Town town;

    // Scrolling
    private int scrollOffset = 0;
    private static final int MAX_SCROLL_SPEED = 10;

    // Panel dimensions - compact size
    private int panelWidth;
    private int panelHeight;
    private int leftX;
    private int topY;

    // Colors
    private static final int BACKGROUND_COLOR = 0xE0101010;
    private static final int HEADER_COLOR = 0xFF202020;

    /**
     * Creates a new town benefits screen.
     *
     * @param parent The parent screen
     * @param town The town
     */
    public TownBenefitsScreen(Screen parent, Town town) {
        super(Text.literal("Town Benefits"));
        this.parent = parent;
        this.town = town;
    }

    @Override
    protected void init() {
        super.init();

        // Calculate panel dimensions - compact size
        panelWidth = Math.min(320, width - 40);
        panelHeight = Math.min(300, height - 40);
        leftX = (width - panelWidth) / 2;
        topY = (height - panelHeight) / 2;
    }

    /**
     * Plays the button click sound.
     */
    private void playClickSound() {
        SoundUtil.playButtonClickSound();
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw dark background
        this.renderBackground(context);

        // Draw panel background
        context.fill(leftX, topY, leftX + panelWidth, topY + panelHeight, BACKGROUND_COLOR);

        // Draw header
        int headerHeight = 24;
        context.fill(leftX, topY, leftX + panelWidth, topY + headerHeight, HEADER_COLOR);

        // Draw title
        context.drawCenteredTextWithShadow(this.textRenderer, Text.literal("Town Level Benefits").formatted(Formatting.BOLD),
            leftX + panelWidth / 2, topY + 8, 0xFFFFFF);

        // Draw content area
        int contentX = leftX + 10;
        int contentY = topY + headerHeight + 8;
        int contentWidth = panelWidth - 20;

        // Get town level (for demo purposes)
        int townLevel = 3; // Example level

        // Draw current level
        context.drawTextWithShadow(this.textRenderer, "Current Level: " + townLevel,
            contentX, contentY, 0xFFFFFF);

        // Draw scrollable content area
        int scrollAreaX = contentX;
        int scrollAreaY = contentY + 20;
        int scrollAreaWidth = contentWidth;
        int scrollAreaHeight = panelHeight - headerHeight - 50;

        // Define all benefits (3 per level)
        String[][] allBenefits = {
            {"Basic town features", "Town chat", "Town spawn point"},
            {"Custom town banner", "Town welcome message", "Town shop discounts"},
            {"Town teleport point", "Town protection", "Town storage"},
            {"Custom building styles", "Town weather control", "Town farm boost"},
            {"Town aura effects", "Town flight", "Town special events"}
        };

        // Calculate total content height
        int rowHeight = 14;
        int levelSpacing = 8;
        int benefitSpacing = 4;
        int totalContentHeight = 0;

        for (int level = 0; level < allBenefits.length; level++) {
            totalContentHeight += rowHeight; // Level header
            totalContentHeight += (rowHeight + benefitSpacing) * allBenefits[level].length;
            totalContentHeight += levelSpacing; // Spacing between levels
        }

        // Apply scissor to clip content to visible area
        context.enableScissor(
            scrollAreaX,
            scrollAreaY,
            scrollAreaX + scrollAreaWidth,
            scrollAreaY + scrollAreaHeight
        );

        // Apply scroll offset
        int currentY = scrollAreaY - scrollOffset;

        // Draw level benefits
        for (int level = 0; level < allBenefits.length; level++) {
            // Calculate level section height
            int levelSectionHeight = rowHeight + (rowHeight + benefitSpacing) * allBenefits[level].length + levelSpacing;

            // Skip if completely outside visible area
            if (currentY + levelSectionHeight < scrollAreaY || currentY > scrollAreaY + scrollAreaHeight) {
                currentY += levelSectionHeight;
                continue;
            }

            // Draw level header
            boolean isUnlocked = level + 1 <= townLevel;
            int levelColor = isUnlocked ? 0x55FF55 : 0xFFFFAA00;
            String levelHeader = "Level " + (level + 1);

            context.drawTextWithShadow(this.textRenderer, Text.literal(levelHeader).formatted(Formatting.BOLD),
                scrollAreaX + 5, currentY, levelColor);

            // Draw unlocked/locked status
            String statusText = isUnlocked ? "(Unlocked)" : "(Locked)";
            int statusWidth = this.textRenderer.getWidth(statusText);
            context.drawTextWithShadow(this.textRenderer, statusText,
                scrollAreaX + scrollAreaWidth - statusWidth - 5, currentY, levelColor);

            currentY += rowHeight;

            // Draw benefits for this level
            String[] benefits = allBenefits[level];
            for (int i = 0; i < benefits.length; i++) {
                String prefix = isUnlocked ? "✓ " : "• ";
                context.drawTextWithShadow(this.textRenderer, prefix + benefits[i],
                    scrollAreaX + 15, currentY, isUnlocked ? 0xFFFFFF : 0xAAAAAA);

                currentY += rowHeight + benefitSpacing;
            }

            currentY += levelSpacing - benefitSpacing; // Adjust for last benefit spacing
        }

        context.disableScissor();

        // Draw scrollbar if needed
        if (totalContentHeight > scrollAreaHeight) {
            // Draw scrollbar track
            context.fill(scrollAreaX + scrollAreaWidth - 6, scrollAreaY,
                       scrollAreaX + scrollAreaWidth - 2, scrollAreaY + scrollAreaHeight, 0x20FFFFFF);

            // Calculate scrollbar height and position
            int scrollbarHeight = Math.max(30, scrollAreaHeight * scrollAreaHeight / totalContentHeight);
            float scrollRatio = (float)scrollOffset / (totalContentHeight - scrollAreaHeight);
            int scrollbarY = scrollAreaY + (int)((scrollAreaHeight - scrollbarHeight) * scrollRatio);

            // Draw scrollbar handle
            context.fill(scrollAreaX + scrollAreaWidth - 6, scrollbarY,
                       scrollAreaX + scrollAreaWidth - 2, scrollbarY + scrollbarHeight, 0xA0FFFFFF);

            // Add subtle 3D effect to scrollbar
            context.fill(scrollAreaX + scrollAreaWidth - 6, scrollbarY,
                       scrollAreaX + scrollAreaWidth - 2, scrollbarY + 1, 0x40FFFFFF);
            context.fill(scrollAreaX + scrollAreaWidth - 6, scrollbarY,
                       scrollAreaX + scrollAreaWidth - 5, scrollbarY + scrollbarHeight, 0x40FFFFFF);
        }

        // Draw back button
        int backButtonWidth = 60;
        int backButtonHeight = 16;
        int backButtonX = leftX + 10;
        int backButtonY = topY + panelHeight - backButtonHeight - 10;

        boolean backHovered = mouseX >= backButtonX && mouseX <= backButtonX + backButtonWidth &&
                             mouseY >= backButtonY && mouseY <= backButtonY + backButtonHeight;

        drawModernButton(context, backButtonX, backButtonY, backButtonWidth, backButtonHeight,
                       0xFF555555, backHovered, true);

        context.drawCenteredTextWithShadow(this.textRenderer, "Back",
            backButtonX + backButtonWidth / 2, backButtonY + 4, 0xFFFFFF);

        super.render(context, mouseX, mouseY, delta);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0) { // Left click
            // Calculate scroll area
            int contentX = leftX + 10;
            int headerHeight = 24;
            int contentY = topY + headerHeight + 8;
            int contentWidth = panelWidth - 20;

            int scrollAreaX = contentX;
            int scrollAreaY = contentY + 20;
            int scrollAreaWidth = contentWidth;
            int scrollAreaHeight = panelHeight - headerHeight - 50;

            // Check if clicked on scrollbar track
            if (mouseX >= scrollAreaX + scrollAreaWidth - 6 && mouseX <= scrollAreaX + scrollAreaWidth - 2 &&
                mouseY >= scrollAreaY && mouseY <= scrollAreaY + scrollAreaHeight) {

                // Calculate total content height
                int rowHeight = 14;
                int levelSpacing = 8;
                int benefitSpacing = 4;
                int totalContentHeight = 0;

                String[][] allBenefits = {
                    {"Basic town features", "Town chat", "Town spawn point"},
                    {"Custom town banner", "Town welcome message", "Town shop discounts"},
                    {"Town teleport point", "Town protection", "Town storage"},
                    {"Custom building styles", "Town weather control", "Town farm boost"},
                    {"Town aura effects", "Town flight", "Town special events"}
                };

                for (int level = 0; level < allBenefits.length; level++) {
                    totalContentHeight += rowHeight; // Level header
                    totalContentHeight += (rowHeight + benefitSpacing) * allBenefits[level].length;
                    totalContentHeight += levelSpacing; // Spacing between levels
                }

                // Calculate max scroll
                int maxScroll = Math.max(0, totalContentHeight - scrollAreaHeight);

                if (maxScroll > 0) {
                    // Calculate new scroll position based on click position
                    float clickPosition = (float)(mouseY - scrollAreaY) / scrollAreaHeight;
                    scrollOffset = Math.round(clickPosition * maxScroll);
                    scrollOffset = Math.max(0, Math.min(scrollOffset, maxScroll));
                    return true;
                }
            }

            // Check back button
            int backButtonWidth = 60;
            int backButtonHeight = 16;
            int backButtonX = leftX + 10;
            int backButtonY = topY + panelHeight - backButtonHeight - 10;

            if (mouseX >= backButtonX && mouseX <= backButtonX + backButtonWidth &&
                mouseY >= backButtonY && mouseY <= backButtonY + backButtonHeight) {
                // Play click sound
                playClickSound();

                // Return to parent screen
                this.client.setScreen(parent);
                return true;
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        // Calculate scroll area
        int contentX = leftX + 10;
        int headerHeight = 24;
        int contentY = topY + headerHeight + 8;
        int contentWidth = panelWidth - 20;

        int scrollAreaX = contentX;
        int scrollAreaY = contentY + 20;
        int scrollAreaWidth = contentWidth;
        int scrollAreaHeight = panelHeight - headerHeight - 50;

        // Only scroll if mouse is over scroll area
        if (mouseX >= scrollAreaX && mouseX <= scrollAreaX + scrollAreaWidth &&
            mouseY >= scrollAreaY && mouseY <= scrollAreaY + scrollAreaHeight) {

            // Calculate total content height
            int rowHeight = 14;
            int levelSpacing = 8;
            int benefitSpacing = 4;
            int totalContentHeight = 0;

            String[][] allBenefits = {
                {"Basic town features", "Town chat", "Town spawn point"},
                {"Custom town banner", "Town welcome message", "Town shop discounts"},
                {"Town teleport point", "Town protection", "Town storage"},
                {"Custom building styles", "Town weather control", "Town farm boost"},
                {"Town aura effects", "Town flight", "Town special events"}
            };

            for (int level = 0; level < allBenefits.length; level++) {
                totalContentHeight += rowHeight; // Level header
                totalContentHeight += (rowHeight + benefitSpacing) * allBenefits[level].length;
                totalContentHeight += levelSpacing; // Spacing between levels
            }

            // Apply smoother scrolling
            int scrollAmount = (int)(amount * 15); // Increased for more responsive scrolling
            scrollOffset = Math.max(0, Math.min(totalContentHeight - scrollAreaHeight, scrollOffset - scrollAmount));

            return true;
        }

        return super.mouseScrolled(mouseX, mouseY, amount);
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        if (keyCode == GLFW.GLFW_KEY_ESCAPE) {
            // Return to parent screen
            this.client.setScreen(parent);
            return true;
        }

        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    /**
     * Draws a modern button with subtle 3D effect.
     */
    private void drawModernButton(DrawContext context, int x, int y, int width, int height, int color, boolean isHovered, boolean isActive) {
        // Extract RGB components
        int r = (color >> 16) & 0xFF;
        int g = (color >> 8) & 0xFF;
        int b = color & 0xFF;

        // Adjust colors based on state
        if (!isActive) {
            // Desaturate and darken for inactive buttons
            int avg = (r + g + b) / 3;
            r = (r + avg) / 2;
            g = (g + avg) / 2;
            b = (b + avg) / 2;
            r = r * 3/4;
            g = g * 3/4;
            b = b * 3/4;
        } else if (isHovered) {
            // Brighten for hover state
            r = Math.min(255, r + 30);
            g = Math.min(255, g + 30);
            b = Math.min(255, b + 30);
        }

        // Reconstruct color with alpha
        int baseColor = 0xFF000000 | (r << 16) | (g << 8) | b;

        // Draw button background
        context.fill(x, y, x + width, y + height, baseColor);

        // Draw subtle glass effect
        context.fill(x, y, x + width, y + 1, 0x20FFFFFF);
        context.fill(x, y, x + 1, y + height, 0x20FFFFFF);
        context.fill(x, y + height - 1, x + width, y + height, 0x20000000);
        context.fill(x + width - 1, y, x + width, y + height, 0x20000000);
    }
}
