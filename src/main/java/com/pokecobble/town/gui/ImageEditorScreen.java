package com.pokecobble.town.gui;

import com.pokecobble.town.Town;
import com.pokecobble.town.network.town.TownImageSynchronizer;
import com.pokecobble.town.util.TownImageUtil;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.SliderWidget;
import net.minecraft.text.Text;
import net.minecraft.text.Style;
import net.minecraft.util.Formatting;
import net.minecraft.util.Identifier;
import net.minecraft.util.math.MathHelper;
import java.util.UUID;

/**
 * Screen for editing town images (resize, move, etc.)
 */
public class ImageEditorScreen extends Screen {
    private final Screen parent;
    private final Town town;
    private final String imageName;
    private Identifier imageId;

    // Image manipulation parameters
    private float scale = 1.0f;
    private int offsetX = 0;
    private int offsetY = 0;
    private boolean isDragging = false;
    private int lastMouseX, lastMouseY;

    // Constants
    private static final int IMAGE_DISPLAY_SIZE = 160; // Smaller image size
    private static final int BORDER_SIZE = 10;

    // UI colors
    private static final int BACKGROUND_COLOR = 0xCC000000;
    private static final int HEADER_COLOR = 0xFF1E88E5;
    private static final int BORDER_COLOR = 0xFF42A5F5;
    private static final int SLIDER_BG_COLOR = 0x55FFFFFF;
    private static final int SLIDER_HANDLE_COLOR = 0xFFFFFFFF;

    public ImageEditorScreen(Screen parent, Town town, String imageName) {
        super(Text.translatable("screen.pokecobbleclaim.image_editor"));
        this.parent = parent;

        // If town is null, try to get the player's town
        if (town == null) {
            com.pokecobble.Pokecobbleclaim.LOGGER.warn("Town passed to ImageEditorScreen is null, attempting to get player's town");
            // Get the player's town as a fallback
            if (net.minecraft.client.MinecraftClient.getInstance().player != null) {
                UUID playerUUID = net.minecraft.client.MinecraftClient.getInstance().player.getUuid();
                town = com.pokecobble.town.TownManager.getInstance().getPlayerTown(playerUUID);
                if (town != null) {
                    com.pokecobble.Pokecobbleclaim.LOGGER.info("Successfully retrieved player's town: " + town.getName());
                } else {
                    com.pokecobble.Pokecobbleclaim.LOGGER.error("Failed to retrieve player's town");
                }
            }
        }

        this.town = town;
        this.imageName = imageName;

        // Load the image (safely handle null town)
        if (town != null) {
            this.imageId = TownImageUtil.getImageIdentifier(town, imageName);
        } else {
            this.imageId = null;
        }
    }

    @Override
    protected void init() {
        super.init();

        // Request the latest town image data from the server
        if (town != null) {
            try {
                TownImageSynchronizer.requestTownImageUpdate(town.getId());
            } catch (Exception e) {
                com.pokecobble.Pokecobbleclaim.LOGGER.error("Error requesting town image update: " + e.getMessage(), e);
            }
        } else {
            com.pokecobble.Pokecobbleclaim.LOGGER.warn("Town is null in ImageEditorScreen.init()");
        }

        int buttonWidth = 80;
        int buttonHeight = 20;
        int centerX = this.width / 2;
        int bottomY = this.height - 20; // 20px from bottom of screen

        // Save button - positioned in bottom right corner
        this.addDrawableChild(ButtonWidget.builder(Text.literal("Save"), button -> {
            try {
                // Check if town is null
                if (town == null) {
                    com.pokecobble.Pokecobbleclaim.LOGGER.error("Cannot save image settings: town is null");
                    // Show an error message to the user
                    this.client.player.sendMessage(Text.literal("Error: Could not save image settings. Town data is missing.").setStyle(Style.EMPTY.withColor(Formatting.RED)), false);
                    this.close();
                    return;
                }

                // Save the image manipulation parameters
                TownImageUtil.saveImageSettings(town, imageName, scale, offsetX, offsetY);

                // Apply changes immediately on the client side
                TownImageUtil.applyImageSettingsLocally(town, imageName, scale, offsetX, offsetY);

                // Request synchronization with the server
                TownImageSynchronizer.requestTownImageUpdate(town.getId());

                // Log the change
                com.pokecobble.Pokecobbleclaim.LOGGER.info("Applied image settings locally: " + imageName +
                    " scale=" + scale + " offset=" + offsetX + "," + offsetY);
            } catch (Exception e) {
                // Log the error
                com.pokecobble.Pokecobbleclaim.LOGGER.error("Error saving image settings: " + e.getMessage(), e);
                // Show an error message to the user
                if (this.client.player != null) {
                    this.client.player.sendMessage(Text.literal("Error: Could not save image settings. " + e.getMessage()).setStyle(Style.EMPTY.withColor(Formatting.RED)), false);
                }
            }

            this.close();
        }).dimensions(this.width - buttonWidth - 10, bottomY, buttonWidth, buttonHeight).build());

        // Scale slider - centered at the bottom
        int sliderWidth = 180;
        this.addDrawableChild(new SliderWidget(centerX - sliderWidth / 2, bottomY, sliderWidth, 20,
                Text.literal("Scale: " + String.format("%.1f", scale)), scale / 2.0f) {
            @Override
            protected void updateMessage() {
                setMessage(Text.literal("Scale: " + String.format("%.1f", scale)));
            }

            @Override
            protected void applyValue() {
                scale = (float) (this.value * 2.0f);
                updateMessage();
            }

            @Override
            public void renderButton(DrawContext context, int mouseX, int mouseY, float delta) {
                // Custom slider rendering to match mod style
                int sliderX = this.getX();
                int sliderY = this.getY();
                int sliderWidth = this.getWidth();
                int sliderHeight = this.getHeight();

                // Draw slider background
                context.fill(sliderX, sliderY + sliderHeight / 2 - 1, sliderX + sliderWidth, sliderY + sliderHeight / 2 + 1, SLIDER_BG_COLOR);

                // Draw slider handle
                int handleX = sliderX + (int)(this.value * (sliderWidth - 8));
                context.fill(handleX, sliderY, handleX + 8, sliderY + sliderHeight, SLIDER_HANDLE_COLOR);

                // Draw slider text
                int textColor = this.isHovered() ? 0xFFFFFF : 0xE0E0E0;
                context.drawCenteredTextWithShadow(ImageEditorScreen.this.textRenderer, this.getMessage(), sliderX + sliderWidth / 2, sliderY + (sliderHeight - 8) / 2, textColor);
            }
        });

        // Load existing settings if available
        TownImageUtil.ImageSettings settings = TownImageUtil.getImageSettings(town, imageName);
        if (settings != null) {
            scale = settings.scale;
            offsetX = settings.offsetX;
            offsetY = settings.offsetY;
        }
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw full screen background
        context.fill(0, 0, width, height, BACKGROUND_COLOR);

        // Draw border at the edges of the screen
        context.fill(0, 0, width, 2, BORDER_COLOR); // Top
        context.fill(0, height - 2, width, height, BORDER_COLOR); // Bottom
        context.fill(0, 0, 2, height, BORDER_COLOR); // Left
        context.fill(width - 2, 0, width, height, BORDER_COLOR); // Right

        // Draw header
        context.fill(0, 0, width, 30, HEADER_COLOR);

        // Draw title
        context.drawCenteredTextWithShadow(this.textRenderer,
                Text.literal("Image Editor").setStyle(Style.EMPTY.withBold(true)),
                this.width / 2, 10, 0xFFFFFF);

        // Draw minimal instructions
        String instructions = "Drag: Move • Slider: Resize";
        context.drawCenteredTextWithShadow(this.textRenderer,
                Text.literal(instructions).setStyle(Style.EMPTY.withColor(Formatting.GRAY)),
                this.width / 2, 35, 0xAAAAAA);

        int centerX = this.width / 2;
        int centerY = this.height / 2 + 10; // Slightly below center

        // Calculate mask radius
        int maskRadius = IMAGE_DISPLAY_SIZE / 2;

        // Create a stencil for the circular mask
        // First, draw a filled circle to the stencil buffer
        drawFilledCircle(context, centerX, centerY, maskRadius);

        // Draw the image with transformations
        if (imageId != null) {
            try {
                // Calculate the actual image size based on scale
                int scaledSize = (int)(IMAGE_DISPLAY_SIZE * scale);

                // Calculate the position to center the image
                int imageX = centerX - scaledSize / 2 + offsetX;
                int imageY = centerY - scaledSize / 2 + offsetY;

                // Calculate the bounding box of the circle
                int left = centerX - maskRadius;
                int top = centerY - maskRadius;
                int right = centerX + maskRadius;
                int bottom = centerY + maskRadius;

                // Constrain image position to ensure it stays within the circle bounds
                int maxOffset = (int)(maskRadius * 0.7); // Allow some movement but not too much

                // Calculate the center of the image
                int imageCenterX = imageX + scaledSize / 2;
                int imageCenterY = imageY + scaledSize / 2;

                // Calculate the offset from the circle center
                int centerOffsetX = imageCenterX - centerX;
                int centerOffsetY = imageCenterY - centerY;

                // Calculate the distance from the circle center
                double distance = Math.sqrt(centerOffsetX * centerOffsetX + centerOffsetY * centerOffsetY);

                // If the image center is too far from the circle center, move it closer
                if (distance > maxOffset) {
                    double ratio = maxOffset / distance;
                    centerOffsetX = (int)(centerOffsetX * ratio);
                    centerOffsetY = (int)(centerOffsetY * ratio);

                    // Recalculate image position
                    imageCenterX = centerX + centerOffsetX;
                    imageCenterY = centerY + centerOffsetY;
                    imageX = imageCenterX - scaledSize / 2;
                    imageY = imageCenterY - scaledSize / 2;

                    // Update the offset values for dragging
                    offsetX = imageX - (centerX - scaledSize / 2);
                    offsetY = imageY - (centerY - scaledSize / 2);
                }

                // Enable scissor to the bounding box of the circle
                context.enableScissor(left, top, right, bottom);

                // Draw the image
                context.drawTexture(imageId, imageX, imageY, 0, 0, scaledSize, scaledSize, scaledSize, scaledSize);

                // Disable scissor
                context.disableScissor();
            } catch (Exception e) {
                // Draw placeholder if image can't be rendered
                drawPlaceholder(context, centerX, centerY, maskRadius, "Error");
            }
        } else {
            // Draw placeholder if image not found
            drawPlaceholder(context, centerX, centerY, maskRadius, "No Image");
        }

        // Draw circular mask outline on top of the image
        drawCircleOutline(context, centerX, centerY, maskRadius, BORDER_COLOR);

        super.render(context, mouseX, mouseY, delta);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0) { // Left click
            int centerX = this.width / 2;
            int centerY = this.height / 2 - 30;
            int maskRadius = IMAGE_DISPLAY_SIZE / 2;

            // Check if click is inside the image display area
            if (isPointInCircle((int)mouseX, (int)mouseY, centerX, centerY, maskRadius)) {
                isDragging = true;
                lastMouseX = (int)mouseX;
                lastMouseY = (int)mouseY;
                return true;
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        if (button == 0) {
            isDragging = false;
        }

        return super.mouseReleased(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        if (isDragging && button == 0) {
            // Update image position
            offsetX += (int)mouseX - lastMouseX;
            offsetY += (int)mouseY - lastMouseY;

            // Calculate the maximum allowed offset based on the image size and circle radius
            int scaledSize = (int)(IMAGE_DISPLAY_SIZE * scale);
            int maskRadius = IMAGE_DISPLAY_SIZE / 2;

            // Calculate the maximum offset that would keep the image within the circle
            // This ensures at least 1/4 of the image is always visible within the circle
            int maxOffsetX = Math.max(0, (scaledSize / 2) - maskRadius);
            int maxOffsetY = Math.max(0, (scaledSize / 2) - maskRadius);

            // Apply stricter limits for larger images to ensure they don't go too far outside the circle
            if (scale > 1.0f) {
                // For larger images, ensure at least 1/3 of the image is visible
                maxOffsetX = Math.min(maxOffsetX, maskRadius);
                maxOffsetY = Math.min(maxOffsetY, maskRadius);
            }

            // Clamp the offset values
            offsetX = MathHelper.clamp(offsetX, -maxOffsetX, maxOffsetX);
            offsetY = MathHelper.clamp(offsetY, -maxOffsetY, maxOffsetY);

            lastMouseX = (int)mouseX;
            lastMouseY = (int)mouseY;
            return true;
        }

        return super.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
    }

    @Override
    public void close() {
        this.client.setScreen(parent);
    }

    /**
     * Draws a circle outline.
     */
    private void drawCircleOutline(DrawContext context, int centerX, int centerY, int radius, int color) {
        // Draw a more efficient circle outline using the Bresenham algorithm
        int x = 0;
        int y = radius;
        int d = 3 - 2 * radius;

        while (y >= x) {
            // Draw 8 points for each step to complete the circle
            drawCirclePoints(context, centerX, centerY, x, y, color);

            x++;
            if (d > 0) {
                y--;
                d = d + 4 * (x - y) + 10;
            } else {
                d = d + 4 * x + 6;
            }
            drawCirclePoints(context, centerX, centerY, x, y, color);
        }
    }

    /**
     * Helper method to draw the 8 symmetric points of a circle.
     */
    private void drawCirclePoints(DrawContext context, int centerX, int centerY, int x, int y, int color) {
        // Draw the 8 symmetric points
        context.fill(centerX + x, centerY + y, centerX + x + 1, centerY + y + 1, color);
        context.fill(centerX - x, centerY + y, centerX - x + 1, centerY + y + 1, color);
        context.fill(centerX + x, centerY - y, centerX + x + 1, centerY - y + 1, color);
        context.fill(centerX - x, centerY - y, centerX - x + 1, centerY - y + 1, color);
        context.fill(centerX + y, centerY + x, centerX + y + 1, centerY + x + 1, color);
        context.fill(centerX - y, centerY + x, centerX - y + 1, centerY + x + 1, color);
        context.fill(centerX + y, centerY - x, centerX + y + 1, centerY - x + 1, color);
        context.fill(centerX - y, centerY - x, centerX - y + 1, centerY - x + 1, color);
    }

    /**
     * Draws a filled circle.
     * This version is used for both visual rendering and stencil buffer operations.
     */
    private void drawFilledCircle(DrawContext context, int centerX, int centerY, int radius) {
        // Draw a filled circle using horizontal lines
        for (int y = -radius; y <= radius; y++) {
            int x = (int) Math.sqrt(radius * radius - y * y);
            context.fill(centerX - x, centerY + y, centerX + x, centerY + y + 1, 0xFFFFFFFF); // Solid white
        }
    }

    /**
     * Checks if a point is inside a circle.
     */
    private boolean isPointInCircle(int pointX, int pointY, int circleX, int circleY, int radius) {
        int dx = pointX - circleX;
        int dy = pointY - circleY;
        return dx * dx + dy * dy <= radius * radius;
    }

    /**
     * Draws a placeholder for missing or error images.
     */
    private void drawPlaceholder(DrawContext context, int centerX, int centerY, int radius, String message) {
        // Draw a pattern to indicate missing texture
        context.enableScissor(
            centerX - radius,
            centerY - radius,
            centerX + radius,
            centerY + radius
        );

        // Draw simple pattern
        int size = radius * 2;
        int startX = centerX - radius;
        int startY = centerY - radius;

        // Fill with dark background
        context.fill(startX, startY, startX + size, startY + size, 0xFF333333);

        // Draw diagonal lines
        for (int i = -radius; i <= radius; i += 10) {
            context.fill(centerX + i, centerY - radius, centerX + i + 2, centerY + radius, 0xFF666666);
        }

        context.disableScissor();

        // Draw error message
        context.drawCenteredTextWithShadow(this.textRenderer,
                Text.literal(message).setStyle(Style.EMPTY.withColor(Formatting.RED)),
                centerX, centerY, 0xFFFFFF);
    }
}
