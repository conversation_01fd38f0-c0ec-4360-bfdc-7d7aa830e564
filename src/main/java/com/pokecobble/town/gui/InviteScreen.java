package com.pokecobble.town.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.network.town.InviteNetworkHandler;
import com.pokecobble.town.gui.NotificationManager;
import com.pokecobble.town.sound.SoundUtil;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.network.PlayerListEntry;
import net.minecraft.client.sound.PositionedSoundInstance;
import net.minecraft.sound.SoundEvents;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;
import net.minecraft.util.Identifier;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Screen for inviting players to a town.
 */
@Environment(EnvType.CLIENT)
public class InviteScreen extends Screen {
    private final Screen parent;
    private final Town town;

    // Panel dimensions
    private int panelWidth = 400;
    private int panelHeight = 300;
    private int leftX;
    private int topY;

    // Scrolling
    private int scrollOffset = 0;
    private static final int SCROLL_AMOUNT = 15;

    // Status message
    private Text statusText = Text.empty();
    private int statusColor = 0xFFFFFF;
    private int statusTimer = 0;

    // Player list
    private List<PlayerEntry> playerEntries = new ArrayList<>();

    // Constants for layout
    private static final int ENTRY_HEIGHT = 24;
    private static final int ENTRY_SPACING = 2;
    private static final int BUTTON_WIDTH = 60;
    private static final int BUTTON_HEIGHT = 18;

    // Network constants
    private static final Identifier INVITE_PACKET_ID = InviteNetworkHandler.INVITE_RESPONSE;

    /**
     * Creates a new invite screen.
     *
     * @param parent The parent screen
     * @param town The town to invite players to
     */
    public InviteScreen(Screen parent, Town town) {
        super(Text.literal("Invite Players"));
        this.parent = parent;
        this.town = town;
    }

    @Override
    protected void init() {
        super.init();

        // Calculate panel dimensions based on screen size
        panelWidth = Math.min(width - 20, 500);
        panelHeight = height - 20;
        leftX = (width - panelWidth) / 2;
        topY = 10;

        // Load player list
        loadPlayerList();

        // Register packet handler for invite response
        registerPacketHandler();
    }

    /**
     * Loads the list of players that can be invited.
     */
    private void loadPlayerList() {
        playerEntries.clear();

        // Check if we're in singleplayer mode
        boolean isSingleplayer = client != null && client.isInSingleplayer();

        // Get all online players from the player list
        if (client != null && client.getNetworkHandler() != null) {
            for (PlayerListEntry entry : client.getNetworkHandler().getPlayerList()) {
                UUID playerId = entry.getProfile().getId();
                String playerName = entry.getProfile().getName();

                // Skip players who are already in a town
                if (TownManager.getInstance().getPlayerTown(playerId) != null) {
                    continue;
                }

                // Skip the current player
                if (client.player != null && playerId.equals(client.player.getUuid())) {
                    continue;
                }

                playerEntries.add(new PlayerEntry(playerId, playerName));
            }
        }

        // Add dummy players for testing in singleplayer mode
        if (isSingleplayer || playerEntries.isEmpty()) {
            // List of dummy player names
            String[] dummyNames = {
                "Steve", "Alex", "Notch", "Jeb", "Dinnerbone",
                "Grumm", "Marc", "Searge", "Grum", "EvilSeph",
                "Vechs", "Dragoness", "Lydia", "Agnes", "Maria",
                "Vu", "Mojang", "Minecraft", "Creeper", "Enderman"
            };

            // Add dummy players
            for (String name : dummyNames) {
                playerEntries.add(new PlayerEntry(UUID.randomUUID(), name));
            }

            // Sort the list alphabetically
            playerEntries.sort((a, b) -> a.name.compareToIgnoreCase(b.name));
        }
    }

    /**
     * Registers the packet handler for invite responses.
     */
    private void registerPacketHandler() {
        // The packet handler is registered in InviteNetworkHandler
        // This method is kept for future extensions
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Render background
        this.renderBackground(context);

        // Draw panel background
        context.fill(leftX, topY, leftX + panelWidth, topY + panelHeight, 0xCC000000);

        // Draw panel border
        context.drawBorder(leftX, topY, panelWidth, panelHeight, 0xFF5D6CFF);

        // Draw title
        int titleX = leftX + 15;
        int titleY = topY + 10;
        context.drawTextWithShadow(this.textRenderer, Text.literal("Invite Players to " + town.getName()).formatted(Formatting.BOLD),
                titleX, titleY, 0xFFFFFF);

        // Draw content area
        int contentX = leftX + 10;
        int contentY = topY + 30;
        int contentWidth = panelWidth - 20;
        int contentHeight = panelHeight - 70;

        // Draw content area background
        context.fill(contentX, contentY, contentX + contentWidth, contentY + contentHeight, 0x40000000);

        // Draw player list header
        int headerY = contentY + 5;
        context.drawTextWithShadow(this.textRenderer, Text.literal("Player Name").formatted(Formatting.BOLD),
                contentX + 10, headerY, 0xFFFFFF);

        // Draw divider
        context.fill(contentX + 5, headerY + 15, contentX + contentWidth - 5, headerY + 16, 0x40FFFFFF);

        // Calculate visible area for player entries
        int entriesAreaY = headerY + 20;
        int entriesAreaHeight = contentHeight - 30;

        // Calculate total height of all entries
        int totalHeight = playerEntries.size() * (ENTRY_HEIGHT + ENTRY_SPACING);

        // Add extra padding at the bottom
        totalHeight += 10;

        // Calculate max scroll
        int maxScroll = Math.max(0, totalHeight - entriesAreaHeight);
        scrollOffset = Math.min(scrollOffset, maxScroll);

        // Draw scrollbar if needed
        if (maxScroll > 0) {
            // Draw scrollbar track
            context.fill(contentX + contentWidth - 8, entriesAreaY, contentX + contentWidth - 4,
                    entriesAreaY + entriesAreaHeight, 0x20FFFFFF);

            // Calculate scrollbar height and position
            int scrollbarHeight = Math.max(40, entriesAreaHeight * entriesAreaHeight / (totalHeight + entriesAreaHeight));
            float scrollRatio = (float)scrollOffset / maxScroll;
            int scrollbarY = entriesAreaY + (int)((entriesAreaHeight - scrollbarHeight) * scrollRatio);

            // Ensure scrollbar doesn't go out of bounds
            scrollbarY = Math.max(entriesAreaY, Math.min(scrollbarY, entriesAreaY + entriesAreaHeight - scrollbarHeight));

            // Draw scrollbar handle
            context.fill(contentX + contentWidth - 8, scrollbarY, contentX + contentWidth - 4,
                    scrollbarY + scrollbarHeight, 0xC0FFFFFF);
        }

        // Apply scissor to clip content to visible area
        context.enableScissor(
            contentX + 5,
            entriesAreaY,
            contentX + contentWidth - 5,
            entriesAreaY + entriesAreaHeight
        );

        // Draw player entries with scrolling
        int entryY = entriesAreaY - scrollOffset;

        for (PlayerEntry entry : playerEntries) {
            // Skip if entry is completely outside visible area
            if (entryY + ENTRY_HEIGHT < entriesAreaY || entryY > entriesAreaY + entriesAreaHeight) {
                entryY += ENTRY_HEIGHT + ENTRY_SPACING;
                continue;
            }

            // Draw entry background
            boolean isHovered = mouseX >= contentX + 5 && mouseX <= contentX + contentWidth - 5 &&
                               mouseY >= entryY && mouseY <= entryY + ENTRY_HEIGHT &&
                               mouseY >= entriesAreaY && mouseY <= entriesAreaY + entriesAreaHeight;

            int bgColor = isHovered ? 0x40FFFFFF : 0x20FFFFFF;
            context.fill(contentX + 5, entryY, contentX + contentWidth - 5, entryY + ENTRY_HEIGHT, bgColor);

            // Draw player name
            context.drawTextWithShadow(this.textRenderer, entry.name, contentX + 10, entryY + 7, 0xFFFFFF);

            // Draw invite button
            int buttonX = contentX + contentWidth - BUTTON_WIDTH - 15;
            int buttonY = entryY + 3;
            boolean buttonHovered = mouseX >= buttonX && mouseX <= buttonX + BUTTON_WIDTH &&
                                   mouseY >= buttonY && mouseY <= buttonY + BUTTON_HEIGHT &&
                                   mouseY >= entriesAreaY && mouseY <= entriesAreaY + entriesAreaHeight;

            drawModernButton(context, buttonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT,
                    0xFF4CAF50, buttonHovered);
            context.drawCenteredTextWithShadow(this.textRenderer, "Invite",
                    buttonX + BUTTON_WIDTH / 2, buttonY + 6, 0xFFFFFF);

            entryY += ENTRY_HEIGHT + ENTRY_SPACING;
        }

        // Disable scissor
        context.disableScissor();

        // Draw back button
        int backButtonX = leftX + 10;
        int backButtonY = topY + panelHeight - 30;
        boolean backButtonHovered = mouseX >= backButtonX && mouseX <= backButtonX + BUTTON_WIDTH &&
                                   mouseY >= backButtonY && mouseY <= backButtonY + BUTTON_HEIGHT;

        drawModernButton(context, backButtonX, backButtonY, BUTTON_WIDTH, BUTTON_HEIGHT,
                0xFFE53935, backButtonHovered);
        context.drawCenteredTextWithShadow(this.textRenderer, "Back",
                backButtonX + BUTTON_WIDTH / 2, backButtonY + 6, 0xFFFFFF);

        // Draw refresh button
        int refreshButtonX = leftX + panelWidth - BUTTON_WIDTH - 10;
        int refreshButtonY = backButtonY;
        boolean refreshButtonHovered = mouseX >= refreshButtonX && mouseX <= refreshButtonX + BUTTON_WIDTH &&
                                      mouseY >= refreshButtonY && mouseY <= refreshButtonY + BUTTON_HEIGHT;

        drawModernButton(context, refreshButtonX, refreshButtonY, BUTTON_WIDTH, BUTTON_HEIGHT,
                0xFF2196F3, refreshButtonHovered);
        context.drawCenteredTextWithShadow(this.textRenderer, "Refresh",
                refreshButtonX + BUTTON_WIDTH / 2, refreshButtonY + 6, 0xFFFFFF);

        // Draw status message if present
        if (statusTimer > 0) {
            context.drawCenteredTextWithShadow(this.textRenderer, statusText,
                    width / 2, topY + panelHeight - 50, statusColor);
            statusTimer--;
        }

        super.render(context, mouseX, mouseY, delta);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0) { // Left click
            int contentX = leftX + 10;
            int contentY = topY + 30;
            int contentWidth = panelWidth - 20;
            int contentHeight = panelHeight - 70;

            // Calculate visible area for player entries
            int headerY = contentY + 5;
            int entriesAreaY = headerY + 20;
            int entriesAreaHeight = contentHeight - 30;

            // Check for clicks on scrollbar
            if (mouseX >= contentX + contentWidth - 8 && mouseX <= contentX + contentWidth - 4 &&
                mouseY >= entriesAreaY && mouseY <= entriesAreaY + entriesAreaHeight) {

                // Calculate total height of all entries
                int totalHeight = playerEntries.size() * (ENTRY_HEIGHT + ENTRY_SPACING) + 10;

                // Calculate max scroll
                int maxScroll = Math.max(0, totalHeight - entriesAreaHeight);

                if (maxScroll > 0) {
                    // Calculate new scroll position based on click position
                    float clickPosition = (float)(mouseY - entriesAreaY) / entriesAreaHeight;
                    scrollOffset = Math.round(clickPosition * maxScroll);
                    scrollOffset = Math.max(0, Math.min(scrollOffset, maxScroll));
                    playClickSound();
                    return true;
                }
            }

            // Check for clicks on player entries
            if (mouseX >= contentX + 5 && mouseX <= contentX + contentWidth - 5 &&
                mouseY >= entriesAreaY && mouseY <= entriesAreaY + entriesAreaHeight) {

                int entryY = entriesAreaY - scrollOffset;

                for (PlayerEntry entry : playerEntries) {
                    // Skip if entry is completely outside visible area
                    if (entryY + ENTRY_HEIGHT < entriesAreaY || entryY > entriesAreaY + entriesAreaHeight) {
                        entryY += ENTRY_HEIGHT + ENTRY_SPACING;
                        continue;
                    }

                    // Check if click is on the invite button
                    int buttonX = contentX + contentWidth - BUTTON_WIDTH - 15;
                    int buttonY = entryY + 3;
                    if (mouseX >= buttonX && mouseX <= buttonX + BUTTON_WIDTH &&
                        mouseY >= buttonY && mouseY <= buttonY + BUTTON_HEIGHT) {

                        // Invite the player
                        invitePlayer(entry);
                        playClickSound();
                        return true;
                    }

                    entryY += ENTRY_HEIGHT + ENTRY_SPACING;
                }
            }

            // Check back button
            int backButtonX = leftX + 10;
            int backButtonY = topY + panelHeight - 30;
            if (mouseX >= backButtonX && mouseX <= backButtonX + BUTTON_WIDTH &&
                mouseY >= backButtonY && mouseY <= backButtonY + BUTTON_HEIGHT) {

                // Return to parent screen
                this.client.setScreen(parent);
                playClickSound();
                return true;
            }

            // Check refresh button
            int refreshButtonX = leftX + panelWidth - BUTTON_WIDTH - 10;
            int refreshButtonY = backButtonY;
            if (mouseX >= refreshButtonX && mouseX <= refreshButtonX + BUTTON_WIDTH &&
                mouseY >= refreshButtonY && mouseY <= refreshButtonY + BUTTON_HEIGHT) {

                // Refresh player list
                loadPlayerList();
                setStatus("Player list refreshed", 0x55FF55);
                playClickSound();
                return true;
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        int contentX = leftX + 10;
        int contentY = topY + 30;
        int contentWidth = panelWidth - 20;
        int contentHeight = panelHeight - 70;

        // Calculate visible area for player entries
        int headerY = contentY + 5;
        int entriesAreaY = headerY + 20;
        int entriesAreaHeight = contentHeight - 30;

        // Check if mouse is in the entries area
        if (mouseX >= contentX + 5 && mouseX <= contentX + contentWidth - 5 &&
            mouseY >= entriesAreaY && mouseY <= entriesAreaY + entriesAreaHeight) {

            // Calculate total height of all entries
            int totalHeight = playerEntries.size() * (ENTRY_HEIGHT + ENTRY_SPACING) + 10;

            // Calculate max scroll
            int maxScroll = Math.max(0, totalHeight - entriesAreaHeight);

            // Update scroll offset with smoother scrolling
            scrollOffset -= (int) (amount * SCROLL_AMOUNT);

            // Ensure scroll offset stays within bounds
            scrollOffset = Math.max(0, Math.min(scrollOffset, maxScroll));

            return true;
        }

        return super.mouseScrolled(mouseX, mouseY, amount);
    }

    /**
     * Invites a player to the town.
     *
     * @param player The player to invite
     */
    private void invitePlayer(PlayerEntry player) {
        try {
            // Check if we're in singleplayer mode
            boolean isSingleplayer = client != null && client.isInSingleplayer();

            if (isSingleplayer) {
                // In singleplayer, just show a success notification for testing
                NotificationManager.getInstance().addSuccessNotification("Invited " + player.name + " to your town!");

                // Remove the player from the list to simulate acceptance
                playerEntries.remove(player);

                // Show status message
                setStatus("Invitation sent to " + player.name, 0x55FF55);
            } else {
                // In multiplayer, send invitation using the network handler
                InviteNetworkHandler.invitePlayer(town.getId(), player.id);

                // Show status message
                setStatus("Invitation sent to " + player.name, 0x55FF55);
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error inviting player: " + e.getMessage());
            setStatus("Error inviting player", 0xFF5555);
        }
    }

    /**
     * Sets a status message to display.
     *
     * @param message The message to display
     * @param color The color of the message
     */
    private void setStatus(String message, int color) {
        this.statusText = Text.literal(message);
        this.statusColor = color;
        this.statusTimer = 60; // Show for 3 seconds (60 ticks)
    }

    /**
     * Plays a click sound.
     */
    private void playClickSound() {
        SoundUtil.playButtonClickSound();
    }

    /**
     * Draws a modern button with gradient and subtle 3D effect.
     */
    private void drawModernButton(DrawContext context, int x, int y, int width, int height, int color, boolean isHovered) {
        // Extract RGB components
        int r = (color >> 16) & 0xFF;
        int g = (color >> 8) & 0xFF;
        int b = color & 0xFF;

        // Adjust colors based on state
        if (isHovered) {
            // Brighten for hover state
            r = Math.min(255, r + 30);
            g = Math.min(255, g + 30);
            b = Math.min(255, b + 30);
        }

        // Create colors for gradient
        int topColor = ((r) << 16) | ((g) << 8) | (b) | 0xFF000000;
        int bottomColor = ((r * 3/4) << 16) | ((g * 3/4) << 8) | (b * 3/4) | 0xFF000000;

        // Draw gradient background
        context.fillGradient(x, y, x + width, y + height, topColor, bottomColor);

        // Draw subtle 3D effect
        int highlightColor = 0x30FFFFFF; // Subtle white highlight
        int shadowColor = 0x30000000; // Subtle shadow

        // Top highlight
        context.fill(x + 1, y + 1, x + width - 1, y + 2, highlightColor);
        // Left highlight
        context.fill(x + 1, y + 1, x + 2, y + height - 1, highlightColor);

        // Bottom shadow
        context.fill(x + 2, y + height - 2, x + width - 1, y + height - 1, shadowColor);
        // Right shadow
        context.fill(x + width - 2, y + 2, x + width - 1, y + height - 2, shadowColor);
    }

    /**
     * Represents a player entry in the invite list.
     */
    private static class PlayerEntry {
        private final UUID id;
        private final String name;

        public PlayerEntry(UUID id, String name) {
            this.id = id;
            this.name = name;
        }
    }
}
