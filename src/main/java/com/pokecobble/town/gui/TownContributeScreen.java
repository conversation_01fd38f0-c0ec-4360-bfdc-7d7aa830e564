package com.pokecobble.town.gui;

import com.pokecobble.town.Town;
import com.pokecobble.town.sound.SoundUtil;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;
import org.lwjgl.glfw.GLFW;

/**
 * Compact, professional screen for contributing coins to town levels.
 */
public class TownContributeScreen extends Screen {
    private final Screen parent;
    private final Town town;

    // UI elements
    private TextFieldWidget customAmountField;
    private int selectedAmount = 0;
    private static final int[] PRESET_AMOUNTS = {100, 250, 500, 1000, 2500, 5000};

    // Panel dimensions - smaller for more compact display
    private int panelWidth;
    private int panelHeight;
    private int leftX;
    private int topY;

    // Colors
    private static final int BACKGROUND_COLOR = 0xE0101010;
    private static final int HEADER_COLOR = 0xFF202020;

    // Status message
    private String statusMessage = "";
    private int statusColor = 0xFFFFFF;
    private int statusTimer = 0;

    /**
     * Creates a new town contribution screen.
     *
     * @param parent The parent screen
     * @param town The town
     */
    public TownContributeScreen(Screen parent, Town town) {
        super(Text.literal("Contribute"));
        this.parent = parent;
        this.town = town;
    }

    /**
     * Plays the button click sound.
     */
    private void playClickSound() {
        SoundUtil.playButtonClickSound();
    }

    @Override
    protected void init() {
        super.init();

        // Calculate panel dimensions - compact size
        panelWidth = Math.min(300, width - 40);
        panelHeight = Math.min(280, height - 40);
        leftX = (width - panelWidth) / 2;
        topY = (height - panelHeight) / 2;

        // Initialize custom amount field
        this.customAmountField = new TextFieldWidget(this.textRenderer, 0, 0, 80, 16, Text.literal(""));
        this.customAmountField.setMaxLength(7); // Max 7 digits (millions)
        this.customAmountField.setTextPredicate(this::isValidNumber);
        this.customAmountField.setChangedListener(this::onCustomAmountChanged);

        // Position will be set in render method
        this.addDrawableChild(this.customAmountField);
    }

    /**
     * Validates that the input is a valid number.
     */
    private boolean isValidNumber(String text) {
        if (text.isEmpty()) {
            return true;
        }

        try {
            int value = Integer.parseInt(text);
            return value >= 0;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * Handles changes to the custom amount field.
     */
    private void onCustomAmountChanged(String text) {
        if (!text.isEmpty()) {
            try {
                selectedAmount = Integer.parseInt(text);
            } catch (NumberFormatException e) {
                selectedAmount = 0;
            }
        } else {
            selectedAmount = 0;
        }
    }

    @Override
    public void tick() {
        super.tick();
        this.customAmountField.tick();

        // Update status timer
        if (statusTimer > 0) {
            statusTimer--;
        }
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw dark background
        this.renderBackground(context);

        // Draw panel background
        context.fill(leftX, topY, leftX + panelWidth, topY + panelHeight, BACKGROUND_COLOR);

        // Draw header
        int headerHeight = 24;
        context.fill(leftX, topY, leftX + panelWidth, topY + headerHeight, HEADER_COLOR);

        // Draw title
        context.drawCenteredTextWithShadow(this.textRenderer, Text.literal("Contribute to Town").formatted(Formatting.BOLD),
            leftX + panelWidth / 2, topY + 8, 0xFFFFFF);

        // Draw content area
        int contentX = leftX + 10;
        int contentY = topY + headerHeight + 8;
        int contentWidth = panelWidth - 20;

        // Get town level (for demo purposes)
        int townLevel = 3; // Example level
        int currentExp = 750; // Example current experience
        int expForNextLevel = 1000; // Example experience needed for next level

        // Draw level info and progress in a compact layout
        int infoX = contentX;
        int infoY = contentY;

        // Draw level and progress on the same line
        context.drawTextWithShadow(this.textRenderer, "Level " + townLevel,
            infoX, infoY, 0xFFFFFF);

        String progressText = currentExp + "/" + expForNextLevel + " XP";
        int progressWidth = this.textRenderer.getWidth(progressText);
        context.drawTextWithShadow(this.textRenderer, progressText,
            infoX + contentWidth - progressWidth, infoY, 0xFFFFFF);

        // Draw progress bar
        int barY = infoY + 14;
        int barHeight = 5;
        context.fill(infoX, barY, infoX + contentWidth, barY + barHeight, 0x80000000);

        // Draw progress bar fill
        int fillWidth = (int)((float)currentExp / expForNextLevel * contentWidth);
        context.fill(infoX, barY, infoX + fillWidth, barY + barHeight, 0xFF4CAF50);

        // Draw subtle glass effect
        context.fill(infoX, barY, infoX + contentWidth, barY + 1, 0x20FFFFFF);

        // Draw player's coins
        int playerCoins = 10000; // Example coins
        context.drawTextWithShadow(this.textRenderer, "Your Coins: " + playerCoins,
            infoX, barY + barHeight + 8, 0xFFFFD700);

        // Draw divider
        int dividerY = barY + barHeight + 24;
        context.fill(infoX, dividerY, infoX + contentWidth, dividerY + 1, 0x40FFFFFF);

        // Draw preset amounts in a compact grid
        int amountsY = dividerY + 8;
        context.drawTextWithShadow(this.textRenderer, Text.literal("Quick Amounts").formatted(Formatting.BOLD),
            infoX, amountsY, 0xFFFFFF);

        // Draw amount buttons in a 3x2 grid
        int buttonWidth = (contentWidth - 10) / 3;
        int buttonHeight = 16;
        int buttonSpacing = 5;
        int buttonsY = amountsY + 14;

        for (int i = 0; i < PRESET_AMOUNTS.length; i++) {
            int row = i / 3;
            int col = i % 3;

            int buttonX = infoX + col * (buttonWidth + buttonSpacing);
            int buttonY = buttonsY + row * (buttonHeight + buttonSpacing);

            boolean isSelected = selectedAmount == PRESET_AMOUNTS[i];
            boolean isHovered = mouseX >= buttonX && mouseX <= buttonX + buttonWidth &&
                               mouseY >= buttonY && mouseY <= buttonY + buttonHeight;

            // Use different colors for different amounts
            int buttonColor;
            switch (i) {
                case 0: buttonColor = 0xFF4CAF50; break; // Green
                case 1: buttonColor = 0xFF2196F3; break; // Blue
                case 2: buttonColor = 0xFF9C27B0; break; // Purple
                case 3: buttonColor = 0xFFFF9800; break; // Orange
                case 4: buttonColor = 0xFF607D8B; break; // Blue-gray
                case 5: buttonColor = 0xFFE91E63; break; // Pink
                default: buttonColor = 0xFF2196F3; break; // Blue
            }

            drawModernButton(context, buttonX, buttonY, buttonWidth, buttonHeight,
                buttonColor, isHovered, true);

            // Draw button text
            context.drawCenteredTextWithShadow(this.textRenderer, String.valueOf(PRESET_AMOUNTS[i]),
                buttonX + buttonWidth / 2, buttonY + 4, 0xFFFFFF);
        }

        // Draw custom amount section
        int customY = buttonsY + 2 * (buttonHeight + buttonSpacing) + 10;

        // Draw custom amount in a single row
        context.drawTextWithShadow(this.textRenderer, "Custom:",
            infoX, customY + 4, 0xFFFFFF);

        // Position custom amount field
        int fieldX = infoX + 50;
        int fieldY = customY;
        this.customAmountField.setX(fieldX);
        this.customAmountField.setY(fieldY);
        this.customAmountField.setWidth(80);

        // Draw contribute button
        int contributeButtonWidth = 80;
        int contributeButtonHeight = 16;
        int contributeButtonX = fieldX + 85;
        int contributeButtonY = customY;

        boolean contributeHovered = mouseX >= contributeButtonX && mouseX <= contributeButtonX + contributeButtonWidth &&
                                   mouseY >= contributeButtonY && mouseY <= contributeButtonY + contributeButtonHeight;

        int contributeButtonColor = selectedAmount > 0 ? 0xFF4CAF50 : 0xFF555555;

        drawModernButton(context, contributeButtonX, contributeButtonY, contributeButtonWidth, contributeButtonHeight,
                       contributeButtonColor, contributeHovered, selectedAmount > 0);

        context.drawCenteredTextWithShadow(this.textRenderer, "Contribute",
            contributeButtonX + contributeButtonWidth / 2, contributeButtonY + 4, 0xFFFFFF);

        // Draw back button
        int backButtonWidth = 60;
        int backButtonHeight = 16;
        int backButtonX = leftX + 10;
        int backButtonY = topY + panelHeight - backButtonHeight - 10;

        boolean backHovered = mouseX >= backButtonX && mouseX <= backButtonX + backButtonWidth &&
                             mouseY >= backButtonY && mouseY <= backButtonY + backButtonHeight;

        drawModernButton(context, backButtonX, backButtonY, backButtonWidth, backButtonHeight,
                       0xFF555555, backHovered, true);

        context.drawCenteredTextWithShadow(this.textRenderer, "Back",
            backButtonX + backButtonWidth / 2, backButtonY + 4, 0xFFFFFF);

        // Draw status message if active
        if (statusTimer > 0) {
            context.drawCenteredTextWithShadow(this.textRenderer, statusMessage,
                leftX + panelWidth / 2, customY + 25, statusColor);
        }

        super.render(context, mouseX, mouseY, delta);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0) { // Left click
            // Check preset amount buttons
            int contentX = leftX + 10;
            int contentWidth = panelWidth - 20;

            int dividerY = topY + 24 + 8 + 14 + 5 + 24;
            int amountsY = dividerY + 8;

            int buttonWidth = (contentWidth - 10) / 3;
            int buttonHeight = 16;
            int buttonSpacing = 5;
            int buttonsY = amountsY + 14;

            for (int i = 0; i < PRESET_AMOUNTS.length; i++) {
                int row = i / 3;
                int col = i % 3;

                int buttonX = contentX + col * (buttonWidth + buttonSpacing);
                int buttonY = buttonsY + row * (buttonHeight + buttonSpacing);

                if (mouseX >= buttonX && mouseX <= buttonX + buttonWidth &&
                    mouseY >= buttonY && mouseY <= buttonY + buttonHeight) {
                    // Play click sound
                    playClickSound();

                    // Select this amount
                    selectedAmount = PRESET_AMOUNTS[i];
                    customAmountField.setText(String.valueOf(selectedAmount));
                    return true;
                }
            }

            // Check contribute button
            int customY = buttonsY + 2 * (buttonHeight + buttonSpacing) + 10;
            int fieldX = contentX + 50;

            int contributeButtonWidth = 80;
            int contributeButtonHeight = 16;
            int contributeButtonX = fieldX + 85;
            int contributeButtonY = customY;

            if (mouseX >= contributeButtonX && mouseX <= contributeButtonX + contributeButtonWidth &&
                mouseY >= contributeButtonY && mouseY <= contributeButtonY + contributeButtonHeight) {
                // Only allow contribution if an amount is selected
                if (selectedAmount > 0) {
                    // Play click sound
                    playClickSound();

                    // Contribute the selected amount
                    contributeCoins(selectedAmount);
                    return true;
                } else {
                    // Play error sound
                    this.client.getSoundManager().play(net.minecraft.client.sound.PositionedSoundInstance.master(
                        net.minecraft.sound.SoundEvents.BLOCK_NOTE_BLOCK_BASS, 0.5F));

                    // Show error message
                    setStatus("Please select an amount", 0xFFFF5555);
                }
            }

            // Check back button
            int backButtonWidth = 60;
            int backButtonHeight = 16;
            int backButtonX = leftX + 10;
            int backButtonY = topY + panelHeight - backButtonHeight - 10;

            if (mouseX >= backButtonX && mouseX <= backButtonX + backButtonWidth &&
                mouseY >= backButtonY && mouseY <= backButtonY + backButtonHeight) {
                // Play click sound
                playClickSound();

                // Return to parent screen
                this.client.setScreen(parent);
                return true;
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    /**
     * Sets a status message to display.
     */
    private void setStatus(String message, int color) {
        this.statusMessage = message;
        this.statusColor = color;
        this.statusTimer = 60; // Show for 3 seconds (60 ticks)
    }

    /**
     * Contributes coins to the town level.
     */
    private void contributeCoins(int amount) {
        // In a real implementation, this would send a request to the server
        // to contribute the coins to the town level

        // Show success message
        setStatus("Contributed " + amount + " coins", 0xFF55FF55);

        // Update parent screen status if applicable
        if (parent instanceof MyTownScreen) {
            ((MyTownScreen) parent).setStatus("Contributed " + amount + " coins to town level!", Formatting.GREEN);
        }

        // Play success sound
        this.client.getSoundManager().play(net.minecraft.client.sound.PositionedSoundInstance.master(
            net.minecraft.sound.SoundEvents.ENTITY_PLAYER_LEVELUP, 0.8F));

        // Reset selected amount
        selectedAmount = 0;
        customAmountField.setText("");
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        if (keyCode == GLFW.GLFW_KEY_ESCAPE) {
            // Return to parent screen
            this.client.setScreen(parent);
            return true;
        }

        if (keyCode == GLFW.GLFW_KEY_ENTER || keyCode == GLFW.GLFW_KEY_KP_ENTER) {
            // Contribute if an amount is selected
            if (selectedAmount > 0) {
                contributeCoins(selectedAmount);
                return true;
            } else {
                // Show error message
                setStatus("Please select an amount", 0xFFFF5555);
            }
        }

        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    /**
     * Draws a modern button with subtle 3D effect.
     */
    private void drawModernButton(DrawContext context, int x, int y, int width, int height, int color, boolean isHovered, boolean isActive) {
        // Extract RGB components
        int r = (color >> 16) & 0xFF;
        int g = (color >> 8) & 0xFF;
        int b = color & 0xFF;

        // Adjust colors based on state
        if (!isActive) {
            // Desaturate and darken for inactive buttons
            int avg = (r + g + b) / 3;
            r = (r + avg) / 2;
            g = (g + avg) / 2;
            b = (b + avg) / 2;
            r = r * 3/4;
            g = g * 3/4;
            b = b * 3/4;
        } else if (isHovered) {
            // Brighten for hover state
            r = Math.min(255, r + 30);
            g = Math.min(255, g + 30);
            b = Math.min(255, b + 30);
        }

        // Reconstruct color with alpha
        int baseColor = 0xFF000000 | (r << 16) | (g << 8) | b;

        // Draw button background
        context.fill(x, y, x + width, y + height, baseColor);

        // Draw subtle glass effect
        context.fill(x, y, x + width, y + 1, 0x20FFFFFF);
        context.fill(x, y, x + 1, y + height, 0x20FFFFFF);
        context.fill(x, y + height - 1, x + width, y + height, 0x20000000);
        context.fill(x + width - 1, y, x + width, y + height, 0x20000000);
    }
}
