package com.pokecobble.town.gui;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.sound.SoundUtil;
import com.pokecobble.town.util.TownNameValidator;
import com.pokecobble.town.util.TownNameValidator.ValidationResult;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;
import net.minecraft.sound.SoundEvents;
import net.minecraft.client.sound.PositionedSoundInstance;

/**
 * Modern, compact screen for creating a new town with responsive design that matches ModernTownScreen.
 */
public class CreateTownScreen extends Screen {
    private final Screen parent;

    // Compact Design Constants (matching ModernTownScreen)
    private static final int CORNER_RADIUS = 4;
    private static final int SHADOW_SIZE = 2;
    private static final int ANIMATION_DURATION = 150; // milliseconds

    // Modern Color Palette (matching ModernTownScreen)
    private static final int PRIMARY_BG = 0xE8121212;        // Dark background
    private static final int SECONDARY_BG = 0xF01A1A1A;     // Slightly lighter
    private static final int CARD_BG = 0xF0252525;          // Card background
    private static final int ACCENT_PRIMARY = 0xFF4A90E2;   // Modern blue
    private static final int ACCENT_SUCCESS = 0xFF27AE60;   // Success green
    private static final int ACCENT_WARNING = 0xFFF39C12;   // Warning orange
    private static final int ACCENT_DANGER = 0xFFE74C3C;    // Danger red
    private static final int TEXT_PRIMARY = 0xFFFFFFFF;     // Primary text
    private static final int TEXT_SECONDARY = 0xFFB0B0B0;   // Secondary text
    private static final int TEXT_MUTED = 0xFF808080;       // Muted text
    private static final int BORDER_COLOR = 0xFF333333;     // Border color
    private static final int HOVER_OVERLAY = 0x20FFFFFF;    // Hover effect

    // Panel dimensions - adjusted for proper spacing
    private int panelWidth = 340;
    private int panelHeight = 320;

    // Responsive constants - adjusted for increased section heights
    private static final int MIN_PANEL_WIDTH = 280;
    private static final int MIN_PANEL_HEIGHT = 280; // Increased to accommodate larger sections
    private static final int MAX_PANEL_WIDTH = 380;
    private static final int MAX_PANEL_HEIGHT = 380; // Increased to accommodate larger sections

    // Ultra compact spacing system
    private static final int SPACING_XS = 1;
    private static final int SPACING_SM = 2;
    private static final int SPACING_MD = 4;
    private static final int SPACING_LG = 6;
    private static final int SPACING_XL = 8;

    // UI elements
    private CustomTextBox nameField;
    private CustomTextBox descriptionField;
    private Town.JoinType joinType = Town.JoinType.OPEN;
    private int playerLimit = 20;

    // Join type options with better descriptions
    private final JoinTypeOption[] joinTypeOptions = {
        new JoinTypeOption(Town.JoinType.OPEN, "🔓 Open", "Anyone can join your town", ACCENT_SUCCESS),
        new JoinTypeOption(Town.JoinType.INVITE_ONLY, "📧 Invite Only", "Players need an invitation", ACCENT_PRIMARY),
        new JoinTypeOption(Town.JoinType.CLOSED, "🔒 Closed", "No new members allowed", ACCENT_DANGER)
    };

    // UI element positions
    private int createButtonX;
    private int createButtonY;
    private int cancelButtonX;
    private int cancelButtonY;

    // Join type radio buttons - ultra compact
    private int[] radioButtonX = new int[3];
    private int[] radioButtonY = new int[3];
    private static final int RADIO_BUTTON_SIZE = 8;
    private static final int RADIO_BUTTON_SPACING = 2;

    // Player limit slider - ultra compact
    private int sliderX;
    private int sliderY;
    private int sliderWidth = 80;
    private boolean isDraggingSlider = false;
    private static final int SLIDER_HEIGHT = 3;
    private static final int SLIDER_HANDLE_SIZE = 6;

    // Ultra compact button dimensions
    private final int buttonWidth = 60;
    private final int buttonHeight = 16;
    private final int smallButtonWidth = 12;
    private final int smallButtonHeight = 12;
    private final int toggleWidth = 60;
    private final int toggleHeight = 16;

    // Field validation
    private boolean isNameValid = false;
    private String nameValidationMessage = "";

    // Status message with modern styling
    private Text statusText = Text.empty();
    private int statusColor = TEXT_PRIMARY;
    private long statusShowTime = 0;
    private static final long STATUS_DISPLAY_DURATION = 3000; // 3 seconds

    // Animation variables
    private float animationProgress = 0.0f;
    private long lastRenderTime = 0;

    // Form completion tracking
    private float formCompletionProgress = 0.0f;

    // Tooltip system
    private String currentTooltip = "";
    private int tooltipX = 0;
    private int tooltipY = 0;
    private long tooltipShowTime = 0;

    // Section definitions - adjusted heights to prevent overlapping
    private int SECTION_HEIGHT = 64; // Further increased to accommodate character counter and proper spacing
    private int SECTION_SPACING = 8; // Increased spacing between sections
    private static final int SECTION_PADDING = 4;

    // Custom text box constants - compact
    private static final int TEXTBOX_HEIGHT = 16;
    private static final int TEXTBOX_PADDING = 4;

    public CreateTownScreen(Screen parent) {
        super(Text.literal("Create Town"));
        this.parent = parent;
    }

    @Override
    protected void init() {
        super.init();

        // Reset animation and timing
        animationProgress = 0.0f;
        lastRenderTime = System.currentTimeMillis();

        // Calculate responsive panel dimensions
        calculateResponsiveDimensions();

        // Set initial focus to name field
        if (nameField != null) {
            nameField.setFocused(true);
        }

        // Update form completion
        updateFormCompletion();
    }

    /**
     * Calculates responsive panel dimensions based on screen size
     */
    private void calculateResponsiveDimensions() {
        // Calculate available space with safe margins
        int safeMarginX = Math.max(SPACING_XL, width / 20); // At least 5% margin
        int safeMarginY = Math.max(SPACING_XL, height / 20); // At least 5% margin

        int availableWidth = width - safeMarginX * 2;
        int availableHeight = height - safeMarginY * 2;

        // Calculate optimal panel size
        int optimalWidth = Math.min(MAX_PANEL_WIDTH, Math.max(MIN_PANEL_WIDTH, availableWidth));
        int optimalHeight = Math.min(MAX_PANEL_HEIGHT, Math.max(MIN_PANEL_HEIGHT, availableHeight));

        // Ensure panel fits with content
        panelWidth = Math.min(optimalWidth, availableWidth);
        panelHeight = Math.min(optimalHeight, availableHeight);

        // Adjust section dimensions based on panel size
        adjustSectionDimensions();

        // Position all elements
        positionElements();
    }

    /**
     * Adjusts section dimensions for ultra compact layout
     */
    private void adjustSectionDimensions() {
        // Calculate available space with proper header spacing
        int headerHeight = SPACING_XL + 32; // Increased header space to prevent overlap
        int buttonAreaHeight = SPACING_MD + buttonHeight; // Button area
        int availableContentHeight = panelHeight - headerHeight - buttonAreaHeight - SPACING_MD;

        int totalSections = 3;
        int totalSpacing = (totalSections - 1) * SECTION_SPACING;

        int maxSectionHeight = (availableContentHeight - totalSpacing) / totalSections;

        // Force ultra compact sections if needed
        if (maxSectionHeight < SECTION_HEIGHT) {
            SECTION_HEIGHT = Math.max(40, maxSectionHeight); // Ultra compact minimum
            SECTION_SPACING = Math.max(4, SECTION_SPACING); // Ultra compact spacing
        }
    }

    /**
     * Positions all UI elements responsively
     */
    private void positionElements() {
        // Center the panel
        int leftX = (width - panelWidth) / 2;
        int topY = (height - panelHeight) / 2;
        int contentX = leftX + SPACING_LG;
        int contentWidth = panelWidth - SPACING_LG * 2;

        // Calculate section positions with proper spacing to avoid header overlap
        int currentY = topY + SPACING_XL + 32; // Increased spacing to prevent overlap with header

        // Section 1: Basic Information - ultra compact
        int basicInfoY = currentY;

        // Create or update custom name field - compact positioning
        if (nameField == null) {
            nameField = new CustomTextBox(
                    textRenderer,
                    contentX + SECTION_PADDING,
                    basicInfoY + 14, // Tighter spacing
                    contentWidth - SECTION_PADDING * 2,
                    TEXTBOX_HEIGHT,
                    "🏠 Town name",
                    32
            );
            nameField.setChangedListener(this::validateTownName);
        } else {
            nameField.updatePosition(
                contentX + SECTION_PADDING,
                basicInfoY + 14,
                contentWidth - SECTION_PADDING * 2
            );
        }

        // Create or update custom description field - proper spacing to avoid overlap
        if (descriptionField == null) {
            descriptionField = new CustomTextBox(
                    textRenderer,
                    contentX + SECTION_PADDING,
                    basicInfoY + 34, // More spacing to avoid overlap with name field
                    contentWidth - SECTION_PADDING * 2,
                    TEXTBOX_HEIGHT,
                    "📝 Description",
                    100
            );
            descriptionField.setChangedListener(text -> updateFormCompletion());
        } else {
            descriptionField.updatePosition(
                contentX + SECTION_PADDING,
                basicInfoY + 34,
                contentWidth - SECTION_PADDING * 2
            );
        }

        currentY += SECTION_HEIGHT + SECTION_SPACING;

        // Section 2: Privacy Settings - proper spacing to avoid overlap
        int privacyY = currentY;
        for (int i = 0; i < joinTypeOptions.length; i++) {
            radioButtonX[i] = contentX + SECTION_PADDING;
            radioButtonY[i] = privacyY + 16 + i * (RADIO_BUTTON_SIZE + RADIO_BUTTON_SPACING + 4); // More spacing to prevent overlap
        }

        currentY += SECTION_HEIGHT + SECTION_SPACING;

        // Section 3: Player Limit - proper spacing
        int playerLimitY = currentY;
        sliderX = contentX + SECTION_PADDING;
        sliderY = playerLimitY + 24; // More spacing to avoid overlap with section title

        // Adjust slider width for compact layout
        sliderWidth = Math.min(80, contentWidth - SECTION_PADDING * 2 - 30); // Compact slider

        currentY += SECTION_HEIGHT + SECTION_SPACING;

        // Action buttons at the bottom - proper spacing
        createButtonX = contentX + SECTION_PADDING;
        createButtonY = currentY + SPACING_MD; // More spacing to avoid overlap

        // Compact cancel button
        int availableButtonSpace = contentWidth - SECTION_PADDING * 2 - buttonWidth - SPACING_SM;
        int cancelButtonWidth = Math.min(buttonWidth, Math.max(40, availableButtonSpace));
        cancelButtonX = contentX + contentWidth - cancelButtonWidth - SECTION_PADDING;
        cancelButtonY = createButtonY;
    }

    @Override
    public void resize(MinecraftClient client, int width, int height) {
        // Store current state
        String nameText = nameField != null ? nameField.getText() : "";
        String descText = descriptionField != null ? descriptionField.getText() : "";
        boolean nameFocused = nameField != null && nameField.isFocused();
        boolean descFocused = descriptionField != null && descriptionField.isFocused();

        // Update dimensions
        this.width = width;
        this.height = height;

        // Recalculate layout
        calculateResponsiveDimensions();

        // Restore state
        if (nameField != null) {
            nameField.setText(nameText);
            nameField.setFocused(nameFocused);
        }
        if (descriptionField != null) {
            descriptionField.setText(descText);
            descriptionField.setFocused(descFocused);
        }

        // Update form completion
        updateFormCompletion();
    }

    /**
     * Ensures all elements are within window bounds
     */
    private void ensureElementsInBounds() {
        // Check if panel is within screen bounds
        int leftX = (width - panelWidth) / 2;
        int topY = (height - panelHeight) / 2;

        // Adjust if panel goes off screen
        if (leftX < 0) {
            leftX = SPACING_SM;
        }
        if (topY < 0) {
            topY = SPACING_SM;
        }
        if (leftX + panelWidth > width) {
            leftX = width - panelWidth - SPACING_SM;
        }
        if (topY + panelHeight > height) {
            topY = height - panelHeight - SPACING_SM;
        }

        // If panel still doesn't fit, force smaller dimensions
        if (panelWidth > width - SPACING_SM * 2) {
            panelWidth = width - SPACING_SM * 2;
        }
        if (panelHeight > height - SPACING_SM * 2) {
            panelHeight = height - SPACING_SM * 2;
        }
    }

    @Override
    public void tick() {
        super.tick();
        // Ensure elements stay in bounds during any updates
        ensureElementsInBounds();
    }

    /**
     * Validates the town name and updates the UI accordingly.
     *
     * @param name The town name to validate
     */
    private void validateTownName(String name) {
        // First check if the name is already taken
        if (TownManager.getInstance().isTownNameTaken(name)) {
            isNameValid = false;
            nameValidationMessage = "This town name is already taken";
            updateFormCompletion();
            return;
        }

        // Then validate the name content
        ValidationResult result = TownNameValidator.validate(name);
        isNameValid = result.isValid();
        nameValidationMessage = result.getMessage();
        updateFormCompletion();
    }

    /**
     * Updates the form completion progress based on filled fields
     */
    private void updateFormCompletion() {
        float progress = 0.0f;

        // Name field (50% weight - most important)
        if (isNameValid && !nameField.getText().trim().isEmpty()) {
            progress += 0.5f;
        }

        // Description field (20% weight)
        if (!descriptionField.getText().trim().isEmpty()) {
            progress += 0.2f;
        }

        // Join type (15% weight - always has a default)
        progress += 0.15f;

        // Player limit (15% weight - always has a default)
        progress += 0.15f;

        formCompletionProgress = progress;
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Update animation and timing
        long currentTime = System.currentTimeMillis();
        float deltaTime = (currentTime - lastRenderTime) / 1000.0f;
        lastRenderTime = currentTime;

        // Render modern background
        this.renderBackground(context);

        // Calculate responsive panel position
        ensureElementsInBounds(); // Ensure panel fits in window
        int leftX = Math.max(SPACING_SM, (width - panelWidth) / 2);
        int topY = Math.max(SPACING_SM, (height - panelHeight) / 2);
        int contentX = leftX + SPACING_LG;
        int contentWidth = panelWidth - SPACING_LG * 2;

        // Draw modern panel with shadow effect
        drawModernPanel(context, leftX, topY, panelWidth, panelHeight);

        // Draw modern header
        drawModernHeader(context, leftX, topY, panelWidth);

        // Calculate section positions with proper spacing to avoid header overlap
        int currentY = topY + SPACING_XL + 32; // Increased spacing to prevent overlap with header

        // Draw Section 1: Basic Information
        drawSection(context, contentX, currentY, contentWidth, SECTION_HEIGHT, "📝 Basic Information");
        drawCustomTextBoxes(context, mouseX, mouseY);
        currentY += SECTION_HEIGHT + SECTION_SPACING;

        // Draw Section 2: Privacy Settings
        drawSection(context, contentX, currentY, contentWidth, SECTION_HEIGHT, "🔐 Privacy Settings");
        drawPrivacySettings(context, contentX, currentY, contentWidth, mouseX, mouseY);
        currentY += SECTION_HEIGHT + SECTION_SPACING;

        // Draw Section 3: Player Limit
        drawSection(context, contentX, currentY, contentWidth, SECTION_HEIGHT, "👥 Player Limit");
        drawPlayerLimitSlider(context, contentX, currentY, contentWidth, mouseX, mouseY);
        currentY += SECTION_HEIGHT + SECTION_SPACING;

        // Draw action buttons with proper spacing
        drawModernActionButtons(context, contentX, contentWidth, currentY + SPACING_MD, mouseX, mouseY);

        // Draw tooltips
        drawTooltips(context, mouseX, mouseY);

        // Draw modern status message
        drawModernStatusMessage(context, leftX, topY);
    }

    /**
     * Draws a section with title and background
     */
    private void drawSection(DrawContext context, int x, int y, int width, int height, String title) {
        // Draw section background
        drawModernCard(context, x, y, width, height, CARD_BG, false);

        // Draw section title
        context.drawTextWithShadow(this.textRenderer, title, x + SECTION_PADDING, y + 4, TEXT_PRIMARY);

        // Draw section divider line
        context.fill(x + SECTION_PADDING, y + 14, x + width - SECTION_PADDING, y + 15, BORDER_COLOR);
    }

    /**
     * Draws custom text boxes with validation
     */
    private void drawCustomTextBoxes(DrawContext context, int mouseX, int mouseY) {
        // Draw name field
        nameField.render(context, mouseX, mouseY);

        // Draw validation indicator for name field - positioned to the right but within bounds
        if (!nameField.getText().isEmpty()) {
            int validationColor = isNameValid ? ACCENT_SUCCESS : ACCENT_DANGER;
            String icon = isNameValid ? "✓" : "✗";
            // Position validation icon inside the text field on the right side to avoid going outside panel
            context.drawTextWithShadow(this.textRenderer, icon,
                    nameField.getX() + nameField.getWidth() - 12, nameField.getY() + 4, validationColor);
        }

        // Draw description field
        descriptionField.render(context, mouseX, mouseY);

        // Draw character counter for description - positioned below the text field
        String descText = descriptionField.getText();
        if (descText.length() > 0) {
            int counterColor = descText.length() > 80 ? ACCENT_WARNING : TEXT_MUTED;
            String counter = String.format("%d/100", descText.length());
            context.drawTextWithShadow(this.textRenderer, counter,
                    descriptionField.getX() + descriptionField.getWidth() - 40, descriptionField.getY() + descriptionField.getHeight() + 2, counterColor);
        }
    }

    /**
     * Draws compact form completion progress bar
     */
    private void drawFormProgress(DrawContext context, int contentX, int contentWidth, int topY) {
        int progressY = topY + SPACING_XL + 12 + 48 - 20; // More compact positioning
        int progressWidth = contentWidth;
        int progressHeight = 3; // Thinner progress bar

        // Background
        drawModernCard(context, contentX, progressY, progressWidth, progressHeight, CARD_BG, false);

        // Progress fill
        int fillWidth = (int)(progressWidth * formCompletionProgress);
        if (fillWidth > 0) {
            int progressColor = formCompletionProgress >= 0.7f ? ACCENT_SUCCESS :
                               formCompletionProgress >= 0.4f ? ACCENT_WARNING : ACCENT_PRIMARY;
            context.fill(contentX, progressY, contentX + fillWidth, progressY + progressHeight, progressColor);
        }

        // Compact progress text
        String progressText = String.format("%.0f%%", formCompletionProgress * 100);
        context.drawTextWithShadow(this.textRenderer, progressText,
            contentX, progressY - 10, TEXT_MUTED);
    }

    /**
     * Draws compact privacy settings section with radio buttons
     */
    private void drawPrivacySettings(DrawContext context, int sectionX, int sectionY, int sectionWidth, int mouseX, int mouseY) {
        // Draw compact radio buttons for join types
        for (int i = 0; i < joinTypeOptions.length; i++) {
            JoinTypeOption option = joinTypeOptions[i];
            boolean isSelected = option.joinType == joinType;
            boolean isHovered = isMouseOver(mouseX, mouseY,
                radioButtonX[i], radioButtonY[i],
                sectionWidth - SECTION_PADDING * 2, RADIO_BUTTON_SIZE + 1);

            // Draw radio button circle
            int radioColor = isSelected ? option.color : BORDER_COLOR;
            if (isHovered && !isSelected) {
                radioColor = blendColors(option.color, HOVER_OVERLAY, 0.3f);
            }

            drawModernCard(context, radioButtonX[i], radioButtonY[i],
                RADIO_BUTTON_SIZE, RADIO_BUTTON_SIZE, radioColor, isSelected);

            // Draw selection dot - ultra compact
            if (isSelected) {
                int dotSize = 2; // Ultra small dot
                int dotX = radioButtonX[i] + (RADIO_BUTTON_SIZE - dotSize) / 2;
                int dotY = radioButtonY[i] + (RADIO_BUTTON_SIZE - dotSize) / 2;
                context.fill(dotX, dotY, dotX + dotSize, dotY + dotSize, TEXT_PRIMARY);
            }

            // Draw ultra compact option text - properly aligned with radio button center
            int textX = radioButtonX[i] + RADIO_BUTTON_SIZE + RADIO_BUTTON_SPACING;
            int textY = radioButtonY[i] + (RADIO_BUTTON_SIZE - 8) / 2; // Center align with radio button

            // Use ultra short text for compact display
            String compactTitle = getUltraCompactTitle(option.title);
            context.drawTextWithShadow(this.textRenderer, compactTitle, textX, textY, TEXT_PRIMARY);

            // Set tooltip on hover for full description
            if (isHovered) {
                setTooltip(option.title + " - " + option.description, mouseX, mouseY);
            }
        }
    }

    /**
     * Gets ultra compact version of join type titles
     */
    private String getUltraCompactTitle(String fullTitle) {
        switch (fullTitle) {
            case "🔓 Open":
                return "🔓 Open";
            case "📧 Invite Only":
                return "📧 Invite";
            case "🔒 Closed":
                return "🔒 Closed";
            default:
                return fullTitle;
        }
    }

    /**
     * Draws ultra compact player limit slider
     */
    private void drawPlayerLimitSlider(DrawContext context, int sectionX, int sectionY, int sectionWidth, int mouseX, int mouseY) {
        // Draw compact current value
        String valueText = String.format("%d players", playerLimit);
        context.drawTextWithShadow(this.textRenderer, valueText, sliderX, sectionY + 12, TEXT_PRIMARY);

        // Draw ultra compact slider track
        drawModernCard(context, sliderX, sliderY + SLIDER_HANDLE_SIZE / 2 - SLIDER_HEIGHT / 2,
            sliderWidth, SLIDER_HEIGHT, SECONDARY_BG, false);

        // Calculate handle position (5-100 players range)
        float sliderProgress = (playerLimit - 5) / 95.0f;
        int handleX = sliderX + (int)(sliderProgress * (sliderWidth - SLIDER_HANDLE_SIZE));
        int handleY = sliderY;

        // Check if slider is hovered or being dragged
        boolean sliderHovered = isMouseOver(mouseX, mouseY, sliderX, sliderY, sliderWidth, SLIDER_HANDLE_SIZE);
        boolean handleHovered = isMouseOver(mouseX, mouseY, handleX, handleY, SLIDER_HANDLE_SIZE, SLIDER_HANDLE_SIZE);

        // Draw ultra compact slider handle
        int handleColor = (isDraggingSlider || handleHovered) ? ACCENT_PRIMARY : TEXT_SECONDARY;
        drawModernCard(context, handleX, handleY, SLIDER_HANDLE_SIZE, SLIDER_HANDLE_SIZE, handleColor, true);

        // Draw compact value indicators - positioned to avoid overlap
        context.drawTextWithShadow(this.textRenderer, "5", sliderX - 8, sliderY + SLIDER_HANDLE_SIZE + 2, TEXT_MUTED);
        context.drawTextWithShadow(this.textRenderer, "100", sliderX + sliderWidth - 16, sliderY + SLIDER_HANDLE_SIZE + 2, TEXT_MUTED);

        // Set tooltip
        if (sliderHovered) {
            setTooltip("Drag: 5-100 players", mouseX, mouseY);
        }
    }

    /**
     * Draws compact tooltips
     */
    private void drawTooltips(DrawContext context, int mouseX, int mouseY) {
        if (!currentTooltip.isEmpty() && System.currentTimeMillis() - tooltipShowTime > 300) { // Faster show
            int tooltipWidth = this.textRenderer.getWidth(currentTooltip) + SPACING_SM * 2; // Smaller padding
            int tooltipHeight = 16; // Smaller height

            // Ensure tooltip stays on screen
            int tooltipX = Math.min(mouseX + 8, width - tooltipWidth - 8); // Closer to mouse
            int tooltipY = Math.max(mouseY - tooltipHeight - 8, 8);

            // Draw compact tooltip background
            drawModernCard(context, tooltipX, tooltipY, tooltipWidth, tooltipHeight, SECONDARY_BG, true);

            // Draw tooltip text
            context.drawTextWithShadow(this.textRenderer, currentTooltip,
                tooltipX + SPACING_SM, tooltipY + 4, TEXT_PRIMARY);
        }
    }

    /**
     * Sets tooltip text and position
     */
    private void setTooltip(String text, int x, int y) {
        if (!text.equals(currentTooltip)) {
            currentTooltip = text;
            tooltipX = x;
            tooltipY = y;
            tooltipShowTime = System.currentTimeMillis();
        }
    }

    /**
     * Draws modern action buttons
     */
    private void drawModernActionButtons(DrawContext context, int contentX, int contentWidth, int buttonY, int mouseX, int mouseY) {
        // Calculate responsive button widths
        int cancelButtonWidth = Math.min(buttonWidth, contentWidth - buttonWidth - SPACING_MD);

        // Draw create button
        boolean createHovered = isMouseOver(mouseX, mouseY, createButtonX, createButtonY, buttonWidth, buttonHeight);
        int createButtonColor = isNameValid ? ACCENT_SUCCESS : TEXT_MUTED;
        drawModernCard(context, createButtonX, createButtonY, buttonWidth, buttonHeight,
                      createButtonColor, createHovered && isNameValid);
        context.drawCenteredTextWithShadow(this.textRenderer, "✓ Create",
            createButtonX + buttonWidth / 2, createButtonY + (buttonHeight - 8) / 2, TEXT_PRIMARY);

        // Draw cancel button
        boolean cancelHovered = isMouseOver(mouseX, mouseY, cancelButtonX, cancelButtonY, cancelButtonWidth, buttonHeight);
        drawModernCard(context, cancelButtonX, cancelButtonY, cancelButtonWidth, buttonHeight,
                      cancelHovered ? ACCENT_DANGER : blendColors(ACCENT_DANGER, CARD_BG, 0.7f), cancelHovered);
        String cancelText = cancelButtonWidth >= 80 ? "✗ Cancel" : "✗";
        context.drawCenteredTextWithShadow(this.textRenderer, cancelText,
            cancelButtonX + cancelButtonWidth / 2, cancelButtonY + (buttonHeight - 8) / 2, TEXT_PRIMARY);
    }

    /**
     * Draws a compact modern panel with subtle shadow and gradient background
     */
    private void drawModernPanel(DrawContext context, int x, int y, int width, int height) {
        // Draw compact shadow
        for (int i = 0; i < SHADOW_SIZE; i++) {
            int shadowAlpha = (SHADOW_SIZE - i) * 15;
            int shadowColor = shadowAlpha << 24;
            context.fill(x + i, y + i, x + width + i, y + height + i, shadowColor);
        }

        // Draw main panel background with gradient
        context.fillGradient(x, y, x + width, y + height, PRIMARY_BG, SECONDARY_BG);

        // Draw subtle border
        context.drawBorder(x, y, width, height, BORDER_COLOR);

        // Add subtle inner glow
        context.fill(x + 1, y + 1, x + width - 1, y + 2, 0x15FFFFFF);
        context.fill(x + 1, y + 1, x + 2, y + height - 1, 0x15FFFFFF);
    }

    /**
     * Draws the ultra compact modern header section
     */
    private void drawModernHeader(DrawContext context, int x, int y, int width) {
        // Ultra compact header background
        context.fillGradient(x, y, x + width, y + SPACING_XL + 8, SECONDARY_BG, PRIMARY_BG);

        // Compact header title
        String title = "Create Town";
        int titleWidth = this.textRenderer.getWidth(title);
        int titleX = x + (width - titleWidth) / 2;
        int titleY = y + SPACING_SM;

        // Minimal title glow effect
        context.fillGradient(
            titleX - SPACING_XS, titleY - 1,
            titleX + titleWidth + SPACING_XS, titleY + 8,
            0x15FFFFFF, 0x05FFFFFF
        );

        // Draw title
        context.drawTextWithShadow(this.textRenderer, title, titleX, titleY, TEXT_PRIMARY);

        // Ultra compact header divider
        context.fill(x + SPACING_MD, y + SPACING_XL + 6, x + width - SPACING_MD, y + SPACING_XL + 7, BORDER_COLOR);
    }

    /**
     * Draws a compact modern card with subtle shadow and hover effects
     */
    private void drawModernCard(DrawContext context, int x, int y, int width, int height, int bgColor, boolean elevated) {
        // Draw compact card shadow if elevated
        if (elevated) {
            int shadowAlpha = 10;
            int shadowColor = shadowAlpha << 24;
            context.fill(x + 1, y + 1, x + width + 1, y + height + 1, shadowColor);
        }

        // Draw card background
        context.fill(x, y, x + width, y + height, bgColor);

        // Draw subtle border
        context.drawBorder(x, y, width, height, BORDER_COLOR);

        // Add subtle inner highlight
        context.fill(x + 1, y + 1, x + width - 1, y + 2, 0x10FFFFFF);
    }

    /**
     * Draws a modern field label
     */
    private void drawModernFieldLabel(DrawContext context, String text, int x, int y) {
        context.drawTextWithShadow(this.textRenderer, text, x, y, TEXT_SECONDARY);
    }

    /**
     * Draws a modern join type button with three states
     */
    private void drawModernJoinTypeButton(DrawContext context, int x, int y, int width, int height, Town.JoinType joinType, boolean isHovered) {
        int backgroundColor;
        String text;

        // Find the matching option
        JoinTypeOption selectedOption = null;
        for (JoinTypeOption option : joinTypeOptions) {
            if (option.joinType == joinType) {
                selectedOption = option;
                break;
            }
        }

        if (selectedOption != null) {
            backgroundColor = isHovered ? blendColors(selectedOption.color, HOVER_OVERLAY, 0.3f) : selectedOption.color;
            text = selectedOption.title;
        } else {
            backgroundColor = TEXT_MUTED;
            text = "Unknown";
        }

        // Draw modern button
        drawModernCard(context, x, y, width, height, backgroundColor, isHovered);

        // Draw button text
        context.drawCenteredTextWithShadow(this.textRenderer, text,
            x + width / 2, y + (height - 8) / 2, TEXT_PRIMARY);
    }

    /**
     * Draws modern status message with auto-hide functionality
     */
    private void drawModernStatusMessage(DrawContext context, int panelX, int panelY) {
        if (statusText != Text.empty()) {
            long currentTime = System.currentTimeMillis();
            long timeSinceShow = currentTime - statusShowTime;

            // Auto-hide after duration
            if (timeSinceShow > STATUS_DISPLAY_DURATION) {
                statusText = Text.empty();
                return;
            }

            // Calculate fade-out animation
            float alpha = 1.0f;
            if (timeSinceShow > STATUS_DISPLAY_DURATION - 500) { // Fade out in last 500ms
                alpha = (STATUS_DISPLAY_DURATION - timeSinceShow) / 500.0f;
                alpha = Math.max(0.0f, Math.min(1.0f, alpha));
            }

            // Calculate responsive position (bottom center of panel, but ensure it fits on screen)
            int statusY = Math.min(panelY + panelHeight + SPACING_MD, height - 25);
            int statusX = width / 2;

            // Apply alpha to status color
            int alphaValue = (int)(alpha * 255);
            int finalStatusColor = (statusColor & 0x00FFFFFF) | (alphaValue << 24);

            // Draw status background with responsive sizing
            int textWidth = this.textRenderer.getWidth(statusText);
            int maxBgWidth = Math.min(textWidth + SPACING_MD * 2, width - SPACING_MD * 2);
            int bgWidth = maxBgWidth;
            int bgHeight = 20;
            int bgX = Math.max(SPACING_MD, Math.min(statusX - bgWidth / 2, width - bgWidth - SPACING_MD));
            int bgY = statusY - 2;

            int bgColor = (CARD_BG & 0x00FFFFFF) | (alphaValue << 24);
            drawModernCard(context, bgX, bgY, bgWidth, bgHeight, bgColor, false);

            // Draw status text
            context.drawCenteredTextWithShadow(this.textRenderer, statusText, statusX, statusY, finalStatusColor);
        }
    }

    /**
     * Blends two colors together
     */
    private int blendColors(int color1, int color2, float ratio) {
        int a1 = (color1 >> 24) & 0xFF;
        int r1 = (color1 >> 16) & 0xFF;
        int g1 = (color1 >> 8) & 0xFF;
        int b1 = color1 & 0xFF;

        int a2 = (color2 >> 24) & 0xFF;
        int r2 = (color2 >> 16) & 0xFF;
        int g2 = (color2 >> 8) & 0xFF;
        int b2 = color2 & 0xFF;

        int a = (int) (a1 + (a2 - a1) * ratio);
        int r = (int) (r1 + (r2 - r1) * ratio);
        int g = (int) (g1 + (g2 - g1) * ratio);
        int b = (int) (b1 + (b2 - b1) * ratio);

        return (a << 24) | (r << 16) | (g << 8) | b;
    }

    /**
     * Checks if the mouse is over a specific area.
     */
    private boolean isMouseOver(int mouseX, int mouseY, int x, int y, int width, int height) {
        return mouseX >= x && mouseX <= x + width && mouseY >= y && mouseY <= y + height;
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0) { // Left click
            // Clear tooltip
            currentTooltip = "";

            // Handle custom text box clicks
            if (nameField.mouseClicked(mouseX, mouseY, button)) {
                descriptionField.setFocused(false);
                return true;
            }
            if (descriptionField.mouseClicked(mouseX, mouseY, button)) {
                nameField.setFocused(false);
                return true;
            }

            // Unfocus text boxes if clicking elsewhere
            nameField.setFocused(false);
            descriptionField.setFocused(false);

            // Check for radio button clicks
            for (int i = 0; i < joinTypeOptions.length; i++) {
                if (isMouseOver((int)mouseX, (int)mouseY, radioButtonX[i], radioButtonY[i],
                    panelWidth - SPACING_LG * 2, RADIO_BUTTON_SIZE + 4)) {
                    joinType = joinTypeOptions[i].joinType;
                    playClickSound();
                    updateFormCompletion();
                    return true;
                }
            }

            // Check for slider click
            if (isMouseOver((int)mouseX, (int)mouseY, sliderX, sliderY, sliderWidth, SLIDER_HANDLE_SIZE)) {
                // Calculate new player limit based on click position
                float clickProgress = ((float)((int)mouseX - sliderX)) / (sliderWidth - SLIDER_HANDLE_SIZE);
                clickProgress = Math.max(0.0f, Math.min(1.0f, clickProgress));
                playerLimit = 5 + (int)(clickProgress * 95); // 5-100 range
                isDraggingSlider = true;
                playClickSound();
                updateFormCompletion();
                return true;
            }

            // Check for create button click
            if (isNameValid && isMouseOver((int)mouseX, (int)mouseY, createButtonX, createButtonY, buttonWidth, buttonHeight)) {
                createTown();
                return true;
            }

            // Check for cancel button click with responsive width
            int cancelButtonWidth = Math.min(buttonWidth, panelWidth - SPACING_LG * 2 - buttonWidth - SPACING_MD);
            if (isMouseOver((int)mouseX, (int)mouseY, cancelButtonX, cancelButtonY, cancelButtonWidth, buttonHeight)) {
                close();
                return true;
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        // Handle custom text box key presses
        if (nameField.keyPressed(keyCode, scanCode, modifiers)) {
            return true;
        }
        if (descriptionField.keyPressed(keyCode, scanCode, modifiers)) {
            return true;
        }
        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    @Override
    public boolean charTyped(char chr, int modifiers) {
        // Handle custom text box character input
        if (nameField.charTyped(chr, modifiers)) {
            return true;
        }
        if (descriptionField.charTyped(chr, modifiers)) {
            return true;
        }
        return super.charTyped(chr, modifiers);
    }

    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        if (button == 0) {
            isDraggingSlider = false;
        }
        return super.mouseReleased(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        if (button == 0 && isDraggingSlider) {
            // Update player limit while dragging
            float dragProgress = ((float)((int)mouseX - sliderX)) / (sliderWidth - SLIDER_HANDLE_SIZE);
            dragProgress = Math.max(0.0f, Math.min(1.0f, dragProgress));
            int newPlayerLimit = 5 + (int)(dragProgress * 95); // 5-100 range

            if (newPlayerLimit != playerLimit) {
                playerLimit = newPlayerLimit;
                updateFormCompletion();
            }
            return true;
        }
        return super.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
    }

    private void createTown() {
        String name = nameField.getText().trim();
        String description = descriptionField.getText().trim();
        Town.JoinType townJoinType = joinType;
        int maxPlayers = playerLimit;

        // Validate name again to be sure
        ValidationResult validationResult = TownNameValidator.validate(name);
        if (!validationResult.isValid()) {
            setStatus(validationResult.getMessage(), Formatting.RED);
            return;
        }

        // Check if name is already taken
        if (TownManager.getInstance().isTownNameTaken(name)) {
            setStatus("This town name is already taken", Formatting.RED);
            return;
        }

        // Use default description if empty
        if (description.isEmpty()) {
            description = "A lovely town";
        }

        // Create the town
        Town town = TownManager.getInstance().createTown(name, description, townJoinType, maxPlayers);
        if (town == null) {
            setStatus("Failed to create town", Formatting.RED);
            return;
        }

        // Add the player to the town
        if (client.player != null) {
            TownManager.getInstance().addPlayerToTown(client.player, town.getId());

            // Play success sound
            client.getSoundManager().play(PositionedSoundInstance.master(
                SoundEvents.ENTITY_PLAYER_LEVELUP, 1.0F));
        }

        // Return to the town screen
        close();
    }

    /**
     * Sets the status text with modern styling and auto-hide.
     *
     * @param message The message to display
     * @param formatting The formatting to apply
     */
    private void setStatus(String message, Formatting formatting) {
        statusText = Text.literal(message).formatted(formatting);
        statusShowTime = System.currentTimeMillis();

        // Set modern color based on formatting
        if (formatting == Formatting.RED) {
            statusColor = ACCENT_DANGER; // Modern red
        } else if (formatting == Formatting.GREEN) {
            statusColor = ACCENT_SUCCESS; // Modern green
        } else if (formatting == Formatting.YELLOW) {
            statusColor = ACCENT_WARNING; // Modern orange/yellow
        } else {
            statusColor = TEXT_PRIMARY; // Modern white
        }
    }

    /**
     * Plays a click sound.
     */
    private void playClickSound() {
        SoundUtil.playButtonClickSound();
    }

    @Override
    public void close() {
        client.setScreen(parent);
    }

    /**
     * Helper class for join type options
     */
    private static class JoinTypeOption {
        public final Town.JoinType joinType;
        public final String title;
        public final String description;
        public final int color;

        public JoinTypeOption(Town.JoinType joinType, String title, String description, int color) {
            this.joinType = joinType;
            this.title = title;
            this.description = description;
            this.color = color;
        }
    }

    /**
     * Custom text box with modern styling and responsive positioning
     */
    private static class CustomTextBox {
        private final net.minecraft.client.font.TextRenderer textRenderer;
        private int x, y, width, height; // Made non-final for responsive updates
        private final String placeholder;
        private final int maxLength;
        private String text = "";
        private boolean focused = false;
        private boolean hovered = false;
        private long lastCursorBlink = 0;
        private java.util.function.Consumer<String> changedListener;

        public CustomTextBox(net.minecraft.client.font.TextRenderer textRenderer, int x, int y, int width, int height, String placeholder, int maxLength) {
            this.textRenderer = textRenderer;
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
            this.placeholder = placeholder;
            this.maxLength = maxLength;
        }

        public void render(DrawContext context, int mouseX, int mouseY) {
            // Check if hovered
            hovered = mouseX >= x && mouseX <= x + width && mouseY >= y && mouseY <= y + height;

            // Determine colors
            int bgColor = focused ? blendColors(CARD_BG, ACCENT_PRIMARY, 0.1f) :
                         hovered ? blendColors(CARD_BG, HOVER_OVERLAY, 0.3f) : CARD_BG;
            int borderColor = focused ? ACCENT_PRIMARY : hovered ? TEXT_SECONDARY : BORDER_COLOR;

            // Draw background
            context.fill(x, y, x + width, y + height, bgColor);

            // Draw border
            context.drawBorder(x, y, width, height, borderColor);

            // Draw text or placeholder
            String displayText = text.isEmpty() ? placeholder : text;
            int textColor = text.isEmpty() ? TEXT_MUTED : TEXT_PRIMARY;

            // Clip text to fit
            String clippedText = displayText;
            int textWidth = textRenderer.getWidth(displayText);
            if (textWidth > width - TEXTBOX_PADDING * 2) {
                while (textRenderer.getWidth(clippedText + "...") > width - TEXTBOX_PADDING * 2 && clippedText.length() > 0) {
                    clippedText = clippedText.substring(0, clippedText.length() - 1);
                }
                clippedText += "...";
            }

            context.drawTextWithShadow(textRenderer, clippedText,
                x + TEXTBOX_PADDING, y + (height - 8) / 2, textColor);

            // Draw cursor if focused
            if (focused && (System.currentTimeMillis() - lastCursorBlink) % 1000 < 500) {
                int cursorX = x + TEXTBOX_PADDING + textRenderer.getWidth(text);
                if (cursorX < x + width - TEXTBOX_PADDING) {
                    context.fill(cursorX, y + 2, cursorX + 1, y + height - 2, TEXT_PRIMARY);
                }
            }
        }

        public boolean mouseClicked(double mouseX, double mouseY, int button) {
            if (button == 0) {
                boolean wasInBounds = mouseX >= x && mouseX <= x + width && mouseY >= y && mouseY <= y + height;
                focused = wasInBounds;
                return wasInBounds;
            }
            return false;
        }

        public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
            if (!focused) return false;

            if (keyCode == 259) { // Backspace
                if (!text.isEmpty()) {
                    text = text.substring(0, text.length() - 1);
                    if (changedListener != null) changedListener.accept(text);
                }
                return true;
            }
            return false;
        }

        public boolean charTyped(char chr, int modifiers) {
            if (!focused) return false;

            if (text.length() < maxLength && chr >= 32) {
                text += chr;
                if (changedListener != null) changedListener.accept(text);
                return true;
            }
            return false;
        }

        // Helper method for color blending
        private static int blendColors(int color1, int color2, float ratio) {
            int a1 = (color1 >> 24) & 0xFF;
            int r1 = (color1 >> 16) & 0xFF;
            int g1 = (color1 >> 8) & 0xFF;
            int b1 = color1 & 0xFF;

            int a2 = (color2 >> 24) & 0xFF;
            int r2 = (color2 >> 16) & 0xFF;
            int g2 = (color2 >> 8) & 0xFF;
            int b2 = color2 & 0xFF;

            int a = (int) (a1 + (a2 - a1) * ratio);
            int r = (int) (r1 + (r2 - r1) * ratio);
            int g = (int) (g1 + (g2 - g1) * ratio);
            int b = (int) (b1 + (b2 - b1) * ratio);

            return (a << 24) | (r << 16) | (g << 8) | b;
        }

        // Position update method for responsive design
        public void updatePosition(int newX, int newY, int newWidth) {
            this.x = newX;
            this.y = newY;
            this.width = newWidth;
            // Height remains the same
        }

        // Getters and setters
        public String getText() { return text; }
        public void setText(String text) { this.text = text; }
        public int getX() { return x; }
        public int getY() { return y; }
        public int getWidth() { return width; }
        public int getHeight() { return height; }
        public boolean isFocused() { return focused; }
        public void setChangedListener(java.util.function.Consumer<String> listener) { this.changedListener = listener; }
        public void setFocused(boolean focused) { this.focused = focused; }
    }
}
