package com.pokecobble.town.gui;

import com.pokecobble.town.Town;
import com.pokecobble.town.claim.ClaimHistoryEntry;
import com.pokecobble.town.claim.ClaimTag;
import com.pokecobble.town.sound.SoundUtil;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;
import net.minecraft.util.math.ChunkPos;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import org.lwjgl.glfw.GLFW;

/**
 * Screen that displays detailed claim history for a town.
 */
public class ClaimHistoryScreen extends Screen {
    private final Screen parent;
    private final Town town;
    private final List<ClaimHistoryEntry> history;

    // Panel dimensions
    private int panelWidth;
    private int panelHeight;

    // Scrolling
    private int scrollOffset = 0;
    private static final int SCROLL_AMOUNT = 15;

    // Selected entry
    private ClaimHistoryEntry selectedEntry = null;
    private boolean isEntryExpanded = false;

    // Colors
    private static final int BACKGROUND_COLOR = 0xE0101010;
    private static final int PANEL_COLOR = 0xE0202030;
    private static final int HEADER_COLOR = 0xA0303050;
    private static final int BORDER_COLOR = 0x80FFFFFF;
    private static final int SELECTED_COLOR = 0x40FFFFFF;
    private static final int HOVER_COLOR = 0x20FFFFFF;

    // Entry dimensions
    private static final int ENTRY_HEIGHT = 30;

    /**
     * Plays the button click sound.
     */
    private void playClickSound() {
        SoundUtil.playButtonClickSound();
    }

    /**
     * Plays the button click sound with a custom pitch.
     *
     * @param pitch The pitch of the sound (1.0F is normal)
     */
    private void playClickSound(float pitch) {
        SoundUtil.playButtonClickSound(pitch);
    }
    private static final int ENTRY_SPACING = 2;

    // Date formatter
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public ClaimHistoryScreen(Screen parent, Town town) {
        super(Text.literal("Claim History"));
        this.parent = parent;
        this.town = town;
        this.history = town.getClaimHistory();
    }

    @Override
    protected void init() {
        super.init();

        // Calculate panel dimensions
        panelWidth = Math.min(width - 40, 800);
        panelHeight = height - 40;

        // Reset scroll offset when screen is initialized
        scrollOffset = 0;
        selectedEntry = null;

        // Request the latest claim history from the server
        com.pokecobble.town.network.town.ClaimHistorySynchronizer.requestClaimHistory(town.getId());
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw background
        this.renderBackground(context);

        // Calculate positions
        int leftX = (width - panelWidth) / 2;
        int topY = (height - panelHeight) / 2;

        // Draw panel background
        context.fill(leftX, topY, leftX + panelWidth, topY + panelHeight, PANEL_COLOR);

        // Top header area - slightly lighter with gradient
        context.fillGradient(leftX, topY, leftX + panelWidth, topY + 40, 0xA0303050, 0xA0404060);

        // Draw title with fancy styling
        String title = "Claim History - " + town.getName();
        int titleWidth = this.textRenderer.getWidth(title);
        int titleX = leftX + (panelWidth - titleWidth) / 2;
        int titleY = topY + 15;

        // Draw static glow behind title
        int glowSize = 15;
        context.fillGradient(
            titleX - glowSize, titleY - 2,
            titleX + titleWidth + glowSize, titleY + 12,
            0x40FFFFFF, 0x10FFFFFF
        );

        // Draw title text with shadow
        context.drawTextWithShadow(this.textRenderer, title, titleX, titleY, 0xFFFFFF);

        // Draw content area
        int contentX = leftX + 10;
        int contentY = topY + 50;
        int contentWidth = panelWidth - 20;
        int contentHeight = panelHeight - 60;

        // Draw content area background with rounded corners
        drawRoundedRect(context, contentX, contentY, contentWidth, contentHeight, 0x40000000);

        // Draw history entries
        if (history.isEmpty()) {
            context.drawCenteredTextWithShadow(this.textRenderer, "No claim history available",
                leftX + panelWidth / 2, contentY + contentHeight / 2 - 30, 0xAAAAAA);

            // Draw a message explaining how history is created
            String[] helpText = {
                "Claim history is created when:",
                "- Chunks are claimed",
                "- Chunks are unclaimed",
                "- Chunk tags are modified"
            };

            int helpY = contentY + contentHeight / 2;
            for (String text : helpText) {
                context.drawCenteredTextWithShadow(this.textRenderer, text,
                    leftX + panelWidth / 2, helpY, 0x888888);
                helpY += 20; // Increased spacing between lines
            }
        } else {
            // Calculate visible area for entries
            int entriesAreaY = contentY + 10;
            int entriesAreaHeight = contentHeight - 20;

            // Calculate total height of all entries including expanded ones
            int totalHeight = 0;
            for (int i = 0; i < history.size(); i++) {
                ClaimHistoryEntry entry = history.get(i);
                if (entry == selectedEntry && isEntryExpanded) {
                    totalHeight += ENTRY_HEIGHT + 50 + ENTRY_SPACING; // Extra space for expanded content
                } else {
                    totalHeight += ENTRY_HEIGHT + ENTRY_SPACING;
                }
            }

            // Draw scrollbar if needed
            if (totalHeight > entriesAreaHeight) {
                // Calculate scrollbar dimensions
                int scrollbarWidth = 4;
                int scrollbarHeight = Math.max(20, entriesAreaHeight * entriesAreaHeight / totalHeight);
                int scrollbarX = contentX + contentWidth - 8;
                int scrollbarY = entriesAreaY + (scrollOffset * (entriesAreaHeight - scrollbarHeight) / Math.max(1, totalHeight - entriesAreaHeight));

                // Ensure scrollbar doesn't go out of bounds
                scrollbarY = Math.max(entriesAreaY, Math.min(scrollbarY, entriesAreaY + entriesAreaHeight - scrollbarHeight));

                // Draw scrollbar handle - rounded corners effect with more opacity
                drawRoundedRect(context, scrollbarX, scrollbarY, scrollbarWidth, scrollbarHeight, 0xC0FFFFFF);
            }

            // Apply scissor to clip content to visible area
            context.enableScissor(
                contentX + 10,
                entriesAreaY,
                contentX + contentWidth - 10,
                entriesAreaY + entriesAreaHeight
            );

            // Draw entries
            int entryY = entriesAreaY - scrollOffset;

            for (int i = 0; i < history.size(); i++) {
                ClaimHistoryEntry entry = history.get(i);

                // Calculate entry height based on expanded state
                int entryHeight = (entry == selectedEntry && isEntryExpanded) ? ENTRY_HEIGHT + 50 : ENTRY_HEIGHT;

                // Skip if entry is completely outside visible area
                if (entryY + entryHeight < entriesAreaY || entryY > entriesAreaY + entriesAreaHeight) {
                    entryY += entryHeight + ENTRY_SPACING;
                    continue;
                }

                // Check if mouse is hovering over this entry
                boolean isHovered = mouseX >= contentX + 10 && mouseX <= contentX + contentWidth - 10 &&
                                   mouseY >= entryY && mouseY <= entryY + ENTRY_HEIGHT &&
                                   mouseY >= entriesAreaY && mouseY <= entriesAreaY + entriesAreaHeight;

                // Check if entry is selected
                boolean isSelected = entry == selectedEntry;

                // Draw entry background
                int bgColor = isSelected ? SELECTED_COLOR : (isHovered ? HOVER_COLOR : (i % 2 == 0 ? 0x20FFFFFF : 0x30FFFFFF));
                drawRoundedRect(context, contentX + 10, entryY, contentWidth - 20, ENTRY_HEIGHT, bgColor);

                // Determine color based on action
                int actionColor;
                switch (entry.getAction()) {
                    case CLAIM:
                        actionColor = 0x55FF55; // Green
                        break;
                    case UNCLAIM:
                        actionColor = 0xFF5555; // Red
                        break;
                    case MODIFY:
                        actionColor = 0xFFAA00; // Orange
                        break;
                    default:
                        actionColor = 0xFFFFFF; // White
                }

                // Draw colored accent on the left side
                context.fill(contentX + 10, entryY + 2, contentX + 12, entryY + ENTRY_HEIGHT - 2, actionColor);

                // Draw expand/collapse indicator
                String expandIndicator = (entry == selectedEntry && isEntryExpanded) ? "▼" : "▶";
                context.drawTextWithShadow(this.textRenderer, expandIndicator,
                    contentX + 14, entryY + (ENTRY_HEIGHT - 8) / 2, 0xFFFFFF);

                // Draw action text
                context.drawTextWithShadow(this.textRenderer, entry.getFormattedAction(),
                    contentX + 25, entryY + 6, actionColor);

                // Draw chunk position
                context.drawTextWithShadow(this.textRenderer, entry.getFormattedChunkPos(),
                    contentX + 90, entryY + 6, 0xFFFFFF);

                // Draw player name
                context.drawTextWithShadow(this.textRenderer, "by " + entry.getPlayerName(),
                    contentX + 170, entryY + 6, 0xAAAAAA);

                // Draw time (right-aligned)
                String timeText = entry.getFormattedTimestamp();
                int timeWidth = this.textRenderer.getWidth(timeText);
                context.drawTextWithShadow(this.textRenderer, timeText,
                    contentX + contentWidth - 30 - timeWidth, entryY + 6, 0xAAAAAA);

                // If selected and expanded, draw additional details
                if (isSelected && isEntryExpanded) {
                    // Draw detailed info below the entry
                    int detailsY = entryY + ENTRY_HEIGHT + 5;

                    // Only render if visible
                    if (detailsY < entriesAreaY + entriesAreaHeight) {
                        // Draw a background for the expanded area
                        drawRoundedRect(context, contentX + 15, detailsY - 2, contentWidth - 30, 45, 0x30000000);

                        // Draw detailed description
                        context.drawTextWithShadow(this.textRenderer, entry.getDetailedDescription(),
                            contentX + 25, detailsY, 0xFFFFFF);

                        // Draw timestamp
                        String dateStr = dateFormat.format(entry.getTimestamp());
                        context.drawTextWithShadow(this.textRenderer, "Date: " + dateStr,
                            contentX + 25, detailsY + 15, 0xAAAAAA);

                        // Draw additional info based on action type
                        switch (entry.getAction()) {
                            case CLAIM:
                                ClaimTag tag = entry.getTag();
                                if (tag != null) {
                                    context.drawTextWithShadow(this.textRenderer, "Tag: " + tag.getName(),
                                        contentX + 25, detailsY + 30, tag.getColor());
                                }
                                break;

                            case UNCLAIM:
                                ClaimTag prevTag = entry.getTag();
                                if (prevTag != null) {
                                    context.drawTextWithShadow(this.textRenderer, "Previous Tag: " + prevTag.getName(),
                                        contentX + 25, detailsY + 30, prevTag.getColor());
                                }
                                break;

                            case MODIFY:
                                ClaimTag oldTag = entry.getPreviousTag();
                                ClaimTag newTag = entry.getTag();
                                if (oldTag != null) {
                                    context.drawTextWithShadow(this.textRenderer, "Changed from: " + oldTag.getName(),
                                        contentX + 25, detailsY + 30, oldTag.getColor());
                                }
                                if (newTag != null) {
                                    context.drawTextWithShadow(this.textRenderer, "to: " + newTag.getName(),
                                        contentX + 200, detailsY + 30, newTag.getColor());
                                }
                                break;
                        }
                    }

                    // Use expanded height for this entry
                    entryY += ENTRY_HEIGHT + 50 + ENTRY_SPACING;
                } else {
                    // Use normal height for this entry
                    entryY += ENTRY_HEIGHT + ENTRY_SPACING;
                }
            }

            // Disable scissor
            context.disableScissor();
        }

        // Draw buttons
        super.render(context, mouseX, mouseY, delta);
    }



    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (super.mouseClicked(mouseX, mouseY, button)) {
            return true;
        }

        // Check for clicks on history entries
        if (button == 0 && !history.isEmpty()) { // Left click
            int leftX = (width - panelWidth) / 2;
            int topY = (height - panelHeight) / 2;
            int contentX = leftX + 10;
            int contentY = topY + 50;
            int contentWidth = panelWidth - 20;
            int contentHeight = panelHeight - 60;

            // Calculate visible area for entries
            int entriesAreaY = contentY + 10;
            int entriesAreaHeight = contentHeight - 20;

            // Check if click is within entries area
            if (mouseX >= contentX + 10 && mouseX <= contentX + contentWidth - 10 &&
                mouseY >= entriesAreaY && mouseY <= entriesAreaY + entriesAreaHeight) {

                // Calculate which entry was clicked
                int entryY = entriesAreaY - scrollOffset;
                int clickedIndex = -1;

                for (int i = 0; i < history.size(); i++) {
                    // Skip if entry is completely outside visible area
                    if (entryY + ENTRY_HEIGHT < entriesAreaY || entryY > entriesAreaY + entriesAreaHeight) {
                        entryY += ENTRY_HEIGHT + ENTRY_SPACING;
                        continue;
                    }

                    // Check if this entry was clicked
                    if (mouseY >= entryY && mouseY <= entryY + ENTRY_HEIGHT) {
                        clickedIndex = i;
                        break;
                    }

                    entryY += ENTRY_HEIGHT + ENTRY_SPACING;
                }

                // If a valid entry was clicked
                if (clickedIndex >= 0 && clickedIndex < history.size()) {
                    ClaimHistoryEntry clickedEntry = history.get(clickedIndex);

                    // Toggle selection and expansion
                    if (selectedEntry == clickedEntry) {
                        // If already selected, toggle expanded state
                        isEntryExpanded = !isEntryExpanded;
                    } else {
                        // Select new entry and expand it
                        selectedEntry = clickedEntry;
                        isEntryExpanded = true;
                    }

                    // Play click sound
                    playClickSound();

                    // Recalculate total height to update scrolling
                    int totalHeight = 0;
                    for (int i = 0; i < history.size(); i++) {
                        ClaimHistoryEntry entry = history.get(i);
                        if (entry == selectedEntry && isEntryExpanded) {
                            totalHeight += ENTRY_HEIGHT + 50 + ENTRY_SPACING;
                        } else {
                            totalHeight += ENTRY_HEIGHT + ENTRY_SPACING;
                        }
                    }

                    // Adjust scroll if needed to keep the expanded entry visible
                    int maxScroll = Math.max(0, totalHeight - entriesAreaHeight);

                    // If we're expanding and the expanded content would be off-screen, scroll to show it
                    if (isEntryExpanded) {
                        // Calculate the bottom of the expanded entry
                        int expandedEntryY = entriesAreaY - scrollOffset;
                        for (int i = 0; i < clickedIndex; i++) {
                            ClaimHistoryEntry entry = history.get(i);
                            expandedEntryY += (entry == selectedEntry && isEntryExpanded) ?
                                ENTRY_HEIGHT + 50 + ENTRY_SPACING : ENTRY_HEIGHT + ENTRY_SPACING;
                        }

                        int expandedBottom = expandedEntryY + ENTRY_HEIGHT + 50;
                        if (expandedBottom > entriesAreaY + entriesAreaHeight) {
                            // Scroll down just enough to show the expanded content
                            scrollOffset = Math.min(maxScroll, scrollOffset + (expandedBottom - (entriesAreaY + entriesAreaHeight)));
                        }
                    }

                    return true;
                }
            }
        } else if (button == 0 && history.isEmpty()) {
            // If history is empty and user clicks anywhere, play a sound to indicate nothing to select
            playClickSound(0.5F);
        }

        return false;
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        // If history is empty, don't allow scrolling
        if (history.isEmpty()) {
            return super.mouseScrolled(mouseX, mouseY, amount);
        }

        // Calculate content area
        int leftX = (width - panelWidth) / 2;
        int topY = (height - panelHeight) / 2;
        int contentX = leftX + 10;
        int contentY = topY + 50;
        int contentWidth = panelWidth - 20;
        int contentHeight = panelHeight - 60;

        // Calculate visible area for entries
        int entriesAreaY = contentY + 10;
        int entriesAreaHeight = contentHeight - 20;

        // Check if mouse is over entries area
        if (mouseX >= contentX + 10 && mouseX <= contentX + contentWidth - 10 &&
            mouseY >= entriesAreaY && mouseY <= entriesAreaY + entriesAreaHeight) {

            // Scroll the content
            scrollOffset -= (int) (amount * SCROLL_AMOUNT);

            // Calculate total height of all entries including expanded ones
            int totalHeight = 0;
            for (int i = 0; i < history.size(); i++) {
                ClaimHistoryEntry entry = history.get(i);
                if (entry == selectedEntry && isEntryExpanded) {
                    totalHeight += ENTRY_HEIGHT + 50 + ENTRY_SPACING;
                } else {
                    totalHeight += ENTRY_HEIGHT + ENTRY_SPACING;
                }
            }

            // Clamp scroll offset
            int maxScroll = Math.max(0, totalHeight - entriesAreaHeight);
            scrollOffset = Math.max(0, Math.min(scrollOffset, maxScroll));

            return true;
        }

        return super.mouseScrolled(mouseX, mouseY, amount);
    }

    @Override
    public void close() {
        this.client.setScreen(parent);
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        if (keyCode == GLFW.GLFW_KEY_ESCAPE) {
            this.close();
            return true;
        }
        return super.keyPressed(keyCode, scanCode, modifiers);
    }



    @Override
    public boolean shouldPause() {
        return false;
    }

    /**
     * Draws a rounded rectangle effect (actual corners aren't rounded, but it creates that visual effect)
     */
    private void drawRoundedRect(DrawContext context, int x, int y, int width, int height, int color) {
        // Main rectangle
        context.fill(x, y, x + width, y + height, color);

        // Lighter top and left edges for rounded effect
        int lightEdge = (color & 0x00FFFFFF) | 0x10FFFFFF;
        context.fill(x, y, x + width, y + 1, lightEdge);
        context.fill(x, y, x + 1, y + height, lightEdge);

        // Darker bottom and right edges for rounded effect
        int darkEdge = (color & 0x00FFFFFF) | 0x10000000;
        context.fill(x, y + height - 1, x + width, y + height, darkEdge);
        context.fill(x + width - 1, y, x + width, y + height, darkEdge);
    }
}
