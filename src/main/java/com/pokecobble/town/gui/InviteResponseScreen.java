package com.pokecobble.town.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.client.InviteNotification;
import com.pokecobble.town.network.NetworkManager;
import com.pokecobble.town.network.town.TownInviteHandler;
import com.pokecobble.town.sound.SoundUtil;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.sound.PositionedSoundInstance;
import net.minecraft.sound.SoundEvents;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;
import net.minecraft.util.Identifier;

import java.util.UUID;

/**
 * Screen for responding to town invitations.
 */
@Environment(EnvType.CLIENT)
public class InviteResponseScreen extends Screen implements TownInviteHandler.InviteUpdateCallback {
    private final Screen parent;
    private final UUID townId;
    private final String townName;
    private Town town;

    // Panel dimensions - more compact
    private int panelWidth = 320;
    private int panelHeight = 240;
    private int leftX;
    private int topY;

    // Button dimensions
    private static final int BUTTON_WIDTH = 100;
    private static final int BUTTON_HEIGHT = 20;

    // UI colors
    private static final int HEADER_COLOR_TOP = 0xA0303050;
    private static final int HEADER_COLOR_BOTTOM = 0xA0404060;
    private static final int PANEL_COLOR_TOP = 0xD0101010;
    private static final int PANEL_COLOR_BOTTOM = 0xD0202030;
    private static final int CONTENT_BG_COLOR = 0x30000000;
    private static final int ACCEPT_BUTTON_COLOR = 0xFF4CAF50; // Green
    private static final int DECLINE_BUTTON_COLOR = 0xFFE53935; // Red

    // Network constants
    private static final Identifier JOIN_TOWN_PACKET_ID = new Identifier(Pokecobbleclaim.MOD_ID, "join_town");
    private static final Identifier DECLINE_INVITE_PACKET_ID = new Identifier(Pokecobbleclaim.MOD_ID, "decline_invite");

    /**
     * Creates a new invite response screen.
     *
     * @param parent The parent screen
     * @param townId The ID of the town that sent the invitation
     * @param townName The name of the town that sent the invitation
     */
    public InviteResponseScreen(Screen parent, UUID townId, String townName) {
        super(Text.literal("Town Invitation"));
        this.parent = parent;
        this.townId = townId;
        this.townName = townName;
    }

    @Override
    protected void init() {
        super.init();

        // Calculate panel dimensions based on screen size
        panelWidth = Math.min(width - 20, 500);
        panelHeight = height - 20;
        leftX = (width - panelWidth) / 2;
        topY = 10;

        // Register for invite updates
        TownInviteHandler.registerInviteUpdateCallback(this);

        // Try to get town information
        town = TownManager.getInstance().getTown(townId);
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Render background
        this.renderBackground(context);

        // Draw panel background with gradient
        context.fillGradient(leftX, topY, leftX + panelWidth, topY + panelHeight, PANEL_COLOR_TOP, PANEL_COLOR_BOTTOM);

        // Draw header with gradient
        context.fillGradient(leftX, topY, leftX + panelWidth, topY + 20, HEADER_COLOR_TOP, HEADER_COLOR_BOTTOM);

        // Draw title centered in header
        context.drawCenteredTextWithShadow(this.textRenderer, Text.literal("Town Invitation").formatted(Formatting.GOLD, Formatting.BOLD),
                width / 2, topY + 6, 0xFFFFFF);

        // Calculate content area dimensions - more compact
        int contentX = leftX + 10;
        int contentY = topY + 25;
        int contentWidth = panelWidth - 20;
        int contentHeight = panelHeight - 50;

        // Draw content area background
        context.fill(contentX, contentY, contentX + contentWidth, contentY + contentHeight, CONTENT_BG_COLOR);

        // Draw town name with more emphasis
        int nameY = contentY + 15;
        context.drawCenteredTextWithShadow(this.textRenderer, Text.literal("You have been invited to join:").formatted(Formatting.WHITE),
                width / 2, nameY, 0xFFFFFF);

        int townNameY = nameY + 20;
        context.drawCenteredTextWithShadow(this.textRenderer, Text.literal(townName).formatted(Formatting.YELLOW, Formatting.BOLD),
                width / 2, townNameY, 0xFFFFFF);

        // Draw town information if available
        int infoY = townNameY + 30;
        if (town != null) {
            // Draw town information in a more structured layout - more compact
            int leftColumnX = contentX + 10;
            int rightColumnX = contentX + contentWidth / 2 + 5;
            int rowHeight = 16;

            // Draw town description
            context.drawTextWithShadow(this.textRenderer, Text.literal("Description:").formatted(Formatting.WHITE, Formatting.BOLD),
                    leftColumnX, infoY, 0xFFFFFF);

            String description = town.getDescription();
            if (description == null || description.isEmpty()) {
                description = "No description available";
            }

            // Wrap description text - more compact
            int maxWidth = contentWidth - 20;
            java.util.List<String> wrappedText = wrapText(description, maxWidth);

            int descY = infoY + 12;
            // Limit to 3 lines maximum for compactness
            int maxLines = Math.min(3, wrappedText.size());
            for (int i = 0; i < maxLines; i++) {
                String line = wrappedText.get(i);
                if (i == maxLines - 1 && wrappedText.size() > maxLines) {
                    // Add ellipsis if there are more lines
                    line = line.substring(0, Math.min(line.length(), 30)) + "...";
                }
                context.drawTextWithShadow(this.textRenderer, Text.literal(line).formatted(Formatting.GRAY),
                        leftColumnX + 8, descY, 0xFFFFFF);
                descY += 10;
            }

            // Draw town stats in a box - more compact
            int statsBoxY = descY + 10;
            int statsBoxHeight = 65;
            int statsBoxWidth = contentWidth - 20;

            // Draw stats box background
            context.fill(leftColumnX, statsBoxY, leftColumnX + statsBoxWidth, statsBoxY + statsBoxHeight, 0x20FFFFFF);

            // Draw player count with icon-like prefix - more compact
            int statY = statsBoxY + 8;
            context.drawTextWithShadow(this.textRenderer,
                    Text.literal("⛏ Players: ").formatted(Formatting.WHITE)
                            .append(Text.literal(String.valueOf(town.getPlayerCount())).formatted(Formatting.AQUA))
                            .append(Text.literal(" / ").formatted(Formatting.WHITE))
                            .append(Text.literal(String.valueOf(town.getMaxPlayers())).formatted(Formatting.AQUA)),
                    leftColumnX + 8, statY, 0xFFFFFF);

            // Draw join type with icon-like prefix - more compact
            statY += 16;
            String joinTypeText;
            Formatting joinTypeFormatting;

            switch (town.getJoinType()) {
                case OPEN:
                    joinTypeText = "Open";
                    joinTypeFormatting = Formatting.GREEN;
                    break;
                case CLOSED:
                    joinTypeText = "Closed";
                    joinTypeFormatting = Formatting.RED;
                    break;
                case INVITE_ONLY:
                    joinTypeText = "Invite Only";
                    joinTypeFormatting = Formatting.BLUE;
                    break;
                default:
                    joinTypeText = "Unknown";
                    joinTypeFormatting = Formatting.GRAY;
                    break;
            }

            context.drawTextWithShadow(this.textRenderer,
                    Text.literal("⚙ Join Type: ").formatted(Formatting.WHITE)
                            .append(Text.literal(joinTypeText).formatted(joinTypeFormatting)),
                    leftColumnX + 8, statY, 0xFFFFFF);

            // Draw claim count with icon-like prefix - more compact
            statY += 16;
            context.drawTextWithShadow(this.textRenderer,
                    Text.literal("⬛ Claims: ").formatted(Formatting.WHITE)
                            .append(Text.literal(String.valueOf(town.getClaimCount())).formatted(Formatting.YELLOW)),
                    leftColumnX + 8, statY, 0xFFFFFF);
        } else {
            // Town information not available
            context.drawCenteredTextWithShadow(this.textRenderer, Text.literal("Town information not available").formatted(Formatting.RED),
                    width / 2, infoY, 0xFFFFFF);
        }

        // Draw buttons at the bottom
        int acceptButtonX = leftX + panelWidth / 2 - BUTTON_WIDTH - 10;
        int buttonY = topY + panelHeight - BUTTON_HEIGHT - 15;
        boolean acceptHovered = mouseX >= acceptButtonX && mouseX <= acceptButtonX + BUTTON_WIDTH &&
                mouseY >= buttonY && mouseY <= buttonY + BUTTON_HEIGHT;

        drawModernButton(context, acceptButtonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT,
                ACCEPT_BUTTON_COLOR, acceptHovered, true);
        context.drawCenteredTextWithShadow(this.textRenderer, "Accept",
                acceptButtonX + BUTTON_WIDTH / 2, buttonY + 6, 0xFFFFFF);

        // Draw decline button
        int declineButtonX = leftX + panelWidth / 2 + 10;
        boolean declineHovered = mouseX >= declineButtonX && mouseX <= declineButtonX + BUTTON_WIDTH &&
                mouseY >= buttonY && mouseY <= buttonY + BUTTON_HEIGHT;

        drawModernButton(context, declineButtonX, buttonY, BUTTON_WIDTH, BUTTON_HEIGHT,
                DECLINE_BUTTON_COLOR, declineHovered, true);
        context.drawCenteredTextWithShadow(this.textRenderer, "Decline",
                declineButtonX + BUTTON_WIDTH / 2, buttonY + 6, 0xFFFFFF);

        super.render(context, mouseX, mouseY, delta);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button == 0) { // Left click
            // Check accept button
            int acceptButtonX = leftX + panelWidth / 2 - BUTTON_WIDTH - 10;
            int buttonY = topY + panelHeight - BUTTON_HEIGHT - 20;
            if (mouseX >= acceptButtonX && mouseX <= acceptButtonX + BUTTON_WIDTH &&
                mouseY >= buttonY && mouseY <= buttonY + BUTTON_HEIGHT) {

                // Accept invitation
                acceptInvitation();
                playClickSound();
                return true;
            }

            // Check decline button
            int declineButtonX = leftX + panelWidth / 2 + 10;
            if (mouseX >= declineButtonX && mouseX <= declineButtonX + BUTTON_WIDTH &&
                mouseY >= buttonY && mouseY <= buttonY + BUTTON_HEIGHT) {

                // Decline invitation
                declineInvitation();
                playClickSound();
                return true;
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    /**
     * Accepts the invitation and joins the town.
     */
    private void acceptInvitation() {
        try {
            // Create packet buffer
            net.minecraft.network.PacketByteBuf buf = PacketByteBufs.create();

            // Write town ID
            buf.writeUuid(townId);

            // Send packet to server
            ClientPlayNetworking.send(JOIN_TOWN_PACKET_ID, buf);

            // Clear the pending invitation
            InviteNotification.clearPendingInvite();

            // Close the screen
            this.client.setScreen(null);

            // Show notification
            NotificationManager.getInstance().addSuccessNotification("You have joined " + townName + "!");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error accepting invitation: " + e.getMessage());
            NotificationManager.getInstance().addErrorNotification("Error accepting invitation");
        }
    }

    /**
     * Declines the invitation.
     */
    private void declineInvitation() {
        try {
            // Create packet buffer
            net.minecraft.network.PacketByteBuf buf = PacketByteBufs.create();

            // Write town ID
            buf.writeUuid(townId);

            // Send packet to server
            ClientPlayNetworking.send(DECLINE_INVITE_PACKET_ID, buf);

            // Clear the pending invitation
            InviteNotification.clearPendingInvite();

            // Return to parent screen
            this.client.setScreen(parent);

            // Show notification
            NotificationManager.getInstance().addInfoNotification("Invitation declined");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error declining invitation: " + e.getMessage());
            NotificationManager.getInstance().addErrorNotification("Error declining invitation");
        }
    }

    /**
     * Plays a click sound.
     */
    private void playClickSound() {
        SoundUtil.playButtonClickSound();
    }

    /**
     * Wraps text to fit within a specified width.
     *
     * @param text The text to wrap
     * @param maxWidth The maximum width in pixels
     * @return A list of wrapped text lines
     */
    private java.util.List<String> wrapText(String text, int maxWidth) {
        java.util.List<String> lines = new java.util.ArrayList<>();
        String[] words = text.split(" ");
        StringBuilder currentLine = new StringBuilder();

        for (String word : words) {
            String testLine = currentLine.toString() + (currentLine.length() > 0 ? " " : "") + word;
            if (this.textRenderer.getWidth(testLine) <= maxWidth) {
                if (currentLine.length() > 0) {
                    currentLine.append(" ");
                }
                currentLine.append(word);
            } else {
                if (currentLine.length() > 0) {
                    lines.add(currentLine.toString());
                    currentLine = new StringBuilder(word);
                } else {
                    // Word is too long for a single line, force add it
                    lines.add(word);
                }
            }
        }

        if (currentLine.length() > 0) {
            lines.add(currentLine.toString());
        }

        return lines;
    }

    /**
     * Called when an invite is updated.
     * Implements the InviteUpdateCallback interface.
     *
     * @param updatedTownId The ID of the town whose invite was updated
     * @param updatedTownName The name of the town whose invite was updated
     * @param isInvite True if this is a new invite, false if the invite was processed
     */
    @Override
    public void onInviteUpdate(UUID updatedTownId, String updatedTownName, boolean isInvite) {
        // If this is our town and the invite was processed (not a new invite), close the screen
        if (updatedTownId.equals(this.townId) && !isInvite) {
            this.client.execute(() -> {
                this.client.setScreen(parent);
            });
        }
    }

    /**
     * Called when the screen is removed.
     */
    @Override
    public void removed() {
        super.removed();

        // Unregister from invite updates
        TownInviteHandler.unregisterInviteUpdateCallback(this);
    }

    /**
     * Draws a modern button with gradient and subtle 3D effect.
     */
    private void drawModernButton(DrawContext context, int x, int y, int width, int height, int color, boolean isHovered, boolean isSmall) {
        // Extract RGB components
        int r = (color >> 16) & 0xFF;
        int g = (color >> 8) & 0xFF;
        int b = color & 0xFF;

        // Adjust colors based on state
        if (isHovered) {
            // Brighten for hover state
            r = Math.min(255, r + 30);
            g = Math.min(255, g + 30);
            b = Math.min(255, b + 30);
        }

        // Create colors for gradient
        int topColor = ((r) << 16) | ((g) << 8) | (b) | 0xFF000000;
        int bottomColor = ((r * 3/4) << 16) | ((g * 3/4) << 8) | (b * 3/4) | 0xFF000000;

        // Draw gradient background
        context.fillGradient(x, y, x + width, y + height, topColor, bottomColor);

        // Draw subtle 3D effect
        int highlightColor = 0x30FFFFFF; // Subtle white highlight
        int shadowColor = 0x30000000; // Subtle shadow

        // Top highlight
        context.fill(x + 1, y + 1, x + width - 1, y + 2, highlightColor);
        // Left highlight
        context.fill(x + 1, y + 1, x + 2, y + height - 1, highlightColor);

        // Bottom shadow
        context.fill(x + 2, y + height - 2, x + width - 1, y + height - 1, shadowColor);
        // Right shadow
        context.fill(x + width - 2, y + 2, x + width - 1, y + height - 2, shadowColor);
    }
}
