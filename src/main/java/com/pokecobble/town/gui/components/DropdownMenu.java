package com.pokecobble.town.gui.components;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.font.TextRenderer;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

/**
 * A dropdown menu component for UI screens.
 */
public class DropdownMenu<T> {
    private final int x;
    private final int y;
    private final int width;
    private final int height;
    private final List<T> options;
    private final List<String> optionLabels;
    private final Consumer<T> onSelect;
    private final TextRenderer textRenderer;

    private T selectedOption;
    private boolean expanded = false;
    private int maxVisibleOptions = 5;

    // UI colors
    private static final int BACKGROUND_COLOR = 0xFF303050;
    private static final int BACKGROUND_HOVER_COLOR = 0xFF404060;
    private static final int DROPDOWN_BACKGROUND = 0xE0202030;
    private static final int DROPDOWN_HOVER = 0xE0303050;
    private static final int BORDER_COLOR = 0x60FFFFFF;

    /**
     * Creates a new dropdown menu.
     *
     * @param x The x position
     * @param y The y position
     * @param width The width
     * @param height The height
     * @param options The list of options
     * @param optionLabels The list of option labels
     * @param initialSelection The initially selected option
     * @param onSelect The callback when an option is selected
     */
    public DropdownMenu(int x, int y, int width, int height, List<T> options, List<String> optionLabels,
                        T initialSelection, Consumer<T> onSelect) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
        this.options = new ArrayList<>(options);
        this.optionLabels = new ArrayList<>(optionLabels);
        this.selectedOption = initialSelection;
        this.onSelect = onSelect;
        this.textRenderer = MinecraftClient.getInstance().textRenderer;
    }

    /**
     * Renders the dropdown menu.
     *
     * @param context The draw context
     * @param mouseX The mouse x position
     * @param mouseY The mouse y position
     */
    public void render(DrawContext context, int mouseX, int mouseY) {
        boolean isHovered = mouseX >= x && mouseX <= x + width && mouseY >= y && mouseY <= y + height;

        // Draw main button
        drawModernButton(context, x, y, width, height, isHovered);

        // Draw selected option text
        String selectedText = getSelectedLabel();
        context.drawCenteredTextWithShadow(textRenderer, selectedText, x + width / 2, y + (height - 8) / 2, 0xFFFFFF);

        // Draw dropdown arrow
        String arrow = expanded ? "▲" : "▼";
        context.drawTextWithShadow(textRenderer, arrow, x + width - 12, y + (height - 8) / 2, 0xFFFFFF);

        // Draw dropdown if expanded
        if (expanded) {
            int dropdownHeight = Math.min(options.size(), maxVisibleOptions) * height;
            int dropdownY = y + height;

            // Draw dropdown background
            context.fill(x, dropdownY, x + width, dropdownY + dropdownHeight, DROPDOWN_BACKGROUND);
            context.drawBorder(x, dropdownY, width, dropdownHeight, BORDER_COLOR);

            // Draw options
            for (int i = 0; i < options.size() && i < maxVisibleOptions; i++) {
                int optionY = dropdownY + i * height;
                boolean optionHovered = mouseX >= x && mouseX <= x + width &&
                                       mouseY >= optionY && mouseY <= optionY + height;

                // Draw option background
                if (optionHovered) {
                    context.fill(x + 1, optionY, x + width - 1, optionY + height, DROPDOWN_HOVER);
                }

                // Draw option text
                String optionText = optionLabels.get(i);
                boolean isSelected = false;

                // Safely compare options, handling null values
                T option = options.get(i);
                if (option == null && selectedOption == null) {
                    isSelected = true;
                } else if (option != null && selectedOption != null && option.equals(selectedOption)) {
                    isSelected = true;
                }

                if (isSelected) {
                    optionText = Formatting.BOLD + optionText;
                }

                context.drawCenteredTextWithShadow(textRenderer, optionText,
                    x + width / 2, optionY + (height - 8) / 2, 0xFFFFFF);
            }
        }
    }

    /**
     * Handles mouse clicks.
     *
     * @param mouseX The mouse x position
     * @param mouseY The mouse y position
     * @param button The mouse button
     * @return True if the click was handled, false otherwise
     */
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (button != 0) return false; // Only handle left clicks

        // Check if main button was clicked
        if (mouseX >= x && mouseX <= x + width && mouseY >= y && mouseY <= y + height) {
            expanded = !expanded;
            return true;
        }

        // Check if dropdown option was clicked
        if (expanded) {
            int dropdownY = y + height;
            int dropdownHeight = Math.min(options.size(), maxVisibleOptions) * height;

            if (mouseX >= x && mouseX <= x + width && mouseY >= dropdownY && mouseY <= dropdownY + dropdownHeight) {
                int optionIndex = (int)((mouseY - dropdownY) / height);
                if (optionIndex >= 0 && optionIndex < options.size()) {
                    T option = options.get(optionIndex);

                    // Only update if the selection has changed
                    boolean selectionChanged = false;
                    if (option == null && selectedOption != null) {
                        selectionChanged = true;
                    } else if (option != null && selectedOption == null) {
                        selectionChanged = true;
                    } else if (option != null && selectedOption != null && !option.equals(selectedOption)) {
                        selectionChanged = true;
                    }

                    if (selectionChanged) {
                        selectedOption = option;

                        // Call the callback
                        if (onSelect != null) {
                            onSelect.accept(option);
                        }
                    }

                    expanded = false;

                    return true;
                }
            }
        }

        // Close dropdown if clicked outside
        if (expanded) {
            expanded = false;
            return true;
        }

        return false;
    }

    /**
     * Gets the selected option.
     *
     * @return The selected option
     */
    public T getSelectedOption() {
        return selectedOption;
    }

    /**
     * Gets the label of the selected option.
     *
     * @return The label of the selected option
     */
    private String getSelectedLabel() {
        int index = options.indexOf(selectedOption);
        if (index >= 0 && index < optionLabels.size()) {
            return optionLabels.get(index);
        }
        return "";
    }

    /**
     * Sets the selected option.
     *
     * @param option The option to select
     */
    public void setSelectedOption(T option) {
        if (options.contains(option)) {
            selectedOption = option;
        }
    }

    /**
     * Draws a modern button with the mod's styling.
     *
     * @param context The draw context
     * @param x The x position
     * @param y The y position
     * @param width The width
     * @param height The height
     * @param isHovered Whether the button is hovered
     */
    private void drawModernButton(DrawContext context, int x, int y, int width, int height, boolean isHovered) {
        // Draw button background
        int bgColor = isHovered ? BACKGROUND_HOVER_COLOR : BACKGROUND_COLOR;
        context.fill(x, y, x + width, y + height, bgColor);

        // Draw button border
        context.drawBorder(x, y, width, height, BORDER_COLOR);

        // Draw subtle glass highlight at the top
        context.fill(x + 1, y + 1, x + width - 1, y + 2, 0x20FFFFFF);
        context.fill(x + 1, y + 1, x + 2, y + height - 1, 0x20FFFFFF);

        // Draw subtle shadow at the bottom
        context.fill(x + 1, y + height - 2, x + width - 1, y + height - 1, 0x20000000);
        context.fill(x + width - 2, y + 1, x + width - 1, y + height - 1, 0x20000000);
    }
}
