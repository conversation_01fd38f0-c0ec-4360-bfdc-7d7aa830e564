package com.pokecobble.town.gui;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.client.InviteNotification;
import com.pokecobble.browser.ui.ScreenToggleManager;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.screen.Screen;

/**
 * Manages opening and closing the town screen.
 */
public class TownScreenManager {

    /**
     * Opens the town screen using the current UI mode (HTML or Minecraft GUI).
     */
    public static void openTownScreen() {
        Pokecobbleclaim.LOGGER.info("Opening town screen");
        MinecraftClient client = MinecraftClient.getInstance();
        if (client != null) {
            // Check if there's a pending invitation
            if (InviteNotification.hasPendingInvite()) {
                // Open the invite response screen (always use Minecraft GUI for invitations)
                client.execute(() -> client.setScreen(new InviteResponseScreen(
                    client.currentScreen,
                    InviteNotification.getPendingInviteTownId(),
                    InviteNotification.getPendingInviteTownName()
                )));
            } else {
                // Use the screen toggle manager to open the appropriate UI
                ScreenToggleManager toggleManager = ScreenToggleManager.getInstance();
                Screen townScreen = toggleManager.openScreen(ModernTownScreen.class, client.currentScreen);

                if (townScreen != null) {
                    client.execute(() -> client.setScreen(townScreen));
                } else {
                    // Fallback to Minecraft GUI if HTML screen creation failed
                    Pokecobbleclaim.LOGGER.warn("Failed to create town screen, falling back to Minecraft GUI");
                    client.execute(() -> client.setScreen(new ModernTownScreen(client.currentScreen)));
                }
            }
        }
    }
}
