package com.pokecobble.town.gui;

import com.pokecobble.town.claim.ClaimTag;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.text.Text;

/**
 * A screen for selecting colors for claim tags.
 */
public class ColorPickerScreen extends Screen {
    private final MinecraftClient client;
    private final ClaimTagScreen parentScreen;
    private final ClaimTag tag;

    // Panel dimensions
    private int panelWidth;
    private int panelHeight;
    private int leftX;
    private int topY;

    // Color grid dimensions - smaller cells in a wider layout
    private static final int COLORS_PER_ROW = 32; // Doubled from 16 to 32
    private static final int COLOR_CELL_SIZE = 12; // Reduced from 16 to 12
    private static final int COLOR_PADDING = 1; // Reduced from 2 to 1
    private static final int ROWS = 8; // Reduced from 16 to 8

    // Colors array - reorganized for wider layout (32 colors per row, 8 rows)
    private static final int[] COLORS = {
        // Row 1: Full spectrum at 100% brightness
        0xFF0000, 0xFF2000, 0xFF4000, 0xFF6000, 0xFF8000, 0xFFA000, 0xFFC000, 0xFFE000, // Red to Yellow
        0xFFFF00, 0xE0FF00, 0xC0FF00, 0xA0FF00, 0x80FF00, 0x60FF00, 0x40FF00, 0x20FF00, // Yellow to Green
        0x00FF00, 0x00FF20, 0x00FF40, 0x00FF60, 0x00FF80, 0x00FFA0, 0x00FFC0, 0x00FFE0, // Green to Cyan
        0x00FFFF, 0x00E0FF, 0x00C0FF, 0x00A0FF, 0x0080FF, 0x0060FF, 0x0040FF, 0x0020FF, // Cyan to Blue

        // Row 2: Full spectrum continued
        0x0000FF, 0x2000FF, 0x4000FF, 0x6000FF, 0x8000FF, 0xA000FF, 0xC000FF, 0xE000FF, // Blue to Magenta
        0xFF00FF, 0xFF00E0, 0xFF00C0, 0xFF00A0, 0xFF0080, 0xFF0060, 0xFF0040, 0xFF0020, // Magenta to Red
        0xFF0000, 0xFF0000, 0xEE0000, 0xDD0000, 0xCC0000, 0xBB0000, 0xAA0000, 0x990000, // Red shades
        0x880000, 0x770000, 0x660000, 0x550000, 0x440000, 0x330000, 0x220000, 0x110000, // Dark red shades

        // Row 3: Yellows, Greens and Blues at 80% brightness
        0xCCCC00, 0xBBCC00, 0xAACC00, 0x99CC00, 0x88CC00, 0x77CC00, 0x66CC00, 0x55CC00, // Yellow-green shades
        0x44CC00, 0x33CC00, 0x22CC00, 0x11CC00, 0x00CC00, 0x00CC11, 0x00CC22, 0x00CC33, // Green shades
        0x00CC44, 0x00CC55, 0x00CC66, 0x00CC77, 0x00CC88, 0x00CC99, 0x00CCAA, 0x00CCBB, // Green-cyan shades
        0x00CCCC, 0x00BBCC, 0x00AACC, 0x0099CC, 0x0088CC, 0x0077CC, 0x0066CC, 0x0055CC, // Cyan-blue shades

        // Row 4: Blues, Purples and Magentas at 80% brightness
        0x0044CC, 0x0033CC, 0x0022CC, 0x0011CC, 0x0000CC, 0x1100CC, 0x2200CC, 0x3300CC, // Blue shades
        0x4400CC, 0x5500CC, 0x6600CC, 0x7700CC, 0x8800CC, 0x9900CC, 0xAA00CC, 0xBB00CC, // Blue-magenta shades
        0xCC00CC, 0xCC00BB, 0xCC00AA, 0xCC0099, 0xCC0088, 0xCC0077, 0xCC0066, 0xCC0055, // Magenta-red shades
        0xCC0044, 0xCC0033, 0xCC0022, 0xCC0011, 0xCC0000, 0xCC1100, 0xCC2200, 0xCC3300, // Red-orange shades

        // Row 5: Pastel colors
        0xFFCCCC, 0xFFDDCC, 0xFFEECC, 0xFFFFCC, 0xEEFFCC, 0xDDFFCC, 0xCCFFCC, 0xCCFFDD, // Red to Green pastels
        0xCCFFEE, 0xCCFFFF, 0xCCEEFF, 0xCCDDFF, 0xCCCCFF, 0xDDCCFF, 0xEECCFF, 0xFFCCFF, // Green to Magenta pastels
        0xFFCCEE, 0xFFCCDD, 0xFFCCCC, 0xFFDDDD, 0xFFEEEE, 0xFFFFFF, 0xF5F5F5, 0xF0F0F0, // Magenta to White pastels
        0xE0E0E0, 0xD0D0D0, 0xC0C0C0, 0xB0B0B0, 0xA0A0A0, 0x909090, 0x808080, 0x707070, // Light gray shades

        // Row 6: Grayscale and dark colors
        0x606060, 0x505050, 0x404040, 0x303030, 0x202020, 0x101010, 0x000000, 0x001010, // Gray to Black to Dark Cyan
        0x002020, 0x003030, 0x004040, 0x005050, 0x006060, 0x007070, 0x008080, 0x007070, // Dark Cyan shades
        0x006060, 0x005050, 0x004040, 0x003030, 0x002020, 0x001010, 0x000000, 0x100010, // Dark Cyan to Black to Dark Magenta
        0x200020, 0x300030, 0x400040, 0x500050, 0x600060, 0x700070, 0x800080, 0x700070, // Dark Magenta shades

        // Row 7: Earth tones and special colors
        0x8B4513, 0x9B5523, 0xAB6533, 0xBB7543, 0xCB8553, 0xDB9563, 0xEBA573, 0xFBB583, // Brown shades
        0xDEB887, 0xD2B48C, 0xBC8F8F, 0xF4A460, 0xDAA520, 0xB8860B, 0xCD853F, 0xD2691E, // More browns and golds
        0xA0522D, 0x800000, 0x8B0000, 0xA52A2A, 0xB22222, 0xDC143C, 0xE9967A, 0xFA8072, // Dark reds and salmons
        0xFFA07A, 0xFFDAB9, 0xF5DEB3, 0xFFE4B5, 0xFFEBCD, 0xFFEFD5, 0xFFF8DC, 0xFFFACD, // Skin tones and creams

        // Row 8: Special colors and muted tones
        0x2E8B57, 0x3CB371, 0x90EE90, 0x98FB98, 0x00FA9A, 0x00FF7F, 0x7CFC00, 0x7FFF00, // Green special colors
        0x556B2F, 0x6B8E23, 0x808000, 0xBDB76B, 0xF0E68C, 0xEEE8AA, 0xFFD700, 0xFFC0CB, // Olive and gold tones
        0xFF69B4, 0xFF1493, 0xC71585, 0xDB7093, 0xFFA500, 0xFF8C00, 0xFF7F50, 0xFF6347, // Pink and orange tones
        0x40E0D0, 0x48D1CC, 0x00CED1, 0x5F9EA0, 0x4682B4, 0x6495ED, 0x7B68EE, 0x9370DB  // Turquoise and blue-purple tones
    };

    public ColorPickerScreen(ClaimTagScreen parentScreen, ClaimTag tag) {
        super(Text.literal("Select Color"));
        this.client = MinecraftClient.getInstance();
        this.parentScreen = parentScreen;
        this.tag = tag;
    }

    @Override
    protected void init() {
        super.init();

        // Calculate panel dimensions - wider and shorter
        int gridWidth = COLORS_PER_ROW * (COLOR_CELL_SIZE + COLOR_PADDING) + COLOR_PADDING;
        int gridHeight = ROWS * (COLOR_CELL_SIZE + COLOR_PADDING) + COLOR_PADDING;

        // Make sure the panel fits on screen
        panelWidth = Math.min(gridWidth + 20, width - 20); // Just a bit of padding
        panelHeight = Math.min(gridHeight + 30, height - 40); // Shorter height with just space for title

        // Center the panel
        leftX = (width - panelWidth) / 2;
        topY = (height - panelHeight) / 2;
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw dark background
        this.renderBackground(context);

        // Draw panel background with gradient
        context.fillGradient(leftX, topY, leftX + panelWidth, topY + panelHeight, 0xE0101010, 0xE0303030);

        // Draw border
        context.drawBorder(leftX, topY, panelWidth, panelHeight, 0xFFAAAAAA);

        // Draw title
        context.drawCenteredTextWithShadow(this.textRenderer, this.title, width / 2, topY + 10, 0xFFFFFF);

        // Draw current color preview - smaller and more compact
        int previewSize = 16;
        int previewX = leftX + 10;
        int previewY = topY + 7;

        // Draw preview border
        context.fill(previewX - 1, previewY - 1, previewX + previewSize + 1, previewY + previewSize + 1, 0xFFFFFFFF);
        // Draw preview color
        context.fill(previewX, previewY, previewX + previewSize, previewY + previewSize, tag.getColor() | 0xFF000000);

        // No hex code display to save space for more colors

        // Draw color grid - more compact
        int startY = topY + 30; // Reduced from 35 to 30

        for (int row = 0; row < ROWS; row++) {
            for (int col = 0; col < COLORS_PER_ROW; col++) {
                int index = row * COLORS_PER_ROW + col;
                if (index < COLORS.length) {
                    int color = COLORS[index];
                    int x = leftX + COLOR_PADDING + col * (COLOR_CELL_SIZE + COLOR_PADDING);
                    int y = startY + COLOR_PADDING + row * (COLOR_CELL_SIZE + COLOR_PADDING);

                    boolean isHovered = mouseX >= x && mouseX <= x + COLOR_CELL_SIZE &&
                                       mouseY >= y && mouseY <= y + COLOR_CELL_SIZE;

                    boolean isSelected = (color | 0xFF000000) == (tag.getColor() | 0xFF000000);

                    // Simplified border - just a 1px line for selected or hovered
                    if (isSelected || isHovered) {
                        int borderColor = isSelected ? 0xFFFFFFFF : 0xFFCCCCCC;
                        context.drawBorder(x, y, COLOR_CELL_SIZE, COLOR_CELL_SIZE, borderColor);
                    }

                    // Draw color cell
                    context.fill(x, y, x + COLOR_CELL_SIZE, y + COLOR_CELL_SIZE, color | 0xFF000000);

                    // Draw selection indicator (smaller for smaller cells)
                    if (isSelected) {
                        // Draw a tiny white dot in the center for selected color
                        context.fill(x + COLOR_CELL_SIZE/2, y + COLOR_CELL_SIZE/2,
                                   x + COLOR_CELL_SIZE/2 + 1, y + COLOR_CELL_SIZE/2 + 1,
                                   0xFFFFFFFF);
                    }
                }
            }
        }

        // No category labels or instructions to save space for more colors

        // Draw tooltip for hovered color
        if (mouseX >= leftX && mouseX <= leftX + panelWidth &&
            mouseY >= startY && mouseY <= startY + ROWS * (COLOR_CELL_SIZE + COLOR_PADDING)) {

            int col = (int)((mouseX - leftX - COLOR_PADDING) / (COLOR_CELL_SIZE + COLOR_PADDING));
            int row = (int)((mouseY - startY - COLOR_PADDING) / (COLOR_CELL_SIZE + COLOR_PADDING));

            if (col >= 0 && col < COLORS_PER_ROW && row >= 0 && row < ROWS) {
                int index = row * COLORS_PER_ROW + col;
                if (index < COLORS.length) {
                    int color = COLORS[index];
                    String hexCode = String.format("#%06X", color & 0xFFFFFF);
                    context.drawTooltip(this.textRenderer, Text.literal(hexCode), mouseX, mouseY);
                }
            }
        }

        super.render(context, mouseX, mouseY, delta);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        int startY = topY + 30; // Match the startY from render method

        // Check if click is inside the color grid
        if (mouseX >= leftX && mouseX <= leftX + COLORS_PER_ROW * (COLOR_CELL_SIZE + COLOR_PADDING) &&
            mouseY >= startY && mouseY <= startY + ROWS * (COLOR_CELL_SIZE + COLOR_PADDING)) {

            // Calculate which color cell was clicked
            int col = (int)((mouseX - leftX - COLOR_PADDING) / (COLOR_CELL_SIZE + COLOR_PADDING));
            int row = (int)((mouseY - startY - COLOR_PADDING) / (COLOR_CELL_SIZE + COLOR_PADDING));

            if (col >= 0 && col < COLORS_PER_ROW && row >= 0 && row < ROWS) {
                int index = row * COLORS_PER_ROW + col;
                if (index < COLORS.length) {
                    // Set the tag color (ensure full alpha channel)
                    int oldColor = tag.getColor();
                    int newColor = COLORS[index] | 0xFF000000;
                    tag.setColor(newColor);

                    // Update the claim tool with the color change
                    if (parentScreen != null) {
                        parentScreen.updateClaimToolTags();

                        // Show a notification about the color change
                        com.pokecobble.town.client.NotificationRenderer.addNotification(
                            "Changed tag color for '" + tag.getName() + "'");
                    }

                    // Return to parent screen
                    this.client.setScreen(parentScreen);
                    return true;
                }
            }
        }

        // If clicked outside the color grid, close the screen
        if (mouseX < leftX || mouseX > leftX + panelWidth ||
            mouseY < topY || mouseY > topY + panelHeight) {
            this.client.setScreen(parentScreen);
            return true;
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean shouldPause() {
        return false;
    }
}
