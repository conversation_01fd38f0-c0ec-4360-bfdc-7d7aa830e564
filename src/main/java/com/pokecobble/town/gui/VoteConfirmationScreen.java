package com.pokecobble.town.gui;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.election.ElectionManager;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import java.util.UUID;

/**
 * Confirmation screen for voting in a mayoral election.
 */
public class VoteConfirmationScreen extends Screen {
    private final Screen parent;
    private final Town town;
    private final TownPlayer candidate;
    private ButtonWidget confirmButton;
    private ButtonWidget cancelButton;

    // Colors
    private static final int BACKGROUND_COLOR = 0xFF101010; // Solid dark background
    private static final int PANEL_COLOR = 0xFF202030; // Solid panel background
    private static final int BORDER_COLOR = 0xFF5555FF; // Blue border
    private static final int TEXT_COLOR = 0xFFFFFFFF; // White text
    private static final int CONFIRM_BUTTON_COLOR = 0xFF4CAF50; // Green confirm button
    private static final int CANCEL_BUTTON_COLOR = 0xFFE53935; // Red cancel button

    public VoteConfirmationScreen(Screen parent, Town town, TownPlayer candidate) {
        super(Text.literal("Vote Confirmation"));
        this.parent = parent;
        this.town = town;
        this.candidate = candidate;
    }

    @Override
    protected void init() {
        super.init();

        int buttonWidth = 100;
        int buttonHeight = 20;
        int buttonSpacing = 10;
        int panelWidth = 400;
        int panelHeight = 150;
        int panelX = (this.width - panelWidth) / 2;
        int panelY = (this.height - panelHeight) / 2;
        int buttonsY = panelY + panelHeight - buttonHeight - 15;

        // Add confirm button
        confirmButton = ButtonWidget.builder(Text.literal("Confirm"), button -> {
            try {
                // Cast vote
                if (this.client != null && this.client.player != null) {
                    // Log the vote for debugging
                    System.out.println("Casting vote for: " + candidate.getName());

                    // Cast the vote
                    ElectionManager.getInstance().castVote(town, this.client.player.getUuid(), candidate.getUuid());

                    // Create a status message
                    String statusMessage = "You voted for " + candidate.getName();

                    // Return to parent screen
                    if (parent instanceof MyTownScreen) {
                        MyTownScreen myTownScreen = (MyTownScreen) parent;
                        myTownScreen.setStatus(statusMessage, Formatting.GREEN);
                        myTownScreen.refreshPlayerList(); // Refresh the player list
                        myTownScreen.init(); // Refresh the screen

                        // Set the screen with a slight delay to ensure proper rendering
                        this.client.execute(() -> {
                            this.client.setScreen(parent);
                        });
                    } else {
                        // If parent is not MyTownScreen, just return to it directly
                        this.client.setScreen(parent);
                    }
                }
            } catch (Exception e) {
                // Log any errors
                System.err.println("Error casting vote: " + e.getMessage());
                e.printStackTrace();

                // Show an error message
                if (parent instanceof MyTownScreen) {
                    ((MyTownScreen) parent).setStatus("Error casting vote. Try again.", Formatting.RED);
                }

                // Return to parent screen
                this.client.setScreen(parent);
            }
        })
        .dimensions(panelX + panelWidth / 2 - buttonWidth - buttonSpacing / 2, buttonsY, buttonWidth, buttonHeight)
        .build();
        this.addDrawableChild(confirmButton);

        // Add cancel button
        cancelButton = ButtonWidget.builder(Text.literal("Cancel"), button -> {
            // Just close the screen
            this.client.setScreen(parent);
        })
        .dimensions(panelX + panelWidth / 2 + buttonSpacing / 2, buttonsY, buttonWidth, buttonHeight)
        .build();
        this.addDrawableChild(cancelButton);
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw a solid dark background over the entire screen
        context.fill(0, 0, this.width, this.height, BACKGROUND_COLOR);

        // Draw a solid panel
        int panelWidth = 400;
        int panelHeight = 150;
        int panelX = (this.width - panelWidth) / 2;
        int panelY = (this.height - panelHeight) / 2;

        // Draw panel background
        context.fill(panelX, panelY, panelX + panelWidth, panelY + panelHeight, PANEL_COLOR);

        // Draw panel border
        context.drawBorder(panelX, panelY, panelWidth, panelHeight, BORDER_COLOR);

        // Draw title
        context.drawCenteredTextWithShadow(this.textRenderer, Text.literal("Vote Confirmation").formatted(Formatting.BOLD),
            panelX + panelWidth / 2, panelY + 15, TEXT_COLOR);

        // Draw confirmation message
        String message = "Are you sure you want to vote for " + candidate.getName() + "?";
        context.drawCenteredTextWithShadow(this.textRenderer, message,
            panelX + panelWidth / 2, panelY + 40, TEXT_COLOR);

        // Draw warning message
        String warning = "You only have 1 vote and it cannot be changed!";
        context.drawCenteredTextWithShadow(this.textRenderer, Text.literal(warning).formatted(Formatting.RED),
            panelX + panelWidth / 2, panelY + 60, TEXT_COLOR);

        // Draw buttons
        super.render(context, mouseX, mouseY, delta);
    }

    @Override
    public boolean shouldPause() {
        return false;
    }
}
