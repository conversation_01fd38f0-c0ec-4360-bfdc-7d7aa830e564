package com.pokecobble.town.gui;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.claim.ClaimTag;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;
import org.lwjgl.glfw.GLFW;

/**
 * Screen for detailed tag permission settings.
 */
public class TagSettingsScreen extends Screen {
    private final MinecraftClient client;
    private final ClaimTagScreen parentScreen;
    private final ClaimTag tag;

    // Panel dimensions
    private int panelWidth;
    private int panelHeight;
    private int leftX;
    private int topY;

    // UI elements
    private ButtonWidget saveButton;
    private ButtonWidget cancelButton;

    // Currently selected rank for editing
    private TownPlayerRank selectedRank = TownPlayerRank.MEMBER;

    // Colors
    private static final int BACKGROUND_COLOR = 0xE0101010;
    private static final int HEADER_COLOR = 0xA0303050;
    private static final int SELECTED_COLOR = 0x80404080;

    public TagSettingsScreen(ClaimTagScreen parentScreen, ClaimTag tag) {
        super(Text.literal("Tag Settings: " + tag.getName()));
        this.client = MinecraftClient.getInstance();
        this.parentScreen = parentScreen;
        this.tag = tag;
    }

    @Override
    protected void init() {
        super.init();

        // Calculate panel dimensions based on screen size
        panelWidth = Math.min(width - 20, 800);
        panelHeight = height - 20;
        leftX = (width - panelWidth) / 2;
        topY = (height - panelHeight) / 2;

        // Add save and cancel buttons with custom styling
        // We'll draw them manually in the render method instead of using ButtonWidget
        // This allows us to match the style of ClaimTagScreen
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw dark background
        this.renderBackground(context);

        // Draw panel background
        context.fill(leftX, topY, leftX + panelWidth, topY + panelHeight, BACKGROUND_COLOR);

        // Draw header
        context.fillGradient(leftX, topY, leftX + panelWidth, topY + 20, HEADER_COLOR, HEADER_COLOR);
        context.drawCenteredTextWithShadow(this.textRenderer, this.title, width / 2, topY + 6, 0xFFFFFF);

        // Calculate content area dimensions
        int contentX = leftX + 10;
        int contentY = topY + 25;
        int contentWidth = panelWidth - 20;
        int contentHeight = panelHeight - 35;

        // Calculate sidebar dimensions - narrower sidebar
        int sidebarWidth = 100; // Reduced from 150 to 100
        int sidebarX = contentX;
        int sidebarY = contentY;
        int sidebarHeight = contentHeight;

        // Draw sidebar background
        context.fill(sidebarX, sidebarY, sidebarX + sidebarWidth, sidebarY + sidebarHeight, 0x80202020);

        // Draw sidebar title
        context.drawTextWithShadow(this.textRenderer, Text.literal("Ranks").formatted(Formatting.BOLD),
                sidebarX + 10, sidebarY + 10, 0xFFFFFF);

        // Draw rank list - more compact
        int rankY = sidebarY + 30;
        int rankHeight = 22; // Reduced from 30 to 22

        // Draw each rank
        for (TownPlayerRank rank : TownPlayerRank.values()) {
            boolean isSelected = rank == selectedRank;

            // Draw rank background
            if (isSelected) {
                context.fill(sidebarX, rankY, sidebarX + sidebarWidth, rankY + rankHeight, SELECTED_COLOR);
            } else if (mouseX >= sidebarX && mouseX <= sidebarX + sidebarWidth &&
                      mouseY >= rankY && mouseY <= rankY + rankHeight) {
                context.fill(sidebarX, rankY, sidebarX + sidebarWidth, rankY + rankHeight, 0x40FFFFFF);
            }

            // Draw rank icon and name - more compact
            context.drawTextWithShadow(this.textRenderer, rank.getIcon() + " " + getRankText(rank),
                    sidebarX + 5, rankY + (rankHeight - 8) / 2, getRankColor(rank));

            rankY += rankHeight;
        }

        // Draw non-member option
        boolean nonMemberSelected = selectedRank == null;
        if (nonMemberSelected) {
            context.fill(sidebarX, rankY, sidebarX + sidebarWidth, rankY + rankHeight, SELECTED_COLOR);
        } else if (mouseX >= sidebarX && mouseX <= sidebarX + sidebarWidth &&
                  mouseY >= rankY && mouseY <= rankY + rankHeight) {
            context.fill(sidebarX, rankY, sidebarX + sidebarWidth, rankY + rankHeight, 0x40FFFFFF);
        }

        context.drawTextWithShadow(this.textRenderer, "🚶 Non-Member",
                sidebarX + 5, rankY + (rankHeight - 8) / 2, 0xAAAAAA);

        // Draw content area
        int contentAreaX = sidebarX + sidebarWidth + 10;
        int contentAreaWidth = contentWidth - sidebarWidth - 10;

        // Draw permissions section - more compact header
        context.drawTextWithShadow(this.textRenderer,
                Text.literal("Permissions for " + (selectedRank == null ? "Non-Members" : getRankText(selectedRank))).formatted(Formatting.BOLD),
                contentAreaX, contentY + 5, 0xFFFFFF);

        // Draw permission toggles - more compact layout
        int permissionY = contentY + 25; // Reduced from 40 to 25
        int permissionHeight = 20; // Reduced from 25 to 20
        int toggleWidth = 50; // Reduced from 60 to 50
        int toggleHeight = 16; // Reduced from 20 to 16

        // Permission label width
        int labelWidth = 70;

        // Define all permissions
        String[] permissionLabels = {
            "Build:", "Interact:", "Containers:", "Redstone:",
            "Doors:", "Crops:", "Animals:", "Villagers:"
        };

        // Get permission values for the selected rank
        boolean[] permissions = getPermissionsForRank(selectedRank, tag);

        // Calculate layout - use two columns to save space
        int colWidth = contentAreaWidth / 2 - 10;
        int col1X = contentAreaX;
        int col2X = contentAreaX + colWidth + 20;

        // Draw permissions in two columns
        for (int i = 0; i < permissionLabels.length; i++) {
            int currentX = (i < 4) ? col1X : col2X;
            int currentY = (i < 4) ? permissionY + (i * permissionHeight) : permissionY + ((i - 4) * permissionHeight);

            // Draw permission label
            context.drawTextWithShadow(this.textRenderer, permissionLabels[i], currentX, currentY + 4, 0xFFFFFF);

            // Draw toggle button
            int toggleX = currentX + labelWidth;
            boolean toggleHovered = mouseX >= toggleX && mouseX <= toggleX + toggleWidth &&
                                   mouseY >= currentY && mouseY <= currentY + toggleHeight;

            drawModernButton(context, toggleX, currentY, toggleWidth, toggleHeight,
                            permissions[i] ? 0xFF55FF55 : 0xFFFF5555, toggleHovered, true);

            context.drawCenteredTextWithShadow(this.textRenderer, permissions[i] ? "Yes" : "No",
                    toggleX + toggleWidth / 2, currentY + 4, 0xFFFFFF);
        }

        // Draw custom styled buttons
        int buttonWidth = 100;
        int buttonHeight = 20;
        int buttonSpacing = 10;
        int buttonsY = topY + panelHeight - buttonHeight - 10;

        // Save button
        int saveButtonX = leftX + panelWidth - buttonWidth * 2 - buttonSpacing - 10;
        boolean saveButtonHovered = mouseX >= saveButtonX && mouseX <= saveButtonX + buttonWidth &&
                                   mouseY >= buttonsY && mouseY <= buttonsY + buttonHeight;

        drawModernButton(context, saveButtonX, buttonsY, buttonWidth, buttonHeight, 0xFF55FF55, // Green
                        saveButtonHovered, true);
        context.drawCenteredTextWithShadow(this.textRenderer, "Save Changes",
                saveButtonX + buttonWidth / 2, buttonsY + 6, 0xFFFFFF);

        // Cancel button
        int cancelButtonX = leftX + panelWidth - buttonWidth - 10;
        boolean cancelButtonHovered = mouseX >= cancelButtonX && mouseX <= cancelButtonX + buttonWidth &&
                                     mouseY >= buttonsY && mouseY <= buttonsY + buttonHeight;

        drawModernButton(context, cancelButtonX, buttonsY, buttonWidth, buttonHeight, 0xFFFF5555, // Red
                        cancelButtonHovered, true);
        context.drawCenteredTextWithShadow(this.textRenderer, "Cancel",
                cancelButtonX + buttonWidth / 2, buttonsY + 6, 0xFFFFFF);

        // Draw other UI elements
        super.render(context, mouseX, mouseY, delta);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Calculate sidebar dimensions - match the render method
        int sidebarX = leftX + 10;
        int sidebarY = topY + 25;
        int sidebarWidth = 100; // Reduced from 150 to 100

        // Check for clicks on rank list - match the render method
        int rankY = sidebarY + 30;
        int rankHeight = 22; // Reduced from 30 to 22

        // Check for clicks on each rank
        for (TownPlayerRank rank : TownPlayerRank.values()) {
            if (mouseX >= sidebarX && mouseX <= sidebarX + sidebarWidth &&
                mouseY >= rankY && mouseY <= rankY + rankHeight) {
                // Select this rank
                selectedRank = rank;
                return true;
            }

            rankY += rankHeight;
        }

        // Check for click on non-member option
        if (mouseX >= sidebarX && mouseX <= sidebarX + sidebarWidth &&
            mouseY >= rankY && mouseY <= rankY + rankHeight) {
            // Select non-member
            selectedRank = null;
            return true;
        }

        // Calculate content area dimensions
        int contentX = leftX + 10;
        int contentY = topY + 25;
        int contentWidth = panelWidth - 20;

        // Calculate permission toggle positions - match the render method
        int contentAreaX = sidebarX + sidebarWidth + 10;
        int contentAreaWidth = contentWidth - sidebarWidth - 10; // Define contentAreaWidth
        int permissionY = contentY + 25; // Reduced from 40 to 25
        int permissionHeight = 20; // Reduced from 25 to 20
        int toggleWidth = 50; // Reduced from 60 to 50
        int toggleHeight = 16; // Reduced from 20 to 16
        int labelWidth = 70;

        // Define all permissions
        String[] permissionLabels = {
            "Build:", "Interact:", "Containers:", "Redstone:",
            "Doors:", "Crops:", "Animals:", "Villagers:"
        };

        // Calculate layout - use two columns to save space
        int colWidth = contentAreaWidth / 2 - 10;
        int col1X = contentAreaX;
        int col2X = contentAreaX + colWidth + 20;

        // Check for clicks on permission toggles
        for (int i = 0; i < permissionLabels.length; i++) {
            int currentX = (i < 4) ? col1X : col2X;
            int currentY = (i < 4) ? permissionY + (i * permissionHeight) : permissionY + ((i - 4) * permissionHeight);

            // Calculate toggle button position
            int toggleX = currentX + labelWidth;
            if (mouseX >= toggleX && mouseX <= toggleX + toggleWidth &&
                mouseY >= currentY && mouseY <= currentY + toggleHeight) {

                // Calculate permissions for the current rank
                boolean[] permissions = getPermissionsForRank(selectedRank, tag);

                // Get current permission state
                boolean hasPermission = permissions[i];

                // Toggle the permission (simple Yes/No toggle)
                // Use the new permission system to directly set the permission for this rank
                // This ensures that each rank's permissions are completely independent

                // Special case for OWNER rank - always has all permissions
                if (selectedRank == TownPlayerRank.OWNER && i < 8) {
                    // Can't remove permissions from OWNER
                    tag.setPermissionForRank(selectedRank, i, true);
                } else {
                    // For all other ranks, toggle the permission
                    tag.setPermissionForRank(selectedRank, i, !hasPermission);
                }
                return true;
            }
        }

        // Check for clicks on custom buttons
        int buttonWidth = 100;
        int buttonHeight = 20;
        int buttonSpacing = 10;
        int buttonsY = topY + panelHeight - buttonHeight - 10;

        // Save button
        int saveButtonX = leftX + panelWidth - buttonWidth * 2 - buttonSpacing - 10;
        if (mouseX >= saveButtonX && mouseX <= saveButtonX + buttonWidth &&
            mouseY >= buttonsY && mouseY <= buttonsY + buttonHeight) {
            // Save changes and return to parent screen
            this.client.setScreen(parentScreen);
            return true;
        }

        // Cancel button
        int cancelButtonX = leftX + panelWidth - buttonWidth - 10;
        if (mouseX >= cancelButtonX && mouseX <= cancelButtonX + buttonWidth &&
            mouseY >= buttonsY && mouseY <= buttonsY + buttonHeight) {
            // Discard changes and return to parent screen
            this.client.setScreen(parentScreen);
            return true;
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean shouldPause() {
        return false;
    }

    /**
     * Handles key presses in the tag settings screen.
     * This is used to properly handle the ESC key to return to the parent screen without closing the claim tool.
     */
    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        // Handle ESC key to return to the parent screen without closing the claim tool
        if (keyCode == GLFW.GLFW_KEY_ESCAPE) {
            // Return to the parent screen
            this.client.setScreen(parentScreen);
            return true;
        }

        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    private String getRankText(TownPlayerRank rank) {
        switch (rank) {
            case OWNER:
                return "Mayor";
            case ADMIN:
                return "Deputy";
            case MODERATOR:
                return "Council";
            case MEMBER:
                return "Resident";
            case VISITOR:
                return "Citizen";
            default:
                return "Unknown";
        }
    }

    private int getRankColor(TownPlayerRank rank) {
        switch (rank) {
            case OWNER:
                return 0xFFAA00; // Gold
            case ADMIN:
                return 0xFF5555; // Red
            case MODERATOR:
                return 0x55FFFF; // Aqua
            case MEMBER:
                return 0x55FF55; // Green
            case VISITOR:
                return 0xAAAAAA; // Gray
            default:
                return 0xFFFFFF; // White
        }
    }

    /**
     * Gets the next rank in the cycle.
     */
    private TownPlayerRank getNextRank(TownPlayerRank currentRank) {
        switch (currentRank) {
            case VISITOR:
                return TownPlayerRank.MEMBER;
            case MEMBER:
                return TownPlayerRank.MODERATOR;
            case MODERATOR:
                return TownPlayerRank.ADMIN;
            case ADMIN:
                return TownPlayerRank.OWNER;
            case OWNER:
                return TownPlayerRank.VISITOR;
            default:
                return TownPlayerRank.MEMBER;
        }
    }

    /**
     * Gets the next higher rank in the hierarchy.
     * Returns null if there is no higher rank.
     */
    private TownPlayerRank getNextHigherRank(TownPlayerRank currentRank) {
        switch (currentRank) {
            case VISITOR:
                return TownPlayerRank.MEMBER;
            case MEMBER:
                return TownPlayerRank.MODERATOR;
            case MODERATOR:
                return TownPlayerRank.ADMIN;
            case ADMIN:
                return TownPlayerRank.OWNER;
            case OWNER:
                return null; // No higher rank
            default:
                return null;
        }
    }

    /**
     * Gets the permissions for the given rank and tag.
     * Each rank's permissions are independent of other ranks.
     *
     * @param rank The rank to check, or null for non-member
     * @param tag The tag to check
     * @return Array of booleans for all permissions
     */
    private boolean[] getPermissionsForRank(TownPlayerRank rank, ClaimTag tag) {
        // Use the new permission system that stores permissions for each rank independently
        return tag.getPermissionsForRank(rank);
    }

    /**
     * Draws a modern button with the given parameters.
     */
    private void drawModernButton(DrawContext context, int x, int y, int width, int height, int color, boolean hovered, boolean enabled) {
        // Draw button background
        int bgColor = enabled ? color : 0xFF555555;
        if (hovered && enabled) {
            bgColor = (bgColor & 0x00FFFFFF) | 0xFF000000; // Full opacity when hovered
        } else {
            bgColor = (bgColor & 0x00FFFFFF) | 0xDD000000; // Slightly transparent when not hovered
        }

        // Draw button
        context.fill(x, y, x + width, y + height, bgColor);

        // Draw button border
        int borderColor = 0x60FFFFFF;
        context.drawBorder(x, y, width, height, borderColor);
    }
}
