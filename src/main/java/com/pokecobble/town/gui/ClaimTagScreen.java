package com.pokecobble.town.gui;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.claim.ClaimTag;
import com.pokecobble.town.claim.ClaimTool;
import com.pokecobble.town.client.NotificationRenderer;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.widget.ButtonWidget;
import net.minecraft.client.gui.widget.TextFieldWidget;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;
import org.lwjgl.glfw.GLFW;

import java.util.ArrayList;
import java.util.List;

/**
 * Screen for managing claim tags.
 */
public class ClaimTagScreen extends Screen {
    private final MinecraftClient client;
    private final Town town;
    private final Screen parentScreen; // Store the parent screen

    // Panel dimensions
    private int panelWidth;
    private int panelHeight;
    private int leftX;
    private int topY;

    // UI elements
    private ButtonWidget createTagButton;
    private ButtonWidget deleteTagButton;
    private TextFieldWidget tagNameField;

    // Tag list
    private List<ClaimTag> tags = new ArrayList<>();
    private ClaimTag selectedTag;

    // Scroll position
    private int tagsScrollOffset = 0;

    // Colors
    private static final int BACKGROUND_COLOR = 0xE0101010;
    private static final int HEADER_COLOR = 0xA0303050;
    private static final int SELECTED_COLOR = 0x80404080;

    // Sample tag colors
    private static final int[] TAG_COLORS = {
        0xFF4B69FF, // Blue
        0xFF00FF00, // Green
        0xFFFF0000, // Red
        0xFFFFFF00, // Yellow
        0xFFFF00FF, // Magenta
        0xFF00FFFF, // Cyan
        0xFFFF8000, // Orange
        0xFF8000FF  // Purple
    };

    // Current rank being displayed in the permission info
    private TownPlayerRank currentDisplayRank = TownPlayerRank.MEMBER; // Initialize with MEMBER as default
    private boolean showingNonMember = false;

    /**
     * Constructor for when opened from the claim tool.
     *
     * @param town The town to manage tags for
     */
    public ClaimTagScreen(Town town) {
        this(null, town);
    }

    /**
     * Constructor with parent screen.
     *
     * @param parentScreen The parent screen to return to when closed
     * @param town The town to manage tags for
     */
    public ClaimTagScreen(Screen parentScreen, Town town) {
        super(Text.literal("Claim Tags"));
        this.client = MinecraftClient.getInstance();
        this.town = town;
        this.parentScreen = parentScreen;

        // Load tags from the town
        loadTownTags();
    }

    /**
     * Loads tags from the town.
     * If the town has no tags, creates default tags.
     */
    private void loadTownTags() {
        // Clear existing tags
        tags.clear();

        // Get tags from the town
        if (town != null) {
            List<ClaimTag> townTags = town.getClaimTags();

            if (townTags != null && !townTags.isEmpty()) {
                // Town has tags, use them
                tags.addAll(townTags);
            } else {
                // Town has no tags, create default ones
                createDefaultTags();

                // Save the default tags to the town
                town.updateClaimTags(tags);
            }
        } else {
            // No town, create default tags
            createDefaultTags();
        }

        // Set the first tag as selected
        if (!tags.isEmpty()) {
            selectedTag = tags.get(0);
        }
    }

    /**
     * Creates default tags for a new town.
     */
    private void createDefaultTags() {
        // Create default tags with simple numbered names
        for (int i = 0; i < 4; i++) {
            ClaimTag tag = new ClaimTag("Tag" + (i + 1), TAG_COLORS[i % TAG_COLORS.length]);

            // Set default permissions - no access for non-members
            tag.setAllowBuild(false);
            tag.setAllowInteract(false);
            tag.setAllowContainers(false);
            tag.setAllowRedstone(false);
            tag.setAllowDoors(false);
            tag.setAllowCrops(false);
            tag.setAllowAnimals(false);
            tag.setAllowVillagers(false);

            // Set default minimum ranks - only OWNER (Mayor) can do anything
            tag.setMinRankBuild(TownPlayerRank.OWNER);
            tag.setMinRankInteract(TownPlayerRank.OWNER);
            tag.setMinRankContainers(TownPlayerRank.OWNER);
            tag.setMinRankRedstone(TownPlayerRank.OWNER);
            tag.setMinRankDoors(TownPlayerRank.OWNER);
            tag.setMinRankCrops(TownPlayerRank.OWNER);
            tag.setMinRankAnimals(TownPlayerRank.OWNER);
            tag.setMinRankVillagers(TownPlayerRank.OWNER);

            // Add tag to the list
            tags.add(tag);
        }
    }

    @Override
    protected void init() {
        super.init();

        // Calculate panel dimensions based on screen size - wider horizontally
        panelWidth = Math.min(width - 20, 1200); // Wider panel
        panelHeight = height - 20;
        leftX = (width - panelWidth) / 2;
        topY = (height - panelHeight) / 2;

        // Add tag name field - smaller and positioned at the very top
        tagNameField = new TextFieldWidget(this.textRenderer, leftX + 170, topY + 30, 150, 20, Text.literal(""));
        tagNameField.setMaxLength(32);
        if (selectedTag != null) {
            tagNameField.setText(selectedTag.getName());
        }
        this.addDrawableChild(tagNameField);

        // Add delete tag button - positioned next to the name field
        deleteTagButton = ButtonWidget.builder(
                Text.literal("Delete"),
                button -> deleteSelectedTag())
                .dimensions(leftX + 330, topY + 30, 50, 20)
                .build();
        this.addDrawableChild(deleteTagButton);

        // Update button states
        updateButtonStates();
    }

    private void createNewTag() {
        // Create a new tag with a numbered name and random color
        int colorIndex = tags.size() % TAG_COLORS.length;
        String defaultName = "Tag" + (tags.size() + 1);
        ClaimTag newTag = new ClaimTag(defaultName, TAG_COLORS[colorIndex]);

        // Set default permissions - no access for non-members
        newTag.setAllowBuild(false);
        newTag.setAllowInteract(false);
        newTag.setAllowContainers(false);
        newTag.setAllowRedstone(false);
        newTag.setAllowDoors(false);
        newTag.setAllowCrops(false);
        newTag.setAllowAnimals(false);
        newTag.setAllowVillagers(false);

        // Set default minimum ranks - only OWNER (Mayor) can do anything
        newTag.setMinRankBuild(TownPlayerRank.OWNER);
        newTag.setMinRankInteract(TownPlayerRank.OWNER);
        newTag.setMinRankContainers(TownPlayerRank.OWNER);
        newTag.setMinRankRedstone(TownPlayerRank.OWNER);
        newTag.setMinRankDoors(TownPlayerRank.OWNER);
        newTag.setMinRankCrops(TownPlayerRank.OWNER);
        newTag.setMinRankAnimals(TownPlayerRank.OWNER);
        newTag.setMinRankVillagers(TownPlayerRank.OWNER);

        tags.add(newTag);
        selectedTag = newTag;
        tagNameField.setText(defaultName);
        updateButtonStates();

        // Save the tag to the town
        if (town != null) {
            town.addClaimTag(newTag);
            // Update the claim tool with the new tag
            updateClaimToolTags();
            // Send a notification
            NotificationRenderer.addNotification("Created new tag: " + defaultName);
        }
    }

    private void deleteSelectedTag() {
        if (selectedTag != null) {
            // Remove the tag from the list
            tags.remove(selectedTag);

            // Remove the tag from the town
            if (town != null) {
                town.removeClaimTag(selectedTag);
                // Update the claim tool with the removed tag
                updateClaimToolTags();
                // Send a notification
                NotificationRenderer.addNotification("Deleted tag: " + selectedTag.getName());
            }

            // Select a new tag if available
            if (!tags.isEmpty()) {
                selectedTag = tags.get(0);
            } else {
                selectedTag = null;
            }
            updateButtonStates();
        }
    }

    private void updateButtonStates() {
        if (deleteTagButton != null) {
            deleteTagButton.active = selectedTag != null;
        }

        if (tagNameField != null && selectedTag != null) {
            tagNameField.setText(selectedTag.getName());
        }
    }

    @Override
    public void render(DrawContext context, int mouseX, int mouseY, float delta) {
        // Draw dark background
        this.renderBackground(context);

        // Draw panel background
        context.fill(leftX, topY, leftX + panelWidth, topY + panelHeight, BACKGROUND_COLOR);

        // Draw header
        context.fillGradient(leftX, topY, leftX + panelWidth, topY + 20, HEADER_COLOR, HEADER_COLOR);
        context.drawCenteredTextWithShadow(this.textRenderer, this.title, width / 2, topY + 6, 0xFFFFFF);

        // Calculate content area dimensions
        int contentX = leftX + 10;
        int contentY = topY + 25;
        int contentWidth = panelWidth - 20;
        int contentHeight = panelHeight - 35;

        // Calculate sidebar dimensions - narrower sidebar
        int sidebarWidth = 100; // Reduced from 120
        int sidebarX = contentX;
        int sidebarY = contentY;
        int sidebarHeight = contentHeight;

        // Draw sidebar background
        context.fill(sidebarX, sidebarY, sidebarX + sidebarWidth, sidebarY + sidebarHeight, 0x80202020);

        // Draw sidebar title
        context.drawTextWithShadow(this.textRenderer, Text.literal("Tags").formatted(Formatting.BOLD),
                sidebarX + 10, sidebarY + 10, 0xFFFFFF);

        // Draw tag list
        int tagY = sidebarY + 30;
        int tagHeight = 25;
        int visibleTags = 0;
        int maxVisibleTags = (sidebarHeight - 40) / tagHeight;

        // Draw "Create New Tag" as the first item
        boolean createNewHovered = mouseX >= sidebarX && mouseX <= sidebarX + sidebarWidth &&
                                  mouseY >= tagY && mouseY <= tagY + tagHeight;

        // Draw background for "Create New Tag"
        if (createNewHovered) {
            context.fill(sidebarX, tagY, sidebarX + sidebarWidth, tagY + tagHeight, 0x40FFFFFF);
        } else {
            context.fill(sidebarX, tagY, sidebarX + sidebarWidth, tagY + tagHeight, 0x20FFFFFF);
        }

        // Draw plus icon
        context.fill(sidebarX + 8, tagY + 12, sidebarX + 12, tagY + 13, 0xFF55FF55); // Horizontal line
        context.fill(sidebarX + 10, tagY + 10, sidebarX + 11, tagY + 15, 0xFF55FF55); // Vertical line

        // Draw "Create New Tag" text
        context.drawTextWithShadow(this.textRenderer, "Create New Tag",
                sidebarX + 20, tagY + (tagHeight - 8) / 2, 0x55FF55);

        tagY += tagHeight;
        visibleTags++;

        // Draw actual tags
        for (int i = tagsScrollOffset; i < tags.size() && visibleTags < maxVisibleTags; i++) {
            ClaimTag tag = tags.get(i);
            boolean isSelected = tag == selectedTag;

            // Draw tag background
            if (isSelected) {
                context.fill(sidebarX, tagY, sidebarX + sidebarWidth, tagY + tagHeight, SELECTED_COLOR);
            } else if (mouseX >= sidebarX && mouseX <= sidebarX + sidebarWidth &&
                       mouseY >= tagY && mouseY <= tagY + tagHeight) {
                context.fill(sidebarX, tagY, sidebarX + sidebarWidth, tagY + tagHeight, 0x40FFFFFF);
            }

            // Draw color indicator
            context.fill(sidebarX + 5, tagY + 5, sidebarX + 15, tagY + tagHeight - 5, tag.getColor() | 0xFF000000);
            // Draw border around color indicator
            context.drawBorder(sidebarX + 5, tagY + 5, 10, tagHeight - 10, 0x80FFFFFF);

            // Draw tag name
            context.drawTextWithShadow(this.textRenderer, tag.getName(),
                    sidebarX + 20, tagY + (tagHeight - 8) / 2, 0xFFFFFF);

            tagY += tagHeight;
            visibleTags++;
        }

        // Draw content area
        int contentAreaX = sidebarX + sidebarWidth + 10;
        int contentAreaWidth = contentWidth - sidebarWidth - 20;

        // Draw tag settings if a tag is selected
        if (selectedTag != null) {
            // Draw tag name section - moved to the top of the screen in init()

            // Draw permission info section - moved down below the tag name field
            int infoY = contentY + 60; // Moved down to avoid overlap with tag name field
            context.drawTextWithShadow(this.textRenderer, Text.literal("Permission Info").formatted(Formatting.BOLD),
                    contentAreaX, infoY, 0xFFFFFF);

            // Draw current rank selection button
            int rankButtonWidth = 120;
            int rankButtonHeight = 20;
            int rankButtonX = contentAreaX + 200; // Fixed position
            int rankButtonY = infoY - 2; // Align with the Permission Info header

            // Draw rank selection button
            boolean rankButtonHovered = mouseX >= rankButtonX && mouseX <= rankButtonX + rankButtonWidth &&
                                       mouseY >= rankButtonY && mouseY <= rankButtonY + rankButtonHeight;
            drawModernButton(context, rankButtonX, rankButtonY, rankButtonWidth, rankButtonHeight, 0xFF4B69FF, // Blue
                rankButtonHovered, true);

            // Get the current selected rank for display
            TownPlayerRank displayRank = getCurrentDisplayRank();
            String rankText = displayRank == null ? "Non-Member" : getRankText(displayRank);
            int rankColor = displayRank == null ? 0xAAAAAA : getRankColor(displayRank);

            context.drawCenteredTextWithShadow(this.textRenderer, rankText,
                rankButtonX + rankButtonWidth / 2, rankButtonY + 6, rankColor);

            // Draw permission table - more compact
            int tableX = contentAreaX;
            int tableY = infoY + 20; // Closer to header
            int rowHeight = 16; // Smaller rows
            int col1Width = 80; // Narrower columns
            int col2Width = 60;

            // Draw table headers
            context.drawTextWithShadow(this.textRenderer, "Permission", tableX, tableY, 0xFFFFFF);
            context.drawTextWithShadow(this.textRenderer, "Access", tableX + col1Width, tableY, 0xFFFFFF);
            tableY += rowHeight;

            // Draw permission rows - more permissions
            String[] permissions = {"Build", "Interact", "Containers", "Redstone", "Doors", "Crops", "Animals", "Villagers"};
            boolean[] hasAccess = getPermissionsForRank(displayRank, selectedTag);

            // Ensure we have enough access values (fill with defaults if needed)
            if (hasAccess.length < permissions.length) {
                boolean[] extendedAccess = new boolean[permissions.length];
                System.arraycopy(hasAccess, 0, extendedAccess, 0, hasAccess.length);
                // Set default values for additional permissions
                for (int i = hasAccess.length; i < permissions.length; i++) {
                    extendedAccess[i] = displayRank != null && displayRank.ordinal() >= TownPlayerRank.MEMBER.ordinal();
                }
                hasAccess = extendedAccess;
            }

            // Draw in two columns to save vertical space
            int colSpacing = 20;
            int col2StartX = tableX + col1Width + col2Width + colSpacing;
            int originalTableY = tableY;

            for (int i = 0; i < permissions.length; i++) {
                int currentTableX;
                int currentTableY;

                // Determine if this is in the first or second column
                if (i < permissions.length / 2) {
                    currentTableX = tableX;
                    currentTableY = tableY + (i * rowHeight);
                } else {
                    currentTableX = col2StartX;
                    currentTableY = originalTableY + ((i - permissions.length / 2) * rowHeight);
                }

                // Draw row background (alternating)
                int bgColor = i % 2 == 0 ? 0x20FFFFFF : 0x30FFFFFF;
                context.fill(currentTableX, currentTableY, currentTableX + col1Width + col2Width, currentTableY + rowHeight, bgColor);

                // Draw permission name
                context.drawTextWithShadow(this.textRenderer, permissions[i],
                    currentTableX + 5, currentTableY + 4, 0xFFFFFF);

                // Draw access status
                String accessText = hasAccess[i] ? "Yes" : "No";
                int accessColor = hasAccess[i] ? 0x55FF55 : 0xFF5555;
                context.drawTextWithShadow(this.textRenderer, accessText,
                    currentTableX + col1Width + 5, currentTableY + 4, accessColor);
            }

            // Draw buttons at the bottom right
            int buttonWidth = 100;
            int buttonHeight = 20;
            int buttonSpacing = 10;
            int buttonsY = topY + panelHeight - buttonHeight - 10;

            // Draw color button
            int colorButtonX = leftX + panelWidth - (buttonWidth * 2) - buttonSpacing - 10;
            boolean colorButtonHovered = mouseX >= colorButtonX && mouseX <= colorButtonX + buttonWidth &&
                                        mouseY >= buttonsY && mouseY <= buttonsY + buttonHeight;
            drawModernButton(context, colorButtonX, buttonsY, buttonWidth, buttonHeight, selectedTag.getColor(), // Use tag color
                colorButtonHovered, true);
            context.drawCenteredTextWithShadow(this.textRenderer, "Change Color",
                colorButtonX + buttonWidth / 2, buttonsY + 6, 0xFFFFFF);

            // Draw tag settings button
            int settingsButtonX = leftX + panelWidth - buttonWidth - 10;
            boolean settingsButtonHovered = mouseX >= settingsButtonX && mouseX <= settingsButtonX + buttonWidth &&
                                          mouseY >= buttonsY && mouseY <= buttonsY + buttonHeight;
            drawModernButton(context, settingsButtonX, buttonsY, buttonWidth, buttonHeight, 0xFF9C27B0, // Purple
                settingsButtonHovered, true);
            context.drawCenteredTextWithShadow(this.textRenderer, "Tag Settings",
                settingsButtonX + buttonWidth / 2, buttonsY + 6, 0xFFFFFF);
        } else {
            // No tag selected
            context.drawCenteredTextWithShadow(this.textRenderer, "No tag selected",
                    contentAreaX + contentAreaWidth / 2, contentY + 50, 0xAAAAAA);
        }

        // Draw buttons and other UI elements
        super.render(context, mouseX, mouseY, delta);
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Calculate dimensions - same as in render method
        int sidebarX = leftX + 10;
        int sidebarY = topY + 25;
        int sidebarWidth = 120;

        int tagY = sidebarY + 30;
        int tagHeight = 25;

        // Check for click on "Create New Tag"
        if (mouseX >= sidebarX && mouseX <= sidebarX + sidebarWidth &&
            mouseY >= tagY && mouseY <= tagY + tagHeight) {
            // Create a new tag
            createNewTag();
            return true;
        }

        // Move to the next row for actual tags
        tagY += tagHeight;

        int visibleTags = 1; // Start at 1 because we already processed the "Create New Tag" item
        int maxVisibleTags = (panelHeight - 65) / tagHeight;

        for (int i = tagsScrollOffset; i < tags.size() && visibleTags < maxVisibleTags; i++) {
            ClaimTag tag = tags.get(i);

            if (mouseX >= sidebarX && mouseX <= sidebarX + sidebarWidth &&
                mouseY >= tagY && mouseY <= tagY + tagHeight) {
                // Select this tag
                selectedTag = tag;
                updateButtonStates();
                return true;
            }

            tagY += tagHeight;
            visibleTags++;
        }

        // Check for clicks on buttons and controls
        if (selectedTag != null) {
            int contentAreaX = sidebarX + sidebarWidth + 10;
            int contentY = topY + 25;

            // Calculate content area width
            int contentAreaWidth = panelWidth - sidebarWidth - 30; // Same calculation as in render method

            // Check for click on rank selection button
            int infoY = contentY + 60; // Moved down to avoid overlap with tag name field
            int rankButtonWidth = 120;
            int rankButtonHeight = 20;
            int rankButtonX = contentAreaX + 200; // Fixed position
            int rankButtonY = infoY - 2; // Align with the Permission Info header

            if (mouseX >= rankButtonX && mouseX <= rankButtonX + rankButtonWidth &&
                mouseY >= rankButtonY && mouseY <= rankButtonY + rankButtonHeight) {
                // Cycle to the next rank
                cycleDisplayRank();
                return true;
            }

            // Check for click on color button
            int buttonWidth = 100;
            int buttonHeight = 20;
            int buttonSpacing = 10;
            int buttonsY = topY + panelHeight - buttonHeight - 10;
            int colorButtonX = leftX + panelWidth - (buttonWidth * 2) - buttonSpacing - 10;

            if (mouseX >= colorButtonX && mouseX <= colorButtonX + buttonWidth &&
                mouseY >= buttonsY && mouseY <= buttonsY + buttonHeight) {
                // Open color picker dialog
                openColorPicker();
                return true;
            }

            // Check for click on tag settings button
            int settingsButtonX = leftX + panelWidth - buttonWidth - 10;

            if (mouseX >= settingsButtonX && mouseX <= settingsButtonX + buttonWidth &&
                mouseY >= buttonsY && mouseY <= buttonsY + buttonHeight) {
                // Open tag settings screen
                this.client.setScreen(new TagSettingsScreen(this, selectedTag));
                return true;
            }

            // Use three-column layout for permissions
            int colWidth = contentAreaWidth / 3;
            int col1X = contentAreaX;
            int col2X = contentAreaX + colWidth;
            int col3X = contentAreaX + colWidth * 2;

            // Calculate permission positions
            int headerY = contentY + 55; // Moved up
            int permY = headerY + 15; // Closer to header
            int permHeight = 16; // Smaller height

            // Check for clicks on town member rank settings (column 2)
            int clickY = permY;

            // Build rank toggle
            if (mouseX >= col2X && mouseX <= col2X + colWidth &&
                mouseY >= clickY && mouseY <= clickY + permHeight) {
                // Cycle through ranks
                TownPlayerRank currentRank = selectedTag.getMinRankBuild();
                TownPlayerRank newRank = getNextRank(currentRank);
                selectedTag.setMinRankBuild(newRank);

                // Update the claim tool
                updateClaimToolTags();

                // Show notification
                NotificationRenderer.addNotification("Build permission set to " + getRankText(newRank));
                return true;
            }
            clickY += permHeight;

            // Check for clicks on non-member permission toggles (column 3)
            clickY = permY;

            // Allow build toggle
            if (mouseX >= col3X && mouseX <= col3X + colWidth &&
                mouseY >= clickY && mouseY <= clickY + permHeight) {
                boolean newValue = !selectedTag.allowsBuild();
                selectedTag.setAllowBuild(newValue);

                // Update the claim tool
                updateClaimToolTags();

                // Show notification
                NotificationRenderer.addNotification("Non-member build permission set to " + (newValue ? "Yes" : "No"));
                return true;
            }
            clickY += permHeight;

            // Interact rank toggle
            if (mouseX >= col2X && mouseX <= col2X + colWidth &&
                mouseY >= clickY && mouseY <= clickY + permHeight) {
                // Cycle through ranks
                TownPlayerRank currentRank = selectedTag.getMinRankInteract();
                TownPlayerRank newRank = getNextRank(currentRank);
                selectedTag.setMinRankInteract(newRank);

                // Update the claim tool
                updateClaimToolTags();

                // Show notification
                NotificationRenderer.addNotification("Interact permission set to " + getRankText(newRank));
                return true;
            }
            clickY += permHeight;

            // Container rank toggle
            if (mouseX >= col2X && mouseX <= col2X + colWidth &&
                mouseY >= clickY && mouseY <= clickY + permHeight) {
                // Cycle through ranks
                TownPlayerRank currentRank = selectedTag.getMinRankContainers();
                TownPlayerRank newRank = getNextRank(currentRank);
                selectedTag.setMinRankContainers(newRank);

                // Update the claim tool
                updateClaimToolTags();

                // Show notification
                NotificationRenderer.addNotification("Container permission set to " + getRankText(newRank));
                return true;
            }

            // Allow interact toggle
            clickY = permY + permHeight; // Second row
            if (mouseX >= col3X && mouseX <= col3X + colWidth &&
                mouseY >= clickY && mouseY <= clickY + permHeight) {
                boolean newValue = !selectedTag.allowsInteract();
                selectedTag.setAllowInteract(newValue);

                // Update the claim tool
                updateClaimToolTags();

                // Show notification
                NotificationRenderer.addNotification("Non-member interact permission set to " + (newValue ? "Yes" : "No"));
                return true;
            }
            clickY += permHeight;

            // Allow containers toggle
            if (mouseX >= col3X && mouseX <= col3X + colWidth &&
                mouseY >= clickY && mouseY <= clickY + permHeight) {
                selectedTag.setAllowContainers(!selectedTag.allowsContainers());
                return true;
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double amount) {
        // Handle scrolling in the tag list
        int sidebarX = leftX + 10;
        int sidebarY = topY + 25;
        int sidebarWidth = 120;
        int sidebarHeight = panelHeight - 35;

        if (mouseX >= sidebarX && mouseX <= sidebarX + sidebarWidth &&
            mouseY >= sidebarY && mouseY <= sidebarY + sidebarHeight) {
            // Scroll the tag list
            if (amount > 0 && tagsScrollOffset > 0) {
                tagsScrollOffset--;
                return true;
            } else if (amount < 0 && tagsScrollOffset < tags.size() - (sidebarHeight - 40) / 25) {
                tagsScrollOffset++;
                return true;
            }
        }

        return super.mouseScrolled(mouseX, mouseY, amount);
    }

    @Override
    public void tick() {
        super.tick();

        // Update text field
        tagNameField.tick();

        // Update selected tag name if text field has changed
        if (selectedTag != null && !tagNameField.getText().equals(selectedTag.getName())) {
            String oldName = selectedTag.getName();
            selectedTag.setName(tagNameField.getText());

            // Update the claim tool with the renamed tag
            updateClaimToolTags();

            // Send a notification (only if the change is significant)
            if (!oldName.equals(tagNameField.getText()) && tagNameField.getText().length() > 2) {
                NotificationRenderer.addNotification("Renamed tag from '" + oldName + "' to '" + tagNameField.getText() + "'");
            }
        }
    }

    @Override
    public boolean shouldPause() {
        return false;
    }

    /**
     * Handles key presses in the tag screen.
     * This is used to properly handle the ESC key to close only the tag screen, not the claim tool.
     */
    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        // Handle ESC key to close the screen without closing the claim tool
        if (keyCode == GLFW.GLFW_KEY_ESCAPE) {
            // Log the key press
            com.pokecobble.Pokecobbleclaim.LOGGER.info("ESC key pressed in ClaimTagScreen");

            // Close this screen and return to parent if available
            close();
            return true;
        }

        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    /**
     * Closes the screen without deactivating the claim tool.
     * This method is overridden to handle different parent screens.
     */
    @Override
    public void close() {
        if (parentScreen != null) {
            // Return to parent screen if available
            this.client.setScreen(parentScreen);
        } else {
            // Just close the screen without affecting the claim tool
            this.client.setScreen(null);

            // Show a notification only when closing to null (from claim tool)
            NotificationRenderer.addNotification("Claim tag screen closed");
        }
    }

    private String getRankText(TownPlayerRank rank) {
        switch (rank) {
            case OWNER:
                return "Mayor";
            case ADMIN:
                return "Deputy";
            case MODERATOR:
                return "Council";
            case MEMBER:
                return "Resident";
            case VISITOR:
                return "Citizen";
            default:
                return "Unknown";
        }
    }

    private int getRankColor(TownPlayerRank rank) {
        switch (rank) {
            case OWNER:
                return 0xFFAA00; // Gold
            case ADMIN:
                return 0xFF5555; // Red
            case MODERATOR:
                return 0x55FFFF; // Aqua
            case MEMBER:
                return 0x55FF55; // Green
            case VISITOR:
                return 0xAAAAAA; // Gray
            default:
                return 0xFFFFFF; // White
        }
    }

    /**
     * Gets the next rank in the cycle.
     */
    private TownPlayerRank getNextRank(TownPlayerRank currentRank) {
        switch (currentRank) {
            case VISITOR:
                return TownPlayerRank.MEMBER;
            case MEMBER:
                return TownPlayerRank.MODERATOR;
            case MODERATOR:
                return TownPlayerRank.ADMIN;
            case ADMIN:
                return TownPlayerRank.OWNER;
            case OWNER:
                return TownPlayerRank.VISITOR;
            default:
                return TownPlayerRank.MEMBER;
        }
    }

    /**
     * Gets the current rank being displayed in the permission info.
     */
    private TownPlayerRank getCurrentDisplayRank() {
        if (showingNonMember) {
            return null; // null represents non-member
        } else if (currentDisplayRank == null) {
            // Default to MEMBER if not set
            return TownPlayerRank.MEMBER;
        } else {
            return currentDisplayRank;
        }
    }

    /**
     * Cycles to the next rank for display.
     */
    private void cycleDisplayRank() {
        if (showingNonMember) {
            // Switch to VISITOR rank
            showingNonMember = false;
            currentDisplayRank = TownPlayerRank.VISITOR;
        } else {
            // Handle the case where currentDisplayRank might be null (shouldn't happen now with initialization)
            if (currentDisplayRank == null) {
                currentDisplayRank = TownPlayerRank.VISITOR;
            } else if (currentDisplayRank == TownPlayerRank.OWNER) {
                // Switch to non-member
                showingNonMember = true;
                // Keep currentDisplayRank as is, we'll just use showingNonMember flag
            } else {
                // Cycle to next rank
                currentDisplayRank = getNextRank(currentDisplayRank);
            }
        }
    }

    /**
     * Gets the permissions for the given rank and tag.
     * Each rank's permissions are independent of other ranks.
     *
     * @param rank The rank to check, or null for non-member
     * @param tag The tag to check
     * @return Array of booleans [build, interact, containers, redstone, doors, crops, animals, villagers]
     */
    private boolean[] getPermissionsForRank(TownPlayerRank rank, ClaimTag tag) {
        // Use the new permission system that stores permissions for each rank independently
        return tag.getPermissionsForRank(rank);
    }

    /**
     * Draws a modern button with the given parameters.
     */
    private void drawModernButton(DrawContext context, int x, int y, int width, int height, int color, boolean hovered, boolean enabled) {
        // Draw button background
        // Ensure color has full alpha channel
        int bgColor = enabled ? (color | 0xFF000000) : 0xFF555555;

        // Apply hover effect without changing alpha
        if (hovered && enabled) {
            // Make color slightly brighter when hovered
            int r = Math.min(255, ((bgColor >> 16) & 0xFF) + 20);
            int g = Math.min(255, ((bgColor >> 8) & 0xFF) + 20);
            int b = Math.min(255, (bgColor & 0xFF) + 20);
            bgColor = 0xFF000000 | (r << 16) | (g << 8) | b;
        }

        // Draw button
        context.fill(x, y, x + width, y + height, bgColor);

        // Draw button border
        int borderColor = 0x60FFFFFF;
        context.drawBorder(x, y, width, height, borderColor);
    }

    /**
     * Opens a color picker dialog to change the tag color.
     */
    private void openColorPicker() {
        if (selectedTag == null) return;

        // Open the new ColorPickerScreen
        this.client.setScreen(new ColorPickerScreen(this, selectedTag));
    }

    /**
     * Updates the claim tool with the current tags.
     * This ensures that any changes made to tags in this screen are reflected in the claim tool.
     */
    public void updateClaimToolTags() {
        if (town == null) return;

        // Get the claim tool instance
        ClaimTool claimTool = ClaimTool.getInstance();
        if (claimTool == null) return;

        // Update the claim tool's tag list
        claimTool.updateTags(tags);

        // Save the tags to the town
        town.updateClaimTags(tags);
    }
}
