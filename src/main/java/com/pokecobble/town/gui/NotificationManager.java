package com.pokecobble.town.gui;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.text.Text;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * Manages temporary notifications that appear at the bottom of the screen.
 */
public class NotificationManager {
    private static NotificationManager instance;
    
    private final List<Notification> notifications = new ArrayList<>();
    private final MinecraftClient client;
    
    private NotificationManager() {
        this.client = MinecraftClient.getInstance();
    }
    
    /**
     * Gets the singleton instance of the NotificationManager.
     *
     * @return The NotificationManager instance
     */
    public static NotificationManager getInstance() {
        if (instance == null) {
            instance = new NotificationManager();
        }
        return instance;
    }
    
    /**
     * Adds a notification to be displayed.
     *
     * @param message The message to display
     * @param color The color of the message (in ARGB format)
     * @param durationTicks How long to display the message (in ticks)
     */
    public void addNotification(String message, int color, int durationTicks) {
        notifications.add(new Notification(message, color, durationTicks));
    }
    
    /**
     * Adds an error notification (red text).
     *
     * @param message The error message to display
     */
    public void addErrorNotification(String message) {
        addNotification(message, 0xFFFF5555, 60); // Red color, 3 seconds
    }
    
    /**
     * Adds a success notification (green text).
     *
     * @param message The success message to display
     */
    public void addSuccessNotification(String message) {
        addNotification(message, 0xFF55FF55, 60); // Green color, 3 seconds
    }
    
    /**
     * Adds an info notification (white text).
     *
     * @param message The info message to display
     */
    public void addInfoNotification(String message) {
        addNotification(message, 0xFFFFFFFF, 60); // White color, 3 seconds
    }
    
    /**
     * Updates all notifications, removing expired ones.
     */
    public void update() {
        Iterator<Notification> iterator = notifications.iterator();
        while (iterator.hasNext()) {
            Notification notification = iterator.next();
            notification.remainingTicks--;
            if (notification.remainingTicks <= 0) {
                iterator.remove();
            }
        }
    }
    
    /**
     * Renders all active notifications.
     *
     * @param context The draw context
     */
    public void render(DrawContext context) {
        if (notifications.isEmpty()) {
            return;
        }
        
        int screenWidth = client.getWindow().getScaledWidth();
        int screenHeight = client.getWindow().getScaledHeight();
        
        // Calculate position for notifications (bottom center)
        int y = screenHeight - 50; // 50 pixels from the bottom
        
        // Render notifications from newest to oldest
        for (int i = notifications.size() - 1; i >= 0; i--) {
            Notification notification = notifications.get(i);
            
            // Calculate fade in/out alpha
            float alpha = 1.0f;
            if (notification.remainingTicks < 10) {
                // Fade out in the last 10 ticks
                alpha = notification.remainingTicks / 10.0f;
            } else if (notification.durationTicks - notification.remainingTicks < 10) {
                // Fade in in the first 10 ticks
                alpha = (notification.durationTicks - notification.remainingTicks) / 10.0f;
            }
            
            // Apply alpha to color
            int color = notification.color & 0x00FFFFFF; // Remove alpha
            int alphaInt = Math.max(0, Math.min(255, (int)(alpha * 255))) << 24;
            color = alphaInt | color;
            
            // Calculate text width and position
            int textWidth = client.textRenderer.getWidth(notification.message);
            int x = (screenWidth - textWidth) / 2;
            
            // Draw background
            int bgColor = (alphaInt / 2) << 24; // Semi-transparent black
            context.fill(x - 4, y - 4, x + textWidth + 4, y + 12, bgColor);
            
            // Draw text
            context.drawText(client.textRenderer, notification.message, x, y, color, false);
            
            // Move up for the next notification
            y -= 20;
        }
    }
    
    /**
     * Represents a single notification.
     */
    private static class Notification {
        private final String message;
        private final int color;
        private final int durationTicks;
        private int remainingTicks;
        
        public Notification(String message, int color, int durationTicks) {
            this.message = message;
            this.color = color;
            this.durationTicks = durationTicks;
            this.remainingTicks = durationTicks;
        }
    }
}
