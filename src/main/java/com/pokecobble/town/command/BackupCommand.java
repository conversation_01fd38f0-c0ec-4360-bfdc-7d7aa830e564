package com.pokecobble.town.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.BoolArgumentType;
import com.mojang.brigadier.arguments.IntegerArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.PlayerDataManager;
import com.pokecobble.town.config.BackupConfig;
import com.pokecobble.town.data.PlayerDataUtils;
import net.minecraft.command.CommandRegistryAccess;
import net.minecraft.server.command.CommandManager;
import net.minecraft.server.command.ServerCommandSource;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

/**
 * Command for managing player data backups.
 */
public class BackupCommand {

    /**
     * Registers the backup command.
     *
     * @param dispatcher The command dispatcher
     * @param registryAccess The command registry access
     * @param environment The command registration environment
     */
    public static void register(CommandDispatcher<ServerCommandSource> dispatcher, CommandRegistryAccess registryAccess, CommandManager.RegistrationEnvironment environment) {
        dispatcher.register(CommandManager.literal("townbackup")
            .requires(source -> source.hasPermissionLevel(3)) // Require permission level 3 (operator)
            .then(CommandManager.literal("create")
                .executes(BackupCommand::createBackup))
            .then(CommandManager.literal("config")
                .then(CommandManager.literal("autobackup")
                    .then(CommandManager.argument("enabled", BoolArgumentType.bool())
                        .executes(context -> setAutoBackup(context, BoolArgumentType.getBool(context, "enabled")))))
                .then(CommandManager.literal("interval")
                    .then(CommandManager.argument("hours", IntegerArgumentType.integer(1, 168))
                        .executes(context -> setBackupInterval(context, IntegerArgumentType.getInteger(context, "hours")))))
                .then(CommandManager.literal("maxbackups")
                    .then(CommandManager.argument("count", IntegerArgumentType.integer(1, 100))
                        .executes(context -> setMaxBackups(context, IntegerArgumentType.getInteger(context, "count")))))
                .then(CommandManager.literal("compression")
                    .then(CommandManager.argument("enabled", BoolArgumentType.bool())
                        .executes(context -> setCompression(context, BoolArgumentType.getBool(context, "enabled")))))
                .then(CommandManager.literal("compressionlevel")
                    .then(CommandManager.argument("level", IntegerArgumentType.integer(0, 9))
                        .executes(context -> setCompressionLevel(context, IntegerArgumentType.getInteger(context, "level")))))
                .then(CommandManager.literal("backuponstop")
                    .then(CommandManager.argument("enabled", BoolArgumentType.bool())
                        .executes(context -> setBackupOnStop(context, BoolArgumentType.getBool(context, "enabled")))))
                .then(CommandManager.literal("incremental")
                    .then(CommandManager.argument("enabled", BoolArgumentType.bool())
                        .executes(context -> setIncrementalBackups(context, BoolArgumentType.getBool(context, "enabled")))))
                .then(CommandManager.literal("differential")
                    .then(CommandManager.argument("enabled", BoolArgumentType.bool())
                        .executes(context -> setDifferentialBackups(context, BoolArgumentType.getBool(context, "enabled")))))
                .then(CommandManager.literal("verify")
                    .then(CommandManager.argument("enabled", BoolArgumentType.bool())
                        .executes(context -> setVerifyBackups(context, BoolArgumentType.getBool(context, "enabled")))))
                .then(CommandManager.literal("buffersize")
                    .then(CommandManager.argument("size", IntegerArgumentType.integer(1024, 65536))
                        .executes(context -> setBufferSize(context, IntegerArgumentType.getInteger(context, "size")))))
                .executes(BackupCommand::showConfig))
            .then(CommandManager.literal("cleanup")
                .executes(BackupCommand::cleanupBackups))
            .executes(BackupCommand::showHelp));

        Pokecobbleclaim.LOGGER.info("Registered backup command");
    }

    /**
     * Creates a backup of all player data.
     *
     * @param context The command context
     * @return 1 if successful, 0 if failed
     */
    private static int createBackup(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();

        // Save all player data first
        PlayerDataManager.getInstance().saveAllPlayers();

        // Create backup
        source.sendFeedback(() -> Text.literal("Creating player data backup...").formatted(Formatting.YELLOW), true);
        String backupPath = PlayerDataUtils.backupAllPlayerData();

        if (backupPath != null) {
            source.sendFeedback(() -> Text.literal("Backup created at: " + backupPath).formatted(Formatting.GREEN), true);
            return 1;
        } else {
            source.sendError(Text.literal("Failed to create backup").formatted(Formatting.RED));
            return 0;
        }
    }

    /**
     * Shows the current backup configuration.
     *
     * @param context The command context
     * @return 1
     */
    private static int showConfig(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();

        source.sendFeedback(() -> Text.literal("=== Backup Configuration ===").formatted(Formatting.GOLD), false);

        // Basic settings
        source.sendFeedback(() -> Text.literal("--- Basic Settings ---").formatted(Formatting.YELLOW), false);
        source.sendFeedback(() -> Text.literal("Auto Backup: " + (BackupConfig.isAutoBackupEnabled() ? "Enabled" : "Disabled"))
            .formatted(BackupConfig.isAutoBackupEnabled() ? Formatting.GREEN : Formatting.RED), false);
        source.sendFeedback(() -> Text.literal("Backup Interval: " + BackupConfig.getBackupIntervalHours() + " hours")
            .formatted(Formatting.AQUA), false);
        source.sendFeedback(() -> Text.literal("Max Backups: " + BackupConfig.getMaxBackups())
            .formatted(Formatting.AQUA), false);
        source.sendFeedback(() -> Text.literal("Backup on Server Stop: " + (BackupConfig.isBackupOnServerStopEnabled() ? "Enabled" : "Disabled"))
            .formatted(BackupConfig.isBackupOnServerStopEnabled() ? Formatting.GREEN : Formatting.RED), false);

        // Compression settings
        source.sendFeedback(() -> Text.literal("--- Compression Settings ---").formatted(Formatting.YELLOW), false);
        source.sendFeedback(() -> Text.literal("Compression: " + (BackupConfig.isCompressionEnabled() ? "Enabled" : "Disabled"))
            .formatted(BackupConfig.isCompressionEnabled() ? Formatting.GREEN : Formatting.RED), false);
        source.sendFeedback(() -> Text.literal("Compression Level: " + BackupConfig.getCompressionLevel() + " (0-9, where 9 is highest)")
            .formatted(Formatting.AQUA), false);
        source.sendFeedback(() -> Text.literal("Buffer Size: " + BackupConfig.getBufferSize() + " bytes")
            .formatted(Formatting.AQUA), false);

        // Advanced settings
        source.sendFeedback(() -> Text.literal("--- Advanced Settings ---").formatted(Formatting.YELLOW), false);
        source.sendFeedback(() -> Text.literal("Incremental Backups: " + (BackupConfig.isIncrementalBackupsEnabled() ? "Enabled" : "Disabled"))
            .formatted(BackupConfig.isIncrementalBackupsEnabled() ? Formatting.GREEN : Formatting.RED), false);
        source.sendFeedback(() -> Text.literal("Differential Backups: " + (BackupConfig.isDifferentialBackupsEnabled() ? "Enabled" : "Disabled"))
            .formatted(BackupConfig.isDifferentialBackupsEnabled() ? Formatting.GREEN : Formatting.RED), false);
        source.sendFeedback(() -> Text.literal("Backup Verification: " + (BackupConfig.isVerifyBackupsEnabled() ? "Enabled" : "Disabled"))
            .formatted(BackupConfig.isVerifyBackupsEnabled() ? Formatting.GREEN : Formatting.RED), false);

        return 1;
    }

    /**
     * Sets whether automatic backups are enabled.
     *
     * @param context The command context
     * @param enabled Whether automatic backups are enabled
     * @return 1
     */
    private static int setAutoBackup(CommandContext<ServerCommandSource> context, boolean enabled) {
        ServerCommandSource source = context.getSource();

        BackupConfig.setAutoBackupEnabled(enabled);

        source.sendFeedback(() -> Text.literal("Auto Backup: " + (enabled ? "Enabled" : "Disabled"))
            .formatted(enabled ? Formatting.GREEN : Formatting.RED), true);

        return 1;
    }

    /**
     * Sets the backup interval in hours.
     *
     * @param context The command context
     * @param hours The backup interval in hours
     * @return 1
     */
    private static int setBackupInterval(CommandContext<ServerCommandSource> context, int hours) {
        ServerCommandSource source = context.getSource();

        BackupConfig.setBackupIntervalHours(hours);

        source.sendFeedback(() -> Text.literal("Backup Interval set to: " + hours + " hours")
            .formatted(Formatting.GREEN), true);

        return 1;
    }

    /**
     * Sets the maximum number of backups to keep.
     *
     * @param context The command context
     * @param count The maximum number of backups
     * @return 1
     */
    private static int setMaxBackups(CommandContext<ServerCommandSource> context, int count) {
        ServerCommandSource source = context.getSource();

        BackupConfig.setMaxBackups(count);

        source.sendFeedback(() -> Text.literal("Max Backups set to: " + count)
            .formatted(Formatting.GREEN), true);

        return 1;
    }

    /**
     * Sets whether player data compression is enabled.
     *
     * @param context The command context
     * @param enabled Whether player data compression is enabled
     * @return 1
     */
    private static int setCompression(CommandContext<ServerCommandSource> context, boolean enabled) {
        ServerCommandSource source = context.getSource();

        BackupConfig.setCompressionEnabled(enabled);

        source.sendFeedback(() -> Text.literal("Compression: " + (enabled ? "Enabled" : "Disabled"))
            .formatted(enabled ? Formatting.GREEN : Formatting.RED), true);

        return 1;
    }

    /**
     * Sets whether backup on server stop is enabled.
     *
     * @param context The command context
     * @param enabled Whether backup on server stop is enabled
     * @return 1
     */
    private static int setBackupOnStop(CommandContext<ServerCommandSource> context, boolean enabled) {
        ServerCommandSource source = context.getSource();

        BackupConfig.setBackupOnServerStopEnabled(enabled);

        source.sendFeedback(() -> Text.literal("Backup on Server Stop: " + (enabled ? "Enabled" : "Disabled"))
            .formatted(enabled ? Formatting.GREEN : Formatting.RED), true);

        return 1;
    }

    /**
     * Sets the compression level.
     *
     * @param context The command context
     * @param level The compression level (0-9, where 9 is highest)
     * @return 1
     */
    private static int setCompressionLevel(CommandContext<ServerCommandSource> context, int level) {
        ServerCommandSource source = context.getSource();

        BackupConfig.setCompressionLevel(level);

        source.sendFeedback(() -> Text.literal("Compression Level set to: " + level + " (0-9, where 9 is highest)")
            .formatted(Formatting.GREEN), true);

        return 1;
    }

    /**
     * Sets whether incremental backups are enabled.
     *
     * @param context The command context
     * @param enabled Whether incremental backups are enabled
     * @return 1
     */
    private static int setIncrementalBackups(CommandContext<ServerCommandSource> context, boolean enabled) {
        ServerCommandSource source = context.getSource();

        BackupConfig.setIncrementalBackupsEnabled(enabled);

        source.sendFeedback(() -> Text.literal("Incremental Backups: " + (enabled ? "Enabled" : "Disabled"))
            .formatted(enabled ? Formatting.GREEN : Formatting.RED), true);

        return 1;
    }

    /**
     * Sets whether differential backups are enabled.
     *
     * @param context The command context
     * @param enabled Whether differential backups are enabled
     * @return 1
     */
    private static int setDifferentialBackups(CommandContext<ServerCommandSource> context, boolean enabled) {
        ServerCommandSource source = context.getSource();

        BackupConfig.setDifferentialBackupsEnabled(enabled);

        source.sendFeedback(() -> Text.literal("Differential Backups: " + (enabled ? "Enabled" : "Disabled"))
            .formatted(enabled ? Formatting.GREEN : Formatting.RED), true);

        return 1;
    }

    /**
     * Sets whether backup verification is enabled.
     *
     * @param context The command context
     * @param enabled Whether backup verification is enabled
     * @return 1
     */
    private static int setVerifyBackups(CommandContext<ServerCommandSource> context, boolean enabled) {
        ServerCommandSource source = context.getSource();

        BackupConfig.setVerifyBackupsEnabled(enabled);

        source.sendFeedback(() -> Text.literal("Backup Verification: " + (enabled ? "Enabled" : "Disabled"))
            .formatted(enabled ? Formatting.GREEN : Formatting.RED), true);

        return 1;
    }

    /**
     * Sets the buffer size for I/O operations.
     *
     * @param context The command context
     * @param size The buffer size in bytes
     * @return 1
     */
    private static int setBufferSize(CommandContext<ServerCommandSource> context, int size) {
        ServerCommandSource source = context.getSource();

        BackupConfig.setBufferSize(size);

        source.sendFeedback(() -> Text.literal("Buffer Size set to: " + size + " bytes")
            .formatted(Formatting.GREEN), true);

        return 1;
    }

    /**
     * Cleans up old backups.
     *
     * @param context The command context
     * @return 1
     */
    private static int cleanupBackups(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();

        source.sendFeedback(() -> Text.literal("Cleaning up old backups...").formatted(Formatting.YELLOW), true);
        int deleted = PlayerDataUtils.cleanupOldBackups(BackupConfig.getMaxBackups());

        if (deleted > 0) {
            source.sendFeedback(() -> Text.literal("Cleaned up " + deleted + " old backups").formatted(Formatting.GREEN), true);
        } else {
            source.sendFeedback(() -> Text.literal("No old backups to clean up").formatted(Formatting.AQUA), true);
        }

        return 1;
    }

    /**
     * Shows help for the backup command.
     *
     * @param context The command context
     * @return 1
     */
    private static int showHelp(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();

        source.sendFeedback(() -> Text.literal("=== Town Backup Commands ===").formatted(Formatting.GOLD), false);
        source.sendFeedback(() -> Text.literal("/townbackup create").formatted(Formatting.YELLOW)
            .append(Text.literal(" - Create a backup of all player data").formatted(Formatting.WHITE)), false);
        source.sendFeedback(() -> Text.literal("/townbackup config").formatted(Formatting.YELLOW)
            .append(Text.literal(" - Show current backup configuration").formatted(Formatting.WHITE)), false);

        // Basic settings
        source.sendFeedback(() -> Text.literal("--- Basic Settings ---").formatted(Formatting.AQUA), false);
        source.sendFeedback(() -> Text.literal("/townbackup config autobackup <true|false>").formatted(Formatting.YELLOW)
            .append(Text.literal(" - Enable/disable automatic backups").formatted(Formatting.WHITE)), false);
        source.sendFeedback(() -> Text.literal("/townbackup config interval <hours>").formatted(Formatting.YELLOW)
            .append(Text.literal(" - Set backup interval in hours").formatted(Formatting.WHITE)), false);
        source.sendFeedback(() -> Text.literal("/townbackup config maxbackups <count>").formatted(Formatting.YELLOW)
            .append(Text.literal(" - Set maximum number of backups to keep").formatted(Formatting.WHITE)), false);
        source.sendFeedback(() -> Text.literal("/townbackup config backuponstop <true|false>").formatted(Formatting.YELLOW)
            .append(Text.literal(" - Enable/disable backup on server stop").formatted(Formatting.WHITE)), false);

        // Compression settings
        source.sendFeedback(() -> Text.literal("--- Compression Settings ---").formatted(Formatting.AQUA), false);
        source.sendFeedback(() -> Text.literal("/townbackup config compression <true|false>").formatted(Formatting.YELLOW)
            .append(Text.literal(" - Enable/disable player data compression").formatted(Formatting.WHITE)), false);
        source.sendFeedback(() -> Text.literal("/townbackup config compressionlevel <0-9>").formatted(Formatting.YELLOW)
            .append(Text.literal(" - Set compression level (0-9, where 9 is highest)").formatted(Formatting.WHITE)), false);
        source.sendFeedback(() -> Text.literal("/townbackup config buffersize <size>").formatted(Formatting.YELLOW)
            .append(Text.literal(" - Set buffer size for I/O operations in bytes").formatted(Formatting.WHITE)), false);

        // Advanced settings
        source.sendFeedback(() -> Text.literal("--- Advanced Settings ---").formatted(Formatting.AQUA), false);
        source.sendFeedback(() -> Text.literal("/townbackup config incremental <true|false>").formatted(Formatting.YELLOW)
            .append(Text.literal(" - Enable/disable incremental backups").formatted(Formatting.WHITE)), false);
        source.sendFeedback(() -> Text.literal("/townbackup config differential <true|false>").formatted(Formatting.YELLOW)
            .append(Text.literal(" - Enable/disable differential backups").formatted(Formatting.WHITE)), false);
        source.sendFeedback(() -> Text.literal("/townbackup config verify <true|false>").formatted(Formatting.YELLOW)
            .append(Text.literal(" - Enable/disable backup verification").formatted(Formatting.WHITE)), false);

        // Maintenance
        source.sendFeedback(() -> Text.literal("--- Maintenance ---").formatted(Formatting.AQUA), false);
        source.sendFeedback(() -> Text.literal("/townbackup cleanup").formatted(Formatting.YELLOW)
            .append(Text.literal(" - Clean up old backups").formatted(Formatting.WHITE)), false);

        return 1;
    }
}
