package com.pokecobble.town.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.context.CommandContext;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.logging.ErrorLogger;
import com.pokecobble.town.logging.ErrorLogger.ErrorSeverity;
import com.pokecobble.town.network.ErrorLogNetworkHandler;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.fabric.api.command.v2.CommandRegistrationCallback;
import net.minecraft.command.CommandRegistryAccess;
import net.minecraft.server.command.CommandManager;
import net.minecraft.server.command.ServerCommandSource;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;

import io.netty.buffer.Unpooled;
import java.util.List;
import java.util.UUID;

/**
 * Command for opening the error log screen and managing error logs.
 */
public class ErrorLogCommand {
    /**
     * Registers the client-side command.
     * This method should only be called from the client-side initialization.
     */
    @Environment(EnvType.CLIENT)
    public static void registerClientCommand() {
        // This method is implemented in PokecobbleclaimClient class
        // to avoid class loading issues on the server
    }

    /**
     * Registers the server-side command.
     */
    public static void registerServerCommand() {
        CommandRegistrationCallback.EVENT.register((dispatcher, registryAccess, environment) -> {
            dispatcher.register(
                CommandManager.literal("townerrors")
                    .requires(source -> source.hasPermissionLevel(3)) // Require permission level 3 (admin)
                    .executes(ErrorLogCommand::showErrorCount)
                    .then(CommandManager.literal("gui")
                        .executes(ErrorLogCommand::openServerErrorLogScreen))
                    .then(CommandManager.literal("clear")
                        .executes(ErrorLogCommand::clearErrors))
                    .then(CommandManager.literal("test")
                        .executes(ErrorLogCommand::testErrors))
            );
        });
    }

    /**
     * Opens the error log screen from a server command.
     * This sends a message to the client to open the screen.
     *
     * @param context The command context
     * @return 1 for success
     */
    private static int openServerErrorLogScreen(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();

        // Check if the command is run by a player
        if (source.getPlayer() != null) {
            // Send a client-side command to open the error log screen
            source.getPlayer().sendMessage(Text.literal("Opening error log screen..."));

            // Use the special command packet to tell the client to open the screen
            net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking.send(
                source.getPlayer(),
                ErrorLogNetworkHandler.OPEN_ERROR_LOG,
                new net.minecraft.network.PacketByteBuf(Unpooled.buffer())
            );
        } else {
            // If run from console, just show the error count
            showErrorCount(context);
        }

        return 1;
    }

    /**
     * Shows the current error count.
     *
     * @param context The command context
     * @return 1 for success
     */
    private static int showErrorCount(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();

        // Get error counts by severity
        final int infoCount;
        final int warningCount;
        final int errorCount;
        final int criticalCount;

        // Count errors by severity
        List<ErrorLogger.ErrorEntry> allErrors = ErrorLogger.getInstance().getAllErrors();
        int info = 0;
        int warning = 0;
        int error = 0;
        int critical = 0;

        for (ErrorLogger.ErrorEntry entry : allErrors) {
            switch (entry.getSeverity()) {
                case INFO:
                    info++;
                    break;
                case WARNING:
                    warning++;
                    break;
                case ERROR:
                    error++;
                    break;
                case CRITICAL:
                    critical++;
                    break;
            }
        }

        // Assign to final variables for use in lambdas
        infoCount = info;
        warningCount = warning;
        errorCount = error;
        criticalCount = critical;
        final int totalCount = infoCount + warningCount + errorCount + criticalCount;

        // Send feedback
        source.sendFeedback(() -> Text.literal("Error Log Statistics:"), false);
        source.sendFeedback(() -> Text.literal("  Info: " + infoCount), false);
        source.sendFeedback(() -> Text.literal("  Warning: " + warningCount), false);
        source.sendFeedback(() -> Text.literal("  Error: " + errorCount), false);
        source.sendFeedback(() -> Text.literal("  Critical: " + criticalCount), false);
        source.sendFeedback(() -> Text.literal("  Total: " + totalCount), false);

        return 1;
    }

    /**
     * Clears all errors.
     *
     * @param context The command context
     * @return 1 for success
     */
    private static int clearErrors(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();

        ErrorLogger.getInstance().clearErrors();

        source.sendFeedback(() -> Text.literal("Error log cleared"), true);

        return 1;
    }

    /**
     * Generates realistic sample errors for testing the error log system.
     *
     * @param context The command context
     * @return 1 for success
     */
    private static int testErrors(CommandContext<ServerCommandSource> context) {
        ServerCommandSource source = context.getSource();
        ServerPlayerEntity player = null;

        try {
            player = source.getPlayer();
        } catch (Exception e) {
            // Command was run from console
        }

        // Generate realistic sample errors

        // Info messages
        ErrorLogger.getInstance().logInfo("Town data loaded successfully", "Data");
        ErrorLogger.getInstance().logInfo("Chunk claim system initialized", "Chunks");

        // Warning messages
        ErrorLogger.getInstance().logWarning("Town backup took longer than expected (1532ms)", "Backup");
        ErrorLogger.getInstance().logWarning("Player tried to claim chunk outside of allowed range", "Chunks");

        // Error messages with player info
        if (player != null) {
            // Network error
            ErrorLogger.getInstance().logError(
                "Failed to synchronize town data: Packet too large",
                new IllegalArgumentException("Packet exceeds maximum size of 32767 bytes"),
                "Network",
                ErrorSeverity.ERROR,
                player.getName().getString(),
                player.getUuid()
            );

            // Permission error
            ErrorLogger.getInstance().logError(
                "Permission denied: Player attempted to modify town settings without proper rank",
                new SecurityException("Insufficient permissions for operation"),
                "Permissions",
                ErrorSeverity.WARNING,
                player.getName().getString(),
                player.getUuid()
            );
        } else {
            // Network error without player
            ErrorLogger.getInstance().logError(
                "Failed to synchronize town data: Packet too large",
                new IllegalArgumentException("Packet exceeds maximum size of 32767 bytes"),
                "Network",
                ErrorSeverity.ERROR
            );
        }

        // Critical error
        ErrorLogger.getInstance().logError(
            "Failed to save town data: IO error",
            new java.io.IOException("Could not write to file: town_data.json (Access denied)"),
            "Data",
            ErrorSeverity.CRITICAL
        );

        // Database error
        ErrorLogger.getInstance().logError(
            "Database integrity check failed: Missing required town fields",
            new IllegalStateException("Required field 'permissions' is missing from town data"),
            "Database",
            ErrorSeverity.ERROR
        );

        source.sendFeedback(() -> Text.literal("Sample errors generated for testing"), true);

        return 1;
    }
}
