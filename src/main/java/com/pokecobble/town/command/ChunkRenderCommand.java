package com.pokecobble.town.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.context.CommandContext;
import com.pokecobble.town.claim.ClaimTool;
import com.pokecobble.town.client.ChunkRenderTest;
import net.fabricmc.fabric.api.client.command.v2.ClientCommandManager;
import net.fabricmc.fabric.api.client.command.v2.FabricClientCommandSource;
import net.minecraft.text.Text;

/**
 * Command for testing chunk rendering.
 */
public class ChunkRenderCommand {
    /**
     * Registers the command.
     */
    public static void register() {
        CommandDispatcher<FabricClientCommandSource> dispatcher = ClientCommandManager.getActiveDispatcher();
        if (dispatcher == null) {
            return;
        }

        dispatcher.register(
            ClientCommandManager.literal("chunkrender")
                .executes(ChunkRenderCommand::toggleChunkRendering)
        );

        dispatcher.register(
            ClientCommandManager.literal("chunkrender")
                .then(ClientCommandManager.literal("on")
                    .executes(ChunkRenderCommand::enableChunkRendering))
        );

        dispatcher.register(
            ClientCommandManager.literal("chunkrender")
                .then(ClientCommandManager.literal("off")
                    .executes(ChunkRenderCommand::disableChunkRendering))
        );

        dispatcher.register(
            ClientCommandManager.literal("chunkrender")
                .then(ClientCommandManager.literal("test")
                    .executes(ChunkRenderCommand::testChunkRendering))
        );
    }

    /**
     * Toggles chunk rendering.
     */
    private static int toggleChunkRendering(CommandContext<FabricClientCommandSource> context) {
        ClaimTool claimTool = ClaimTool.getInstance();
        claimTool.toggleChunkVisibility();
        
        boolean isVisible = claimTool.areChunksVisible();
        context.getSource().sendFeedback(Text.literal("Chunk rendering " + (isVisible ? "enabled" : "disabled")));
        
        return 1;
    }

    /**
     * Enables chunk rendering.
     */
    private static int enableChunkRendering(CommandContext<FabricClientCommandSource> context) {
        ClaimTool claimTool = ClaimTool.getInstance();
        if (!claimTool.areChunksVisible()) {
            claimTool.toggleChunkVisibility();
        }
        
        context.getSource().sendFeedback(Text.literal("Chunk rendering enabled"));
        
        return 1;
    }

    /**
     * Disables chunk rendering.
     */
    private static int disableChunkRendering(CommandContext<FabricClientCommandSource> context) {
        ClaimTool claimTool = ClaimTool.getInstance();
        if (claimTool.areChunksVisible()) {
            claimTool.toggleChunkVisibility();
        }
        
        context.getSource().sendFeedback(Text.literal("Chunk rendering disabled"));
        
        return 1;
    }

    /**
     * Tests chunk rendering.
     */
    private static int testChunkRendering(CommandContext<FabricClientCommandSource> context) {
        ChunkRenderTest.testChunkRendering();
        
        context.getSource().sendFeedback(Text.literal("Chunk rendering test executed"));
        
        return 1;
    }
}
