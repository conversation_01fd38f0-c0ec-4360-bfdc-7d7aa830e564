package com.pokecobble.town.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.mojang.brigadier.exceptions.CommandSyntaxException;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.election.Election;
import com.pokecobble.town.election.ElectionManager;
import com.pokecobble.town.network.town.TownDataSynchronizer;
import net.minecraft.command.CommandRegistryAccess;
import net.minecraft.command.argument.EntityArgumentType;
import net.minecraft.server.command.CommandManager;
import net.minecraft.server.command.ServerCommandSource;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import java.util.List;
import java.util.UUID;

/**
 * Command for managing town elections.
 */
public class TownElectionCommand {

    /**
     * Registers the town election command.
     *
     * @param dispatcher The command dispatcher
     * @param registryAccess The command registry access
     * @param environment The command registration environment
     */
    public static void register(CommandDispatcher<ServerCommandSource> dispatcher, CommandRegistryAccess registryAccess, CommandManager.RegistrationEnvironment environment) {
        // Register the town election command
        dispatcher.register(CommandManager.literal("townelection")
            .requires(source -> source.hasPermissionLevel(2)) // Require operator permission level
            .then(CommandManager.literal("start")
                .then(CommandManager.argument("townName", StringArgumentType.greedyString())
                    .suggests((context, builder) -> {
                        // Suggest town names
                        for (Town town : TownManager.getInstance().getAllTowns()) {
                            builder.suggest(town.getName());
                        }
                        return builder.buildFuture();
                    })
                    .executes(context -> startElection(context, StringArgumentType.getString(context, "townName")))
                )
            )
            .then(CommandManager.literal("stop")
                .then(CommandManager.argument("townName", StringArgumentType.greedyString())
                    .suggests((context, builder) -> {
                        // Suggest town names
                        for (Town town : TownManager.getInstance().getAllTowns()) {
                            builder.suggest(town.getName());
                        }
                        return builder.buildFuture();
                    })
                    .executes(context -> stopElection(context, StringArgumentType.getString(context, "townName")))
                )
            )
        );
    }

    /**
     * Starts an election in a town.
     *
     * @param context The command context
     * @param townName The name of the town
     * @return 1 if successful, 0 otherwise
     */
    private static int startElection(CommandContext<ServerCommandSource> context, String townName) {
        ServerCommandSource source = context.getSource();

        // Find the town
        Town town = TownManager.getInstance().getTownByName(townName);
        if (town == null) {
            source.sendError(Text.literal("Town not found: " + townName).formatted(Formatting.RED));
            return 0;
        }

        // Check if an election is already in progress
        Election existingElection = ElectionManager.getInstance().getElection(town);
        if (existingElection != null && !existingElection.isCompleted()) {
            source.sendError(Text.literal("An election is already in progress in " + townName).formatted(Formatting.RED));
            return 0;
        }

        // Find the current mayor
        UUID mayorId = null;
        for (UUID playerId : town.getPlayers()) {
            if (town.getPlayerRank(playerId) == TownPlayerRank.OWNER) {
                mayorId = playerId;
                break;
            }
        }

        // Demote the mayor if found
        if (mayorId != null) {
            // Set the mayor to a regular member
            town.setPlayerRank(mayorId, TownPlayerRank.MEMBER);

            // Notify the mayor if they're online
            ServerPlayerEntity mayor = source.getServer().getPlayerManager().getPlayer(mayorId);
            if (mayor != null) {
                mayor.sendMessage(Text.literal("An election has been started in your town. You have been temporarily demoted.").formatted(Formatting.GOLD));
            }
        }

        // Start the election
        Election election = ElectionManager.getInstance().startElection(town);
        if (election == null) {
            source.sendError(Text.literal("Failed to start election in " + townName).formatted(Formatting.RED));
            return 0;
        }

        // Sync the town data to all clients
        syncTownDataToClients(source, town);

        // Notify all online players in the town
        for (UUID playerId : town.getPlayers()) {
            ServerPlayerEntity player = source.getServer().getPlayerManager().getPlayer(playerId);
            if (player != null) {
                player.sendMessage(Text.literal("An election has started in " + townName + "!").formatted(Formatting.GOLD));
                player.sendMessage(Text.literal("Use the Town interface to cast your vote for the new mayor.").formatted(Formatting.GOLD));
            }
        }

        // Notify the command sender
        source.sendFeedback(() -> Text.literal("Started election in " + townName + ". Players can vote using the town interface.").formatted(Formatting.GREEN), true);

        return 1;
    }

    /**
     * Stops an election in a town and immediately declares a winner.
     *
     * @param context The command context
     * @param townName The name of the town
     * @return 1 if successful, 0 otherwise
     */
    private static int stopElection(CommandContext<ServerCommandSource> context, String townName) {
        ServerCommandSource source = context.getSource();

        // Find the town
        Town town = TownManager.getInstance().getTownByName(townName);
        if (town == null) {
            source.sendError(Text.literal("Town not found: " + townName).formatted(Formatting.RED));
            return 0;
        }

        // Check if an election is in progress
        Election election = ElectionManager.getInstance().getElection(town);
        if (election == null || election.isCompleted()) {
            source.sendError(Text.literal("No election is in progress in " + townName).formatted(Formatting.RED));
            return 0;
        }

        // Get the vote counts
        List<UUID> sortedCandidates = election.getSortedCandidates();

        // Find the winner
        UUID winnerId = null;

        // First, check if anyone has votes
        if (!sortedCandidates.isEmpty() && election.getVoteCount().getOrDefault(sortedCandidates.get(0), 0) > 0) {
            // The candidate with the most votes wins
            winnerId = sortedCandidates.get(0);
        } else {
            // No votes cast, find a fallback winner

            // First try: any online player from the town
            for (UUID playerId : town.getPlayers()) {
                ServerPlayerEntity player = source.getServer().getPlayerManager().getPlayer(playerId);
                if (player != null) {
                    winnerId = playerId;
                    break;
                }
            }

            // Second try: use the last seen player (for this example, we'll just use the first player in the list)
            // In a real implementation, you would check the last seen timestamp
            if (winnerId == null && !town.getPlayers().isEmpty()) {
                winnerId = town.getPlayers().get(0);
            }
        }

        // If we found a winner, set them as the mayor
        if (winnerId != null) {
            // Get the current mayor (if any) and demote them
            for (UUID playerId : town.getPlayers()) {
                if (town.getPlayerRank(playerId) == TownPlayerRank.OWNER) {
                    // Demote the current mayor to member
                    town.setPlayerRank(playerId, TownPlayerRank.MEMBER);

                    // Notify them if they're online
                    ServerPlayerEntity formerMayor = source.getServer().getPlayerManager().getPlayer(playerId);
                    if (formerMayor != null) {
                        formerMayor.sendMessage(Text.literal("You are no longer the mayor of " + townName + ".").formatted(Formatting.GOLD));
                    }
                    break;
                }
            }

            // Make sure the winner is in the town's player list
            if (!town.getPlayers().contains(winnerId)) {
                // This should never happen, but just in case
                source.sendError(Text.literal("Winner is not in the town! This is a bug.").formatted(Formatting.RED));
                return 0;
            }

            // Set the winner as the mayor
            boolean success = town.setPlayerRank(winnerId, TownPlayerRank.OWNER);

            if (!success) {
                source.sendError(Text.literal("Failed to set the winner as mayor. This is a bug.").formatted(Formatting.RED));
                return 0;
            }

            // Sync the town data to all clients
            syncTownDataToClients(source, town);

            // Get the winner's name
            String winnerName = "Unknown"; // Default value
            ServerPlayerEntity winner = source.getServer().getPlayerManager().getPlayer(winnerId);

            if (winner != null) {
                // Player is online
                winnerName = winner.getName().getString();

                // Notify the winner
                winner.sendMessage(Text.literal("Congratulations! You have been elected as the mayor of " + townName + "!").formatted(Formatting.GREEN));
            } else {
                // Player is offline, try to get the name from the server's user cache
                try {
                    var userProfile = source.getServer().getUserCache().getByUuid(winnerId);
                    if (userProfile.isPresent()) {
                        winnerName = userProfile.get().getName();
                    }
                } catch (Exception e) {
                    // Keep the default "Unknown" value
                }
            }

            // Create a final copy of the winner name for use in lambdas
            final String finalWinnerName = winnerName;

            // Notify all online players in the town
            for (UUID playerId : town.getPlayers()) {
                if (!playerId.equals(winnerId)) { // Skip the winner, they already got a message
                    ServerPlayerEntity player = source.getServer().getPlayerManager().getPlayer(playerId);
                    if (player != null) {
                        player.sendMessage(Text.literal("The election in " + townName + " has ended. " + finalWinnerName + " is the new mayor!").formatted(Formatting.GOLD));
                    }
                }
            }

            // Notify the command sender
            source.sendFeedback(() -> Text.literal("Stopped election in " + townName + ". " + finalWinnerName + " is the new mayor!").formatted(Formatting.GREEN), true);
        } else {
            // This should never happen, but just in case
            source.sendError(Text.literal("Failed to find a suitable mayor for " + townName).formatted(Formatting.RED));
            return 0;
        }

        // Mark the election as completed
        election.completeElection();

        return 1;
    }

    /**
     * Syncs town data to all clients.
     *
     * @param source The command source
     * @param town The town to sync
     */
    private static void syncTownDataToClients(ServerCommandSource source, Town town) {
        // Use the TownDataSynchronizer to sync town data
        TownDataSynchronizer.syncTownData(source.getServer(), town);

        // Notify all online players in the town
        for (UUID playerId : town.getPlayers()) {
            ServerPlayerEntity player = source.getServer().getPlayerManager().getPlayer(playerId);
            if (player != null) {
                player.sendMessage(Text.literal("Town data has been updated.").formatted(Formatting.GRAY), false);
            }
        }
    }
}
