package com.pokecobble.town.command;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.TownPlayerRank;
import com.pokecobble.town.network.NetworkConstants;
import com.pokecobble.town.network.town.TownDataSynchronizer;
import net.fabricmc.fabric.api.networking.v1.PacketByteBufs;
import net.fabricmc.fabric.api.networking.v1.ServerPlayNetworking;
import net.minecraft.network.PacketByteBuf;
import net.minecraft.command.CommandRegistryAccess;
import net.minecraft.server.command.CommandManager;
import net.minecraft.server.command.ServerCommandSource;
import net.minecraft.server.network.ServerPlayerEntity;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Handles administrative commands for towns.
 */
public class TownAdminCommand {

    /**
     * Registers the town admin commands.
     *
     * @param dispatcher The command dispatcher
     * @param registryAccess The command registry access
     * @param environment The command registration environment
     */
    public static void register(CommandDispatcher<ServerCommandSource> dispatcher, CommandRegistryAccess registryAccess, CommandManager.RegistrationEnvironment environment) {
        Pokecobbleclaim.LOGGER.info("Registering town admin commands");

        // Register /townadmin forcemayor command
        dispatcher.register(CommandManager.literal("townadmin")
            .requires(source -> source.hasPermissionLevel(2)) // Require operator permission level
            .then(CommandManager.literal("forcemayor")
                .then(CommandManager.argument("townName", StringArgumentType.string())
                    .suggests((context, builder) -> {
                        // Suggest town names
                        for (Town town : TownManager.getInstance().getAllTowns()) {
                            builder.suggest(town.getName());
                        }
                        return builder.buildFuture();
                    })
                    .then(CommandManager.argument("playerName", StringArgumentType.greedyString())
                        .suggests((context, builder) -> {
                            // Get the town name from the command
                            String townName = StringArgumentType.getString(context, "townName");
                            Town town = TownManager.getInstance().getTownByName(townName);

                            if (town != null) {
                                // Suggest player names from the town
                                for (UUID playerId : town.getPlayers()) {
                                    ServerPlayerEntity player = context.getSource().getServer().getPlayerManager().getPlayer(playerId);
                                    if (player != null) {
                                        builder.suggest(player.getName().getString());
                                    }
                                }
                            }

                            return builder.buildFuture();
                        })
                        .executes(context -> forceMayor(
                            context,
                            StringArgumentType.getString(context, "townName"),
                            StringArgumentType.getString(context, "playerName")
                        ))
                    )
                )
            )
            .then(CommandManager.literal("allperm")
                .then(CommandManager.argument("value", StringArgumentType.word())
                    .suggests((context, builder) -> {
                        builder.suggest("true");
                        builder.suggest("false");
                        return builder.buildFuture();
                    })
                    .then(CommandManager.argument("playerName", StringArgumentType.greedyString())
                        .suggests((context, builder) -> {
                            // Suggest all online players
                            for (ServerPlayerEntity player : context.getSource().getServer().getPlayerManager().getPlayerList()) {
                                builder.suggest(player.getName().getString());
                            }
                            return builder.buildFuture();
                        })
                        .executes(context -> setAllPermissions(
                            context,
                            StringArgumentType.getString(context, "value").equalsIgnoreCase("true"),
                            StringArgumentType.getString(context, "playerName")
                        ))
                    )
                )
            )
            .then(CommandManager.literal("manageplayer")
                .then(CommandManager.argument("value", StringArgumentType.word())
                    .suggests((context, builder) -> {
                        builder.suggest("true");
                        builder.suggest("false");
                        return builder.buildFuture();
                    })
                    .then(CommandManager.argument("playerName", StringArgumentType.greedyString())
                        .suggests((context, builder) -> {
                            // Suggest all online players
                            for (ServerPlayerEntity player : context.getSource().getServer().getPlayerManager().getPlayerList()) {
                                builder.suggest(player.getName().getString());
                            }
                            return builder.buildFuture();
                        })
                        .executes(context -> setPlayerManagementPermissions(
                            context,
                            StringArgumentType.getString(context, "value").equalsIgnoreCase("true"),
                            StringArgumentType.getString(context, "playerName")
                        ))
                    )
                )
            )
        );

        Pokecobbleclaim.LOGGER.info("Town admin commands registered successfully");
    }

    /**
     * Forces a player to become the mayor of a town.
     *
     * @param context The command context
     * @param townName The name of the town
     * @param playerName The name of the player to make mayor
     * @return 1 if successful, 0 otherwise
     */
    private static int forceMayor(CommandContext<ServerCommandSource> context, String townName, String playerName) {
        ServerCommandSource source = context.getSource();

        // Find the town
        Town town = TownManager.getInstance().getTownByName(townName);
        if (town == null) {
            source.sendError(Text.literal("Town not found: " + townName).formatted(Formatting.RED));
            return 0;
        }

        // Find the player
        ServerPlayerEntity targetPlayer = null;
        UUID targetPlayerId = null;

        // First try to find the player by exact name
        for (ServerPlayerEntity player : source.getServer().getPlayerManager().getPlayerList()) {
            if (player.getName().getString().equalsIgnoreCase(playerName)) {
                targetPlayer = player;
                targetPlayerId = player.getUuid();
                break;
            }
        }

        // If player not found by name, check if they're in the town
        if (targetPlayerId == null) {
            for (UUID playerId : town.getPlayers()) {
                ServerPlayerEntity player = source.getServer().getPlayerManager().getPlayer(playerId);
                if (player != null && player.getName().getString().equalsIgnoreCase(playerName)) {
                    targetPlayer = player;
                    targetPlayerId = playerId;
                    break;
                }
            }
        }

        // If still not found, check if the player is offline but in the town
        if (targetPlayerId == null) {
            for (UUID playerId : town.getPlayers()) {
                TownPlayer townPlayer = town.getPlayer(playerId);
                if (townPlayer != null && townPlayer.getName().equalsIgnoreCase(playerName)) {
                    targetPlayerId = playerId;
                    break;
                }
            }
        }

        // If player not found at all
        if (targetPlayerId == null) {
            source.sendError(Text.literal("Player not found: " + playerName).formatted(Formatting.RED));
            return 0;
        }

        // Check if the player is in the town
        if (!town.getPlayers().contains(targetPlayerId)) {
            // Add the player to the town if they're not already in it
            if (targetPlayer != null) {
                boolean added = TownManager.getInstance().addPlayerToTown(targetPlayerId, town.getId());
                if (!added) {
                    source.sendError(Text.literal("Failed to add player to town. They might already be in another town.").formatted(Formatting.RED));
                    return 0;
                }
            } else {
                source.sendError(Text.literal("Player is not in the town and is offline.").formatted(Formatting.RED));
                return 0;
            }
        }

        // Find the current mayor (if any) and demote them
        for (UUID playerId : town.getPlayers()) {
            if (town.getPlayerRank(playerId) == TownPlayerRank.OWNER && !playerId.equals(targetPlayerId)) {
                // Get the player object
                TownPlayer formerMayorPlayer = town.getPlayer(playerId);

                // Demote the current mayor to member
                town.setPlayerRank(playerId, TownPlayerRank.MEMBER);

                // Reset their permissions to default member permissions
                if (formerMayorPlayer != null) {
                    // Create a new TownPlayer with MEMBER rank to get default permissions
                    TownPlayer tempPlayer = new TownPlayer(playerId, formerMayorPlayer.getName(), TownPlayerRank.MEMBER);

                    // Copy all default permissions to the former mayor
                    Map<String, Map<String, Boolean>> defaultPermissions = tempPlayer.getAllPermissions();
                    for (Map.Entry<String, Map<String, Boolean>> entry : defaultPermissions.entrySet()) {
                        formerMayorPlayer.setCategoryPermissions(entry.getKey(), entry.getValue());
                    }
                }

                // Notify them if they're online
                ServerPlayerEntity formerMayor = source.getServer().getPlayerManager().getPlayer(playerId);
                if (formerMayor != null) {
                    formerMayor.sendMessage(Text.literal("You are no longer the mayor of " + townName + ".").formatted(Formatting.GOLD));
                    formerMayor.sendMessage(Text.literal("Your permissions have been reset to default member permissions.").formatted(Formatting.GOLD));
                }
                break;
            }
        }

        // Get the target player object
        TownPlayer targetTownPlayer = town.getPlayer(targetPlayerId);

        // Set the target player as the mayor
        boolean success = town.setPlayerRank(targetPlayerId, TownPlayerRank.OWNER);

        if (!success) {
            source.sendError(Text.literal("Failed to set player as mayor. This is a bug.").formatted(Formatting.RED));
            return 0;
        }

        // Update the target player's permissions to have full access
        if (targetTownPlayer != null) {
            // Create a new TownPlayer with OWNER rank to get default owner permissions
            TownPlayer tempPlayer = new TownPlayer(targetPlayerId, targetTownPlayer.getName(), TownPlayerRank.OWNER);

            // Copy all owner permissions to the new mayor
            Map<String, Map<String, Boolean>> ownerPermissions = tempPlayer.getAllPermissions();
            for (Map.Entry<String, Map<String, Boolean>> entry : ownerPermissions.entrySet()) {
                targetTownPlayer.setCategoryPermissions(entry.getKey(), entry.getValue());
            }
        }

        // Save the town
        TownManager.getInstance().saveTown(town);

        // Sync the town data to all clients
        syncTownDataToClients(source, town);

        // Notify the target player if they're online
        if (targetPlayer != null) {
            targetPlayer.sendMessage(Text.literal("You are now the mayor of " + townName + "!").formatted(Formatting.GREEN));
            targetPlayer.sendMessage(Text.literal("You have been granted full permissions for the town.").formatted(Formatting.GREEN));
        }

        // Notify all online players in the town
        for (UUID playerId : town.getPlayers()) {
            if (!playerId.equals(targetPlayerId)) { // Skip the new mayor, they already got a message
                ServerPlayerEntity player = source.getServer().getPlayerManager().getPlayer(playerId);
                if (player != null) {
                    String newMayorName = targetPlayer != null ? targetPlayer.getName().getString() : playerName;
                    player.sendMessage(Text.literal(newMayorName + " is now the mayor of " + townName + ".").formatted(Formatting.GOLD));
                }
            }
        }

        // Notify the command sender
        String targetPlayerName = targetPlayer != null ? targetPlayer.getName().getString() : playerName;
        source.sendFeedback(() -> Text.literal("Set " + targetPlayerName + " as the mayor of " + townName + ".").formatted(Formatting.GREEN), true);

        return 1;
    }

    /**
     * Sets all permissions for a player to either true or false.
     *
     * @param context The command context
     * @param value Whether to enable (true) or disable (false) all permissions
     * @param playerName The name of the player to update
     * @return 1 if successful, 0 otherwise
     */
    private static int setAllPermissions(CommandContext<ServerCommandSource> context, boolean value, String playerName) {
        ServerCommandSource source = context.getSource();

        // Find the player
        ServerPlayerEntity targetPlayer = null;
        UUID targetPlayerId = null;

        // First try to find the player by exact name
        for (ServerPlayerEntity player : source.getServer().getPlayerManager().getPlayerList()) {
            if (player.getName().getString().equalsIgnoreCase(playerName)) {
                targetPlayer = player;
                targetPlayerId = player.getUuid();
                break;
            }
        }

        // If player not found, check offline players
        if (targetPlayerId == null) {
            // This is a simplified approach - in a real implementation, you would have a way to look up offline players
            source.sendError(Text.literal("Player not found: " + playerName).formatted(Formatting.RED));
            return 0;
        }

        // Find the town the player is in
        Town town = TownManager.getInstance().getPlayerTown(targetPlayerId);
        if (town == null) {
            source.sendError(Text.literal("Player is not in a town: " + playerName).formatted(Formatting.RED));
            return 0;
        }

        // Get the player's town player object
        TownPlayer townPlayer = town.getPlayer(targetPlayerId);
        if (townPlayer == null) {
            source.sendError(Text.literal("Player not found in town: " + playerName).formatted(Formatting.RED));
            return 0;
        }

        // Set all permissions
        Map<String, Map<String, Boolean>> allPermissions = townPlayer.getAllPermissions();
        boolean changesApplied = false;

        // Update each permission category
        for (Map.Entry<String, Map<String, Boolean>> categoryEntry : allPermissions.entrySet()) {
            String categoryName = categoryEntry.getKey();
            Map<String, Boolean> permissions = categoryEntry.getValue();
            Map<String, Boolean> updatedPermissions = new HashMap<>(permissions);
            boolean categoryChanged = false;

            // Update each permission in the category
            for (String permissionName : permissions.keySet()) {
                if (permissions.get(permissionName) != value) {
                    updatedPermissions.put(permissionName, value);
                    categoryChanged = true;
                }
            }

            // If permissions changed, update them
            if (categoryChanged) {
                townPlayer.setCategoryPermissions(categoryName, updatedPermissions);
                changesApplied = true;

                // Send update to the player if they're online
                if (targetPlayer != null) {
                    // Create a packet to update the client
                    PacketByteBuf buf = PacketByteBufs.create();
                    buf.writeUuid(targetPlayerId);
                    buf.writeString(categoryName);
                    buf.writeInt(updatedPermissions.size());

                    for (Map.Entry<String, Boolean> entry : updatedPermissions.entrySet()) {
                        buf.writeString(entry.getKey());
                        buf.writeBoolean(entry.getValue());
                    }

                    // Send to the player
                    ServerPlayNetworking.send(targetPlayer, NetworkConstants.PLAYER_PERMISSIONS_UPDATE, buf);
                }
            }
        }

        // Save the town
        if (changesApplied) {
            TownManager.getInstance().saveTown(town);

            // Notify the player
            if (targetPlayer != null) {
                targetPlayer.sendMessage(Text.literal("All your permissions have been " +
                    (value ? "enabled" : "disabled") + " by an admin.").formatted(Formatting.GOLD), false);
            }

            // Notify the command sender
            source.sendFeedback(() -> Text.literal("All permissions for " + playerName + " have been " +
                (value ? "enabled" : "disabled") + ".").formatted(Formatting.GREEN), true);

            return 1;
        } else {
            // No changes were needed
            source.sendFeedback(() -> Text.literal("No permission changes were needed for " + playerName + ".").formatted(Formatting.YELLOW), true);
            return 0;
        }
    }

    /**
     * Sets all player management permissions for a player to either true or false.
     *
     * @param context The command context
     * @param value Whether to enable (true) or disable (false) all player management permissions
     * @param playerName The name of the player to update
     * @return 1 if successful, 0 otherwise
     */
    private static int setPlayerManagementPermissions(CommandContext<ServerCommandSource> context, boolean value, String playerName) {
        ServerCommandSource source = context.getSource();

        // Find the player
        ServerPlayerEntity targetPlayer = null;
        UUID targetPlayerId = null;

        // First try to find the player by exact name
        for (ServerPlayerEntity player : source.getServer().getPlayerManager().getPlayerList()) {
            if (player.getName().getString().equalsIgnoreCase(playerName)) {
                targetPlayer = player;
                targetPlayerId = player.getUuid();
                break;
            }
        }

        // If player not found, check offline players
        if (targetPlayerId == null) {
            // This is a simplified approach - in a real implementation, you would have a way to look up offline players
            source.sendError(Text.literal("Player not found: " + playerName).formatted(Formatting.RED));
            return 0;
        }

        // Find the town the player is in
        Town town = TownManager.getInstance().getPlayerTown(targetPlayerId);
        if (town == null) {
            source.sendError(Text.literal("Player is not in a town: " + playerName).formatted(Formatting.RED));
            return 0;
        }

        // Get the player's town player object
        TownPlayer townPlayer = town.getPlayer(targetPlayerId);
        if (townPlayer == null) {
            source.sendError(Text.literal("Player not found in town: " + playerName).formatted(Formatting.RED));
            return 0;
        }

        // Set player management permissions
        Map<String, Boolean> playerManagementPermissions = townPlayer.getCategoryPermissions("Player Management");
        if (playerManagementPermissions == null) {
            playerManagementPermissions = new HashMap<>();
        } else {
            // Create a copy to avoid modifying the original map
            playerManagementPermissions = new HashMap<>(playerManagementPermissions);
        }

        // Update all player management permissions
        boolean changesApplied = false;

        // Player Management category permissions
        String[] playerManagementPerms = {
            "Can kick players",
            "Can change player ranks",
            "Can manage player permissions"
        };

        // Set all Player Management permissions
        for (String permission : playerManagementPerms) {
            playerManagementPermissions.put(permission, value);
            changesApplied = true;
        }

        // Also update Town Settings permissions to allow saving changes
        Map<String, Boolean> townSettingsPermissions = townPlayer.getCategoryPermissions("Town Settings");
        if (townSettingsPermissions == null) {
            townSettingsPermissions = new HashMap<>();
        } else {
            // Create a copy to avoid modifying the original map
            townSettingsPermissions = new HashMap<>(townSettingsPermissions);
        }

        // Add permissions for settings
        townSettingsPermissions.put("Can view settings", value); // This is needed to access the interface
        townSettingsPermissions.put("Can modify settings", value); // This is needed to save changes
        townPlayer.setCategoryPermissions("Town Settings", townSettingsPermissions);
        changesApplied = true;

        // If permissions changed, update them
        if (changesApplied) {
            // Apply Player Management permissions
            townPlayer.setCategoryPermissions("Player Management", playerManagementPermissions);

            // Save the town
            TownManager.getInstance().saveTown(town);

            // Get the current data version after all updates
            int currentVersion = townPlayer.getDataVersion();

            // Send updates to all online players in the town who need to know about this change
            for (UUID playerId : town.getPlayers()) {
                ServerPlayerEntity player = source.getServer().getPlayerManager().getPlayer(playerId);
                if (player != null) {
                    // Only send to the target player and the command executor
                    if (player.getUuid().equals(targetPlayerId) || player.equals(source.getPlayer())) {
                        // Send Player Management permissions update
                        PacketByteBuf playerManagementBuf = PacketByteBufs.create();
                        playerManagementBuf.writeUuid(targetPlayerId);
                        playerManagementBuf.writeString("Player Management");
                        playerManagementBuf.writeInt(playerManagementPermissions.size());
                        playerManagementBuf.writeInt(currentVersion); // Include version

                        for (Map.Entry<String, Boolean> entry : playerManagementPermissions.entrySet()) {
                            playerManagementBuf.writeString(entry.getKey());
                            playerManagementBuf.writeBoolean(entry.getValue());
                        }

                        // Send to the player
                        ServerPlayNetworking.send(player, NetworkConstants.PLAYER_DATA_RESPONSE, playerManagementBuf);

                        // Send Town Settings permissions update
                        PacketByteBuf townSettingsBuf = PacketByteBufs.create();
                        townSettingsBuf.writeUuid(targetPlayerId);
                        townSettingsBuf.writeString("Town Settings");
                        townSettingsBuf.writeInt(townSettingsPermissions.size());
                        townSettingsBuf.writeInt(currentVersion); // Include version

                        for (Map.Entry<String, Boolean> entry : townSettingsPermissions.entrySet()) {
                            townSettingsBuf.writeString(entry.getKey());
                            townSettingsBuf.writeBoolean(entry.getValue());
                        }

                        // Send to the player
                        ServerPlayNetworking.send(player, NetworkConstants.PLAYER_DATA_RESPONSE, townSettingsBuf);
                    }
                }
            }

            // Notify the target player
            if (targetPlayer != null) {
                targetPlayer.sendMessage(Text.literal("Your ability to manage players has been " +
                    (value ? "enabled" : "disabled") + " by an admin.").formatted(Formatting.GOLD), false);
            }

            // Notify the command sender
            source.sendFeedback(() -> Text.literal("Player management ability for " + playerName + " has been " +
                (value ? "enabled" : "disabled") + ".").formatted(Formatting.GREEN), true);

            return 1;
        } else {
            // No changes were needed
            source.sendFeedback(() -> Text.literal("No permission changes were needed for " + playerName + ".").formatted(Formatting.YELLOW), true);
            return 0;
        }
    }

    /**
     * Syncs town data to all clients.
     *
     * @param source The command source
     * @param town The town to sync
     */
    private static void syncTownDataToClients(ServerCommandSource source, Town town) {
        // Use the TownDataSynchronizer to sync town data
        TownDataSynchronizer.syncTownData(source.getServer(), town);

        // Notify all online players in the town
        for (UUID playerId : town.getPlayers()) {
            ServerPlayerEntity player = source.getServer().getPlayerManager().getPlayer(playerId);
            if (player != null) {
                player.sendMessage(Text.literal("Town data has been updated.").formatted(Formatting.GRAY), false);
            }
        }
    }
}
