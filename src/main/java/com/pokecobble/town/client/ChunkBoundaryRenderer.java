package com.pokecobble.town.client;

import com.mojang.blaze3d.systems.RenderSystem;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.claim.ClaimTool;
import net.fabricmc.fabric.api.client.rendering.v1.WorldRenderContext;
import net.fabricmc.fabric.api.client.rendering.v1.WorldRenderEvents;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.render.BufferBuilder;
import net.minecraft.client.render.GameRenderer;
import net.minecraft.client.render.Tessellator;
import net.minecraft.client.render.VertexFormat;
import net.minecraft.client.render.VertexFormats;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.ChunkPos;
import net.minecraft.util.math.Vec3d;
import org.joml.Matrix4f;

/**
 * Renders chunk boundaries in the world.
 * This class is responsible for rendering the chunk boundaries when the claim tool is active.
 */
public class ChunkBoundaryRenderer {
    private static final MinecraftClient client = MinecraftClient.getInstance();
    private static final int RENDER_DISTANCE = 5; // Number of chunks in each direction to render
    private static final float LINE_WIDTH = 2.0f; // Width of the lines
    private static final int CORNER_HEIGHT = 64; // Height of the corner lines in blocks (increased from 16 to 64)

    // Colors
    private static final int COLOR_NORMAL = 0x44FFFFFF; // White with transparency
    private static final int COLOR_SELECTED = 0x4400FF00; // Green with transparency
    private static final int COLOR_CLAIMED = 0x44FF0000; // Red with transparency

    /**
     * Registers the chunk boundary renderer.
     */
    public static void register() {
        Pokecobbleclaim.LOGGER.info("Registering chunk boundary renderer");

        // Register the world render event
        WorldRenderEvents.AFTER_TRANSLUCENT.register(ChunkBoundaryRenderer::renderChunkBoundaries);
    }

    /**
     * Renders chunk boundaries in the world.
     *
     * @param context The world render context
     */
    private static void renderChunkBoundaries(WorldRenderContext context) {
        // Get the claim tool
        ClaimTool claimTool = ClaimTool.getInstance();

        // Check if the claim tool is active and chunks should be visible
        if (!claimTool.isActive() || !claimTool.areChunksVisible()) {
            return;
        }

        // Get the player position
        if (client.player == null) {
            return;
        }

        // Get the player's current chunk
        ChunkPos playerChunk = new ChunkPos(client.player.getBlockPos());

        // Get the camera position for rendering
        Vec3d cameraPos = context.camera().getPos();

        // Setup rendering
        MatrixStack matrices = context.matrixStack();
        matrices.push();

        // Translate to camera position
        matrices.translate(-cameraPos.x, -cameraPos.y, -cameraPos.z);

        // Setup render state
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.disableCull();
        RenderSystem.setShader(GameRenderer::getPositionColorProgram);
        RenderSystem.lineWidth(LINE_WIDTH);

        // Get the tessellator and buffer builder
        Tessellator tessellator = Tessellator.getInstance();
        BufferBuilder buffer = tessellator.getBuffer();

        // Start drawing lines
        buffer.begin(VertexFormat.DrawMode.DEBUG_LINES, VertexFormats.POSITION_COLOR);

        // Render chunks in a square around the player
        for (int x = playerChunk.x - RENDER_DISTANCE; x <= playerChunk.x + RENDER_DISTANCE; x++) {
            for (int z = playerChunk.z - RENDER_DISTANCE; z <= playerChunk.z + RENDER_DISTANCE; z++) {
                ChunkPos chunkPos = new ChunkPos(x, z);
                renderChunkBoundary(buffer, matrices, chunkPos, claimTool);
            }
        }

        // Draw the lines
        tessellator.draw();

        // Restore render state
        RenderSystem.enableCull();
        RenderSystem.disableBlend();

        // Pop matrix stack
        matrices.pop();
    }

    /**
     * Renders the boundary for a single chunk.
     *
     * @param buffer The buffer builder
     * @param matrices The matrix stack
     * @param chunkPos The chunk position
     * @param claimTool The claim tool
     */
    private static void renderChunkBoundary(BufferBuilder buffer, MatrixStack matrices, ChunkPos chunkPos, ClaimTool claimTool) {
        // Get the tessellator for drawing
        Tessellator tessellator = Tessellator.getInstance();
        // Calculate chunk corner positions
        int minX = chunkPos.getStartX();
        int minZ = chunkPos.getStartZ();
        int maxX = chunkPos.getEndX() + 1;
        int maxZ = chunkPos.getEndZ() + 1;

        // Get player Y position for rendering at the right height
        int playerY = client.player.getBlockY();

        // Determine the color based on chunk status
        int color;
        if (claimTool.isChunkSelected(chunkPos)) {
            // Selected chunk - use the tag color if available
            if (claimTool.getTaggedChunks().containsKey(chunkPos)) {
                color = claimTool.getTaggedChunks().get(chunkPos).getColor() | 0x44000000;
            } else {
                color = COLOR_SELECTED;
            }
        } else if (claimTool.isChunkClaimed(chunkPos)) {
            // Claimed chunk - use red
            color = COLOR_CLAIMED;
        } else if (claimTool.hasReachedClaimLimit()) {
            // Town has reached claim limit - use dark gray to indicate it can't be selected
            color = 0x44444444; // Dark gray with some transparency
        } else {
            // Normal chunk - use white
            color = COLOR_NORMAL;
        }

        // Extract color components
        float red = ((color >> 16) & 0xFF) / 255.0f;
        float green = ((color >> 8) & 0xFF) / 255.0f;
        float blue = (color & 0xFF) / 255.0f;
        float alpha = ((color >> 24) & 0xFF) / 255.0f;

        // Get the matrix for transforming vertices
        Matrix4f matrix = matrices.peek().getPositionMatrix();

        // Draw corner lines (vertical lines at each corner)
        // Northwest corner
        drawLine(buffer, matrix, minX, playerY - 32, minZ, minX, playerY + CORNER_HEIGHT, minZ, red, green, blue, alpha);
        // Northeast corner
        drawLine(buffer, matrix, maxX, playerY - 32, minZ, maxX, playerY + CORNER_HEIGHT, minZ, red, green, blue, alpha);
        // Southwest corner
        drawLine(buffer, matrix, minX, playerY - 32, maxZ, minX, playerY + CORNER_HEIGHT, maxZ, red, green, blue, alpha);
        // Southeast corner
        drawLine(buffer, matrix, maxX, playerY - 32, maxZ, maxX, playerY + CORNER_HEIGHT, maxZ, red, green, blue, alpha);

        // Horizontal lines removed as requested
        int y = playerY;

        // If the chunk is selected, draw semi-transparent walls with the tag color
        if (claimTool.isChunkSelected(chunkPos)) {
            // Get the tag color if available
            float wallRed = red;
            float wallGreen = green;
            float wallBlue = blue;
            float wallAlpha = 0.5f; // More visible but still semi-transparent for walls

            if (claimTool.getTaggedChunks().containsKey(chunkPos)) {
                int tagColor = claimTool.getTaggedChunks().get(chunkPos).getColor();
                wallRed = ((tagColor >> 16) & 0xFF) / 255.0f;
                wallGreen = ((tagColor >> 8) & 0xFF) / 255.0f;
                wallBlue = (tagColor & 0xFF) / 255.0f;
            }

            // Draw semi-transparent walls for selected chunks
            // We'll use a different rendering approach for walls to make them semi-transparent
            // First, end the current line drawing and start a new quad drawing
            tessellator.draw();

            // Setup for quad rendering
            RenderSystem.enableBlend();
            RenderSystem.defaultBlendFunc();
            RenderSystem.disableCull(); // So we can see both sides of the walls
            RenderSystem.setShader(GameRenderer::getPositionColorProgram);

            buffer.begin(VertexFormat.DrawMode.QUADS, VertexFormats.POSITION_COLOR);

            // Draw the walls as quads slightly inside the chunk boundaries to prevent color mixing
            // Use a small offset to move the walls slightly inside the chunk
            float offset = 0.05f;

            // North wall (slightly inside the chunk)
            drawWallQuad(buffer, matrix,
                minX, y - 32, minZ + offset,
                maxX, y + CORNER_HEIGHT, minZ + offset,
                wallRed, wallGreen, wallBlue, wallAlpha);

            // South wall (slightly inside the chunk)
            drawWallQuad(buffer, matrix,
                minX, y - 32, maxZ - offset,
                maxX, y + CORNER_HEIGHT, maxZ - offset,
                wallRed, wallGreen, wallBlue, wallAlpha);

            // West wall (slightly inside the chunk)
            drawWallQuad(buffer, matrix,
                minX + offset, y - 32, minZ,
                minX + offset, y + CORNER_HEIGHT, maxZ,
                wallRed, wallGreen, wallBlue, wallAlpha);

            // East wall (slightly inside the chunk)
            drawWallQuad(buffer, matrix,
                maxX - offset, y - 32, minZ,
                maxX - offset, y + CORNER_HEIGHT, maxZ,
                wallRed, wallGreen, wallBlue, wallAlpha);

            // Draw the walls
            tessellator.draw();

            // Restore line drawing
            buffer.begin(VertexFormat.DrawMode.DEBUG_LINES, VertexFormats.POSITION_COLOR);

            // Draw the outline of the walls with solid lines
            // Top edges
            drawLine(buffer, matrix, minX, y + CORNER_HEIGHT, minZ, maxX, y + CORNER_HEIGHT, minZ, wallRed, wallGreen, wallBlue, 0.5f);
            drawLine(buffer, matrix, minX, y + CORNER_HEIGHT, maxZ, maxX, y + CORNER_HEIGHT, maxZ, wallRed, wallGreen, wallBlue, 0.5f);
            drawLine(buffer, matrix, minX, y + CORNER_HEIGHT, minZ, minX, y + CORNER_HEIGHT, maxZ, wallRed, wallGreen, wallBlue, 0.5f);
            drawLine(buffer, matrix, maxX, y + CORNER_HEIGHT, minZ, maxX, y + CORNER_HEIGHT, maxZ, wallRed, wallGreen, wallBlue, 0.5f);

            // Bottom edges
            drawLine(buffer, matrix, minX, y - 32, minZ, maxX, y - 32, minZ, wallRed, wallGreen, wallBlue, 0.5f);
            drawLine(buffer, matrix, minX, y - 32, maxZ, maxX, y - 32, maxZ, wallRed, wallGreen, wallBlue, 0.5f);
            drawLine(buffer, matrix, minX, y - 32, minZ, minX, y - 32, maxZ, wallRed, wallGreen, wallBlue, 0.5f);
            drawLine(buffer, matrix, maxX, y - 32, minZ, maxX, y - 32, maxZ, wallRed, wallGreen, wallBlue, 0.5f);

            // Vertical edges
            drawLine(buffer, matrix, minX, y - 32, minZ, minX, y + CORNER_HEIGHT, minZ, wallRed, wallGreen, wallBlue, 0.5f);
            drawLine(buffer, matrix, maxX, y - 32, minZ, maxX, y + CORNER_HEIGHT, minZ, wallRed, wallGreen, wallBlue, 0.5f);
            drawLine(buffer, matrix, minX, y - 32, maxZ, minX, y + CORNER_HEIGHT, maxZ, wallRed, wallGreen, wallBlue, 0.5f);
            drawLine(buffer, matrix, maxX, y - 32, maxZ, maxX, y + CORNER_HEIGHT, maxZ, wallRed, wallGreen, wallBlue, 0.5f);
        }
    }

    /**
     * Draws a line between two points.
     *
     * @param buffer The buffer builder
     * @param matrix The transformation matrix
     * @param x1 The start X coordinate
     * @param y1 The start Y coordinate
     * @param z1 The start Z coordinate
     * @param x2 The end X coordinate
     * @param y2 The end Y coordinate
     * @param z2 The end Z coordinate
     * @param red The red component (0-1)
     * @param green The green component (0-1)
     * @param blue The blue component (0-1)
     * @param alpha The alpha component (0-1)
     */
    private static void drawLine(BufferBuilder buffer, Matrix4f matrix, float x1, float y1, float z1, float x2, float y2, float z2, float red, float green, float blue, float alpha) {
        buffer.vertex(matrix, x1, y1, z1).color(red, green, blue, alpha).next();
        buffer.vertex(matrix, x2, y2, z2).color(red, green, blue, alpha).next();
    }

    /**
     * Draws a wall quad for a chunk boundary.
     *
     * @param buffer The buffer builder
     * @param matrix The transformation matrix
     * @param x1 The minimum X coordinate
     * @param y1 The minimum Y coordinate
     * @param z1 The minimum Z coordinate
     * @param x2 The maximum X coordinate
     * @param y2 The maximum Y coordinate
     * @param z2 The maximum Z coordinate
     * @param red The red component (0-1)
     * @param green The green component (0-1)
     * @param blue The blue component (0-1)
     * @param alpha The alpha component (0-1)
     */
    private static void drawWallQuad(BufferBuilder buffer, Matrix4f matrix, float x1, float y1, float z1, float x2, float y2, float z2, float red, float green, float blue, float alpha) {
        // Determine if this is a wall along the X or Z axis
        if (x1 == x2) {
            // Wall along the Z axis (east/west wall)
            buffer.vertex(matrix, x1, y1, z1).color(red, green, blue, alpha).next(); // Bottom-near
            buffer.vertex(matrix, x1, y1, z2).color(red, green, blue, alpha).next(); // Bottom-far
            buffer.vertex(matrix, x1, y2, z2).color(red, green, blue, alpha).next(); // Top-far
            buffer.vertex(matrix, x1, y2, z1).color(red, green, blue, alpha).next(); // Top-near
        } else if (z1 == z2) {
            // Wall along the X axis (north/south wall)
            buffer.vertex(matrix, x1, y1, z1).color(red, green, blue, alpha).next(); // Bottom-left
            buffer.vertex(matrix, x2, y1, z1).color(red, green, blue, alpha).next(); // Bottom-right
            buffer.vertex(matrix, x2, y2, z1).color(red, green, blue, alpha).next(); // Top-right
            buffer.vertex(matrix, x1, y2, z1).color(red, green, blue, alpha).next(); // Top-left
        }
    }
}
