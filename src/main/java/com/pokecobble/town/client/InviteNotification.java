package com.pokecobble.town.client;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import com.pokecobble.town.gui.TownScreenManager;
import com.pokecobble.town.sound.SoundRegistry;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.sound.PositionedSoundInstance;
import net.minecraft.sound.SoundCategory;
import net.minecraft.text.Text;
import net.minecraft.util.Formatting;

import java.util.UUID;

/**
 * Displays a persistent notification for town invitations.
 * This notification appears on the right side of the screen and stays until the player responds.
 */
public class InviteNotification {
    private static UUID pendingInviteTownId = null;
    private static String pendingInviteTownName = null;
    private static long animationStartTime = 0;
    private static final float ANIMATION_DURATION = 500; // milliseconds
    private static final float PULSE_DURATION = 2000; // milliseconds

    // UI colors
    private static final int BG_COLOR = 0xE0101820; // Darker, more opaque background
    private static final int BORDER_COLOR = 0xFF5D6CFF; // Blue border
    private static final int TITLE_COLOR = 0xFFFFAA00; // Gold color for title

    /**
     * Sets a pending invitation.
     *
     * @param townId The ID of the town that sent the invitation
     * @param townName The name of the town that sent the invitation
     */
    public static void setPendingInvite(UUID townId, String townName) {
        pendingInviteTownId = townId;
        pendingInviteTownName = townName;
        animationStartTime = System.currentTimeMillis();

        // Log the invitation
        Pokecobbleclaim.LOGGER.info("Received invitation from town: " + townName);

        // Note: Notification sound is now handled by PhoneNotificationManager
    }

    /**
     * Plays the notification sound.
     */
    private static void playNotificationSound() {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client != null) {
            // Play the notification sound
            client.getSoundManager().play(
                PositionedSoundInstance.master(SoundRegistry.INVITE_NOTIFICATION, 1.0F, 1.0F)
            );
            Pokecobbleclaim.LOGGER.info("Playing notification sound");
        }
    }

    /**
     * Clears the pending invitation.
     */
    public static void clearPendingInvite() {
        pendingInviteTownId = null;
        pendingInviteTownName = null;
    }

    /**
     * Checks if there is a pending invitation.
     *
     * @return true if there is a pending invitation, false otherwise
     */
    public static boolean hasPendingInvite() {
        return pendingInviteTownId != null;
    }

    /**
     * Gets the ID of the town that sent the pending invitation.
     *
     * @return The town ID, or null if there is no pending invitation
     */
    public static UUID getPendingInviteTownId() {
        return pendingInviteTownId;
    }

    /**
     * Gets the name of the town that sent the pending invitation.
     *
     * @return The town name, or null if there is no pending invitation
     */
    public static String getPendingInviteTownName() {
        return pendingInviteTownName;
    }

    /**
     * Renders the invitation notification on the HUD.
     *
     * @param context The draw context
     */
    public static void render(DrawContext context) {
        if (!hasPendingInvite()) {
            return;
        }

        Pokecobbleclaim.LOGGER.info("Rendering invitation notification for town: " + pendingInviteTownName);

        MinecraftClient client = MinecraftClient.getInstance();
        int screenWidth = client.getWindow().getScaledWidth();
        int screenHeight = client.getWindow().getScaledHeight();

        // Calculate animation progress
        long currentTime = System.currentTimeMillis();
        float animationProgress = Math.min(1.0f, (currentTime - animationStartTime) / ANIMATION_DURATION);

        // Calculate pulse effect (subtle scaling)
        float pulseProgress = ((currentTime % PULSE_DURATION) / PULSE_DURATION);
        float pulseScale = 1.0f + 0.05f * (float)Math.sin(pulseProgress * Math.PI * 2);

        // Calculate notification dimensions - compact but wide enough for text
        int baseWidth = 170; // Width to fit text
        int baseHeight = 36; // Very compact height
        int width = (int)(baseWidth * pulseScale);
        int height = (int)(baseHeight * pulseScale);

        // Calculate position (right side of screen, middle height)
        // Use animation to slide in from right
        int targetX = screenWidth - width - 15;
        int startX = screenWidth + 50;
        int x = startX - (int)((startX - targetX) * animationProgress);
        int y = (screenHeight - height) / 2;

        // Draw background with border - more stylish
        int bgColor = 0xE0101820; // Darker, more opaque background
        int borderColor = 0xFF5D6CFF; // Blue border

        // Draw background with rounded corners and gradient effect
        context.fillGradient(x + 1, y, x + width - 1, y + height, BG_COLOR, 0xE0000C18); // Main fill with gradient
        context.fill(x, y + 1, x + width, y + height - 1, BG_COLOR); // Side fills

        // Draw border with subtle glow effect
        int glowColor = 0x80AAAAFF; // Subtle blue glow
        context.drawHorizontalLine(x + 1, x + width - 1, y, BORDER_COLOR); // Top
        context.drawHorizontalLine(x + 1, x + width - 1, y + height, BORDER_COLOR); // Bottom
        context.drawVerticalLine(x, y + 1, y + height - 1, BORDER_COLOR); // Left
        context.drawVerticalLine(x + width, y + 1, y + height - 1, BORDER_COLOR); // Right

        // Add subtle glow effect
        context.drawHorizontalLine(x + 2, x + width - 2, y - 1, glowColor); // Top glow
        context.drawHorizontalLine(x + 2, x + width - 2, y + height + 1, glowColor); // Bottom glow

        // Draw title with icon - more compact layout
        Text titleText = Text.literal("✉ Town Invitation").formatted(Formatting.GOLD, Formatting.BOLD);
        int titleWidth = client.textRenderer.getWidth(titleText);
        context.drawTextWithShadow(client.textRenderer, titleText, x + (width - titleWidth) / 2, y + 4, TITLE_COLOR);

        // Draw town name - more compact with from prefix
        Text townText = Text.literal("From: ").formatted(Formatting.WHITE)
                .append(Text.literal(pendingInviteTownName).formatted(Formatting.YELLOW));
        int townWidth = client.textRenderer.getWidth(townText);
        context.drawTextWithShadow(client.textRenderer, townText, x + (width - townWidth) / 2, y + 15, 0xFFFFFF);

        // Draw instruction - original text but more compact
        Text instructionText = Text.literal("Open town menu to respond").formatted(Formatting.GREEN);
        int instructionWidth = client.textRenderer.getWidth(instructionText);
        context.drawTextWithShadow(client.textRenderer, instructionText,
                x + (width - instructionWidth) / 2, y + 26, 0xFFFFFF);
    }
}
