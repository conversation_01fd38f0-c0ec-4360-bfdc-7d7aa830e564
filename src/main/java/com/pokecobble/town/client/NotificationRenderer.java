package com.pokecobble.town.client;

import com.pokecobble.town.gui.NotificationManager;
import net.fabricmc.fabric.api.client.rendering.v1.HudRenderCallback;
import net.minecraft.client.MinecraftClient;

/**
 * Handles rendering notifications on the HUD.
 */
public class NotificationRenderer {
    /**
     * Registers the notification renderer.
     */
    public static void register() {
        // Register the HUD render callback to render notifications
        HudRenderCallback.EVENT.register((context, tickDelta) -> {
            // Update notifications
            NotificationManager.getInstance().update();

            // Render notifications
            NotificationManager.getInstance().render(context);
        });
    }

    /**
     * Adds a notification to be displayed.
     *
     * @param message The message to display
     */
    public static void addNotification(String message) {
        NotificationManager.getInstance().addInfoNotification(message);
    }

    /**
     * Adds an error notification.
     *
     * @param message The error message to display
     */
    public static void addErrorNotification(String message) {
        NotificationManager.getInstance().addErrorNotification(message);
    }

    /**
     * Adds a success notification.
     *
     * @param message The success message to display
     */
    public static void addSuccessNotification(String message) {
        NotificationManager.getInstance().addSuccessNotification(message);
    }
}
