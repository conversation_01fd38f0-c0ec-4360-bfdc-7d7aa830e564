package com.pokecobble.town.client;

import com.pokecobble.town.claim.ClaimTag;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.util.math.ChunkPos;

/**
 * Handles displaying a notification when a player enters a claimed chunk.
 */
public class ClaimedChunkNotification {
    private static final int FADE_IN_TIME = 10; // Ticks
    private static final int STAY_TIME = 60; // Ticks
    private static final int FADE_OUT_TIME = 10; // Ticks
    private static final int TOTAL_DISPLAY_TIME = FADE_IN_TIME + STAY_TIME + FADE_OUT_TIME;

    private static String townName = null;
    private static String tagName = null;
    private static int tagColor = 0xFFFFFFFF; // Default white
    private static int displayTicks = 0;
    private static ChunkPos lastChunk = null;

    /**
     * Shows a notification for a claimed chunk.
     *
     * @param town The name of the town that owns the chunk
     * @param tag The tag name
     * @param color The tag color
     */
    public static void show(String town, String tag, int color) {
        townName = town;
        tagName = tag;
        tagColor = color | 0xFF000000; // Ensure full opacity
        displayTicks = TOTAL_DISPLAY_TIME;

        // Reset cache
        cachedNotificationText = null;
        cachedTextWidth = 0;
        cachedColorWithAlpha = 0;
    }

    /**
     * Hides the current notification.
     */
    public static void hide() {
        townName = null;
        tagName = null;
        displayTicks = 0;

        // Reset cache
        cachedNotificationText = null;
        cachedTextWidth = 0;
        cachedColorWithAlpha = 0;
    }

    /**
     * Updates the notification timer.
     */
    public static void update() {
        if (displayTicks > 0) {
            displayTicks--;
        }
    }

    // Cache for rendered notification to avoid recalculating every frame
    private static String cachedNotificationText = null;
    private static int cachedTextWidth = 0;
    private static int cachedColorWithAlpha = 0;

    /**
     * Renders the notification on the HUD.
     * Optimized to reduce text width calculations and string concatenations.
     *
     * @param context The draw context
     */
    public static void render(DrawContext context) {
        if (townName == null || displayTicks <= 0) {
            return;
        }

        MinecraftClient client = MinecraftClient.getInstance();
        int screenWidth = client.getWindow().getScaledWidth();
        int screenHeight = client.getWindow().getScaledHeight();

        // Calculate alpha based on fade in/out
        float alpha = 1.0f;
        if (displayTicks > STAY_TIME + FADE_OUT_TIME) {
            // Fade in
            alpha = 1.0f - ((float)(displayTicks - STAY_TIME - FADE_OUT_TIME) / FADE_IN_TIME);
        } else if (displayTicks < FADE_OUT_TIME) {
            // Fade out
            alpha = (float)displayTicks / FADE_OUT_TIME;
        }

        // Apply alpha to color
        int alphaInt = Math.max(0, Math.min(255, (int)(alpha * 255)));
        int colorWithAlpha = (tagColor & 0x00FFFFFF) | (alphaInt << 24);

        // Only recalculate text if it has changed or alpha has changed significantly
        if (cachedNotificationText == null || cachedColorWithAlpha != colorWithAlpha) {
            // Create notification text
            cachedNotificationText = "Claimed by " + townName + " (" + tagName + ")";
            cachedTextWidth = client.textRenderer.getWidth(cachedNotificationText);
            cachedColorWithAlpha = colorWithAlpha;
        }

        // Calculate position (centered horizontally, above hotbar)
        int x = (screenWidth - cachedTextWidth) / 2;
        int y = screenHeight - 50; // Above hotbar

        // Draw text with tag color and no background
        context.drawTextWithShadow(client.textRenderer, cachedNotificationText, x, y, colorWithAlpha);
    }
}
