package com.pokecobble.town.client;

import com.pokecobble.town.claim.ClaimTool;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.option.KeyBinding;
import net.minecraft.client.util.InputUtil;
import net.minecraft.text.Text;
import org.lwjgl.glfw.GLFW;

import java.util.HashSet;
import java.util.Set;

/**
 * Handles keybindings for the mod.
 */
public class KeyBindings {
    private static KeyBinding toggleChunkVisibilityKey;
    private static KeyBinding toggleChunkSelectionKey;

    // Track key states to prevent repeated triggering
    private static boolean wasQPressed = false;
    private static boolean wasEPressed = false;
    private static boolean wasRPressed = false;
    // Z and X keys for changing tags have been removed
    private static boolean wasCPressed = false;
    private static boolean wasEscPressed = false;

    // Set of allowed keys when claim tool is active
    private static final Set<Integer> ALLOWED_KEYS = new HashSet<>();

    static {
        // Add WASD keys
        ALLOWED_KEYS.add(GLFW.GLFW_KEY_W);
        ALLOWED_KEYS.add(GLFW.GLFW_KEY_A);
        ALLOWED_KEYS.add(GLFW.GLFW_KEY_S);
        ALLOWED_KEYS.add(GLFW.GLFW_KEY_D);

        // Add Space and Shift
        ALLOWED_KEYS.add(GLFW.GLFW_KEY_SPACE);
        ALLOWED_KEYS.add(GLFW.GLFW_KEY_LEFT_SHIFT);
        ALLOWED_KEYS.add(GLFW.GLFW_KEY_RIGHT_SHIFT);

        // Add our tool keys
        ALLOWED_KEYS.add(GLFW.GLFW_KEY_Q); // Toggle chunk visibility
        ALLOWED_KEYS.add(GLFW.GLFW_KEY_E); // Toggle tag on current chunk (was: Select/deselect chunk)
        ALLOWED_KEYS.add(GLFW.GLFW_KEY_R); // Open claim tag menu
        // Z and X keys for changing tags have been removed

        // Add escape key to exit the tool (only ESC can close the tool)
        ALLOWED_KEYS.add(GLFW.GLFW_KEY_ESCAPE);
    }

    /**
     * Registers all keybindings.
     */
    public static void register() {
        // Register the toggle chunk visibility key (Q)
        toggleChunkVisibilityKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
            "key.pokecobble.toggle_chunk_visibility",
            InputUtil.Type.KEYSYM,
            GLFW.GLFW_KEY_Q,
            "category.pokecobble.town"
        ));

        // Register the toggle chunk selection key (E)
        toggleChunkSelectionKey = KeyBindingHelper.registerKeyBinding(new KeyBinding(
            "key.pokecobble.toggle_chunk_selection",
            InputUtil.Type.KEYSYM,
            GLFW.GLFW_KEY_E,
            "category.pokecobble.town"
        ));

        // Register the tick event to handle key presses
        ClientTickEvents.END_CLIENT_TICK.register(client -> {
            // Only handle key presses if the claim tool is active
            if (ClaimTool.getInstance().isActive()) {
                // Handle toggle chunk visibility key (Q)
                boolean qKeyPressed = InputUtil.isKeyPressed(MinecraftClient.getInstance().getWindow().getHandle(), GLFW.GLFW_KEY_Q);
                if (qKeyPressed && !wasQPressed) {
                    ClaimTool.getInstance().toggleChunkVisibility();
                    wasQPressed = true;
                } else if (!qKeyPressed) {
                    wasQPressed = false;
                }

                // Handle E key (now used for toggling tags instead of chunk selection)
                boolean eKeyPressed = InputUtil.isKeyPressed(MinecraftClient.getInstance().getWindow().getHandle(), GLFW.GLFW_KEY_E);
                if (eKeyPressed && !wasEPressed) {
                    ClaimTool.getInstance().toggleTagOnCurrentChunk();
                    wasEPressed = true;
                } else if (!eKeyPressed) {
                    wasEPressed = false;
                }

                // Handle open claim tag menu key (R)
                boolean rKeyPressed = InputUtil.isKeyPressed(MinecraftClient.getInstance().getWindow().getHandle(), GLFW.GLFW_KEY_R);
                if (rKeyPressed && !wasRPressed) {
                    ClaimTool.getInstance().openClaimTagMenu();
                    wasRPressed = true;
                } else if (!rKeyPressed) {
                    wasRPressed = false;
                }

                // Z and X keys for changing tags have been removed
                // Only scroll wheel is used for changing tags now

                // We no longer handle ESC key here, it's handled by the KeyboardMixin
                boolean escKeyPressed = InputUtil.isKeyPressed(MinecraftClient.getInstance().getWindow().getHandle(), GLFW.GLFW_KEY_ESCAPE);
                if (!escKeyPressed) {
                    wasEscPressed = false;
                } else if (escKeyPressed && !wasEscPressed) {
                    wasEscPressed = true;
                }
            }
        });

        // Register key event handler to restrict keys when claim tool is active
        net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientLifecycleEvents.CLIENT_STARTED.register(client -> {
            // Use a mixin or event handler to intercept key presses
            // This is a simplified version - in a real implementation, you'd use a mixin
            // to properly intercept key events before they're processed

            // For demonstration purposes, we'll add a warning in the activate method
            // A proper implementation would require adding a mixin to KeyboardInput
        });
    }
}
