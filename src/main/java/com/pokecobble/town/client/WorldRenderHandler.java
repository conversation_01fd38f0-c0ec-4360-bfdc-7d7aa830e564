package com.pokecobble.town.client;

import com.pokecobble.town.claim.ClaimTool;
import com.pokecobble.town.client.ModernClaimToolHud;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.rendering.v1.HudRenderCallback;
import net.fabricmc.fabric.api.client.rendering.v1.WorldRenderEvents;
import net.minecraft.client.MinecraftClient;

/**
 * Handles rendering of world elements.
 */
public class WorldRenderHandler {
    /**
     * Registers the world render handler.
     */
    public static void register() {
        // Register the chunk boundary renderer
        ChunkBoundaryRenderer.register();

        // Register the HUD render callback to render the claim tool HUD
        // Use an explicit lambda to ensure we're using the latest version of the render method
        HudRenderCallback.EVENT.register((context, tickDelta) -> {
            ModernClaimToolHud.render(context, tickDelta);
        });

        // Register the HUD render callback for claimed chunk notifications
        HudRenderCallback.EVENT.register((context, tickDelta) -> {
            ClaimedChunkNotification.render(context);
        });

        // Note: Town invite notifications are now handled by the phone notification system
        // The InviteNotification class is kept for managing pending invite state only

        // Register the notification renderer
        NotificationRenderer.register();

        // Register the tick event to track player chunks
        ClientTickEvents.END_CLIENT_TICK.register(client -> {
            ChunkTracker.update();
        });

        // Registration complete
    }

    // All chunk rendering code has been removed
}
