package com.pokecobble.town.client;

import com.pokecobble.town.claim.ClaimTag;
import com.pokecobble.town.claim.ClaimTool;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.font.TextRenderer;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.util.math.ChunkPos;

/**
 * Modern, consolidated HUD renderer for the claim tool.
 * This combines the functionality of the previous separate renders into a single, clean design.
 */
public class ModernClaimToolHud {
    // Static instance for the HUD
    private static ModernClaimToolHud instance;

    // Flag to force a refresh on the next render
    private boolean needsRefresh = false;
    private static final MinecraftClient client = MinecraftClient.getInstance();

    // Colors for the modern UI
    private static final int PANEL_BACKGROUND = 0xC0000000; // Semi-transparent black for main panel
    private static final int PANEL_BORDER = 0x40FFFFFF; // Subtle white border
    private static final int TEXT_COLOR = 0xFFFFFFFF; // White text
    private static final int ACCENT_COLOR = 0xFF55AAFF; // Blue accent color
    private static final int HELP_BACKGROUND = 0xE0000000; // More opaque black for help panel

    // Status colors
    private static final int STATUS_FREE = 0xFF55FF55; // Green for free chunks
    private static final int STATUS_SELECTED = 0xFFFFAA00; // Orange for selected chunks
    private static final int STATUS_CLAIMED = 0xFFFF5555; // Red for claimed chunks

    // Spacing and sizing
    private static final int PADDING = 4;
    private static final int CORNER_RADIUS = 3;
    private static final int LINE_HEIGHT = 10;

    /**
     * Initializes the HUD and registers as a listener for selection changes.
     */
    public static void initialize() {
        if (instance == null) {
            instance = new ModernClaimToolHud();

            // Register as a listener for selection changes
            ClaimTool claimTool = ClaimTool.getInstance();
            claimTool.setSelectionChangeListener(() -> {
                if (instance != null) {
                    instance.needsRefresh = true;
                }
            });
        }
    }

    /**
     * Renders the claim tool HUD.
     * This is the main entry point for rendering the claim tool interface.
     */
    public static void render(DrawContext context, float tickDelta) {
        // Initialize the HUD if needed
        if (instance == null) {
            initialize();
        }
        ClaimTool claimTool = ClaimTool.getInstance();
        if (!claimTool.isActive()) return;

        int screenWidth = client.getWindow().getScaledWidth();
        int screenHeight = client.getWindow().getScaledHeight();

        // Get current chunk
        ChunkPos currentChunk = null;
        if (client.player != null) {
            currentChunk = new ChunkPos(client.player.getBlockPos());
        }

        // Draw the main compact info panel
        renderMainPanel(context, currentChunk, claimTool);

        // Draw the controls bar at the bottom
        renderControlsBar(context, screenWidth, screenHeight);

        // Draw expanded help if enabled
        if (claimTool.isShowExpandedHelp()) {
            renderHelpPanel(context, screenWidth, screenHeight);
        }
    }

    /**
     * Renders the main information panel in the top-left corner.
     */
    private static void renderMainPanel(DrawContext context, ChunkPos currentChunk, ClaimTool claimTool) {
        if (currentChunk == null) return;

        TextRenderer textRenderer = client.textRenderer;

        // Determine chunk status
        String statusText;
        int statusColor;

        if (claimTool.isChunkClaimed(currentChunk)) {
            statusText = "Claimed";
            statusColor = STATUS_CLAIMED;
        } else if (claimTool.isChunkSelected(currentChunk)) {
            statusText = "Selected";
            statusColor = STATUS_SELECTED;
        } else {
            statusText = "Free";
            statusColor = STATUS_FREE;
        }

        // Get current tag
        ClaimTag currentTag = claimTool.getCurrentTag();
        String tagName = currentTag != null ? currentTag.getName() : "None";
        int tagColor = currentTag != null ? (currentTag.getColor() | 0xFF000000) : TEXT_COLOR;

        // Get selected chunks count - this will be updated in real-time
        int selectedCount = claimTool.getSelectedChunks().size();

        // Get claim usage information
        int usedClaims = 0;
        int maxClaims = 0;
        int townClaimedChunksInWorld = 0;

        // Get the player's town
        if (client.player != null) {
            com.pokecobble.town.Town playerTown = com.pokecobble.town.TownManager.getInstance().getPlayerTown(client.player.getUuid());
            if (playerTown != null) {
                usedClaims = playerTown.getClaimCount();
                maxClaims = playerTown.getMaxClaims();

                // Count chunks claimed by this town
                townClaimedChunksInWorld = playerTown.getClaimCount();
            }
        }

        // No need to force a redraw, the HUD will update naturally

        // Check if we need to refresh the display
        if (instance != null && instance.needsRefresh) {
            // Reset the flag
            instance.needsRefresh = false;

            // Force a redraw by scheduling a task for the next tick
            if (client != null) {
                client.execute(() -> {
                    // This will trigger a redraw of the HUD
                    // We don't need to do anything here, just scheduling the task is enough
                });
            }
        }

        // Calculate panel dimensions
        int margin = 5;
        int panelWidth = 120;
        int panelHeight = 48; // Base height for chunk status, tag, selection count, and claims

        // Pre-calculate if we need to show a warning
        if (maxClaims > 0) {
            int effectiveUsedClaims = usedClaims + selectedCount;
            if (effectiveUsedClaims >= maxClaims || effectiveUsedClaims >= maxClaims - 1) {
                panelHeight += LINE_HEIGHT + 2; // Add space for warning
            }
        }

        // Draw panel background with rounded corners
        drawRoundedRect(context, margin, margin, panelWidth, panelHeight, CORNER_RADIUS, PANEL_BACKGROUND);

        // Draw subtle border
        drawRoundedRectOutline(context, margin, margin, panelWidth, panelHeight, CORNER_RADIUS, PANEL_BORDER);

        // Draw content
        int textX = margin + PADDING;
        int textY = margin + PADDING;

        // Draw chunk coordinates and status
        String posText = "Chunk: " + currentChunk.x + "," + currentChunk.z;
        context.drawText(textRenderer, posText, textX, textY, TEXT_COLOR, true);

        // Draw status indicator (colored dot + text)
        int statusX = textX + textRenderer.getWidth(posText) + 8;
        context.fill(statusX, textY + 3, statusX + 4, textY + 7, statusColor);
        context.drawText(textRenderer, statusText, statusX + 6, textY, statusColor, true);
        textY += LINE_HEIGHT + 2;

        // Draw tag info with colored square
        context.fill(textX, textY + 1, textX + 5, textY + 6, tagColor);
        context.drawText(textRenderer, "Tag: " + tagName, textX + 8, textY, tagColor, true);
        textY += LINE_HEIGHT + 2;

        // Draw selection count
        String selectionText = "Selected: " + selectedCount;
        context.drawText(textRenderer, selectionText, textX, textY,
                selectedCount > 0 ? STATUS_SELECTED : TEXT_COLOR, true);
        textY += LINE_HEIGHT + 2;

        // Draw claim usage
        if (maxClaims > 0) {
            // Calculate the effective used claims (current claims + selected)
            int effectiveUsedClaims = usedClaims + selectedCount;
            if (effectiveUsedClaims > maxClaims) effectiveUsedClaims = maxClaims;

            // Determine color based on effective usage
            int usageColor;
            if (effectiveUsedClaims < maxClaims * 0.5) {
                usageColor = STATUS_FREE; // Less than 50% used
            } else if (effectiveUsedClaims < maxClaims * 0.8) {
                usageColor = STATUS_SELECTED; // Between 50% and 80% used
            } else {
                usageColor = STATUS_CLAIMED; // More than 80% used
            }

            // Draw claims with real-time update based on selected chunks
            String claimText = "Claims: " + effectiveUsedClaims + "/" + maxClaims;
            context.drawText(textRenderer, claimText, textX, textY, usageColor, true);

            // Show a warning if at or near the claim limit
            if (effectiveUsedClaims >= maxClaims) {
                textY += LINE_HEIGHT + 2;
                String warningText = "Maximum claims reached!";
                context.drawText(textRenderer, warningText, textX, textY, STATUS_CLAIMED, true);
            } else if (effectiveUsedClaims >= maxClaims - 1) {
                textY += LINE_HEIGHT + 2;
                String warningText = "Only 1 more claim available!";
                context.drawText(textRenderer, warningText, textX, textY, STATUS_SELECTED, true);
            }
        }
    }

    /**
     * Renders the controls bar at the bottom of the screen.
     */
    private static void renderControlsBar(DrawContext context, int screenWidth, int screenHeight) {
        TextRenderer textRenderer = client.textRenderer;

        // Calculate bar dimensions
        int barHeight = 16;
        int barWidth = 280;
        int barY = screenHeight - barHeight - 5;
        int barX = (screenWidth - barWidth) / 2;

        // Draw bar background with rounded corners
        drawRoundedRect(context, barX, barY, barWidth, barHeight, CORNER_RADIUS, PANEL_BACKGROUND);

        // Draw subtle border
        drawRoundedRectOutline(context, barX, barY, barWidth, barHeight, CORNER_RADIUS, PANEL_BORDER);

        // Draw controls text with more detailed key help
        String controls = "LMB:Select  E:Tag Chunk  Scroll:Change Tag  ESC:Exit";
        int textX = barX + (barWidth - textRenderer.getWidth(controls)) / 2;
        int textY = barY + (barHeight - 8) / 2;
        context.drawText(textRenderer, controls, textX, textY, TEXT_COLOR, true);
    }

    /**
     * This method has been removed as the help panel is no longer shown.
     * The key controls are now displayed directly in the controls bar.
     */
    private static void renderHelpPanel(DrawContext context, int screenWidth, int screenHeight) {
        // Help panel has been removed
        // All necessary controls are now shown in the controls bar
    }



    /**
     * Draws a rounded rectangle.
     */
    private static void drawRoundedRect(DrawContext context, int x, int y, int width, int height, int radius, int color) {
        // Main rectangle
        context.fill(x + radius, y, x + width - radius, y + height, color);
        context.fill(x, y + radius, x + width, y + height - radius, color);

        // Top-left corner
        fillCircleQuarter(context, x + radius, y + radius, radius, 0, color);
        // Top-right corner
        fillCircleQuarter(context, x + width - radius, y + radius, radius, 1, color);
        // Bottom-right corner
        fillCircleQuarter(context, x + width - radius, y + height - radius, radius, 2, color);
        // Bottom-left corner
        fillCircleQuarter(context, x + radius, y + height - radius, radius, 3, color);
    }

    /**
     * Draws a rounded rectangle outline.
     */
    private static void drawRoundedRectOutline(DrawContext context, int x, int y, int width, int height, int radius, int color) {
        // Top and bottom edges
        context.fill(x + radius, y, x + width - radius, y + 1, color);
        context.fill(x + radius, y + height - 1, x + width - radius, y + height, color);

        // Left and right edges
        context.fill(x, y + radius, x + 1, y + height - radius, color);
        context.fill(x + width - 1, y + radius, x + width, y + height - radius, color);

        // Draw the corner outlines
        drawCircleQuarterOutline(context, x + radius, y + radius, radius, 0, color);
        drawCircleQuarterOutline(context, x + width - radius, y + radius, radius, 1, color);
        drawCircleQuarterOutline(context, x + width - radius, y + height - radius, radius, 2, color);
        drawCircleQuarterOutline(context, x + radius, y + height - radius, radius, 3, color);
    }

    /**
     * Fills a quarter of a circle.
     * @param quarter 0=top-left, 1=top-right, 2=bottom-right, 3=bottom-left
     */
    private static void fillCircleQuarter(DrawContext context, int centerX, int centerY, int radius, int quarter, int color) {
        for (int dx = 0; dx <= radius; dx++) {
            for (int dy = 0; dy <= radius; dy++) {
                // Check if point is within the circle
                if (dx * dx + dy * dy <= radius * radius) {
                    int drawX = centerX;
                    int drawY = centerY;

                    // Adjust coordinates based on which quarter we're drawing
                    switch (quarter) {
                        case 0: // Top-left
                            drawX -= dx;
                            drawY -= dy;
                            break;
                        case 1: // Top-right
                            drawX += dx;
                            drawY -= dy;
                            break;
                        case 2: // Bottom-right
                            drawX += dx;
                            drawY += dy;
                            break;
                        case 3: // Bottom-left
                            drawX -= dx;
                            drawY += dy;
                            break;
                    }

                    context.fill(drawX, drawY, drawX + 1, drawY + 1, color);
                }
            }
        }
    }

    /**
     * Draws the outline of a quarter of a circle.
     * @param quarter 0=top-left, 1=top-right, 2=bottom-right, 3=bottom-left
     */
    private static void drawCircleQuarterOutline(DrawContext context, int centerX, int centerY, int radius, int quarter, int color) {
        for (int i = 0; i <= 90; i++) {
            double angle = Math.toRadians(i);

            // Adjust angle based on which quarter we're drawing
            switch (quarter) {
                case 0: // Top-left
                    angle = Math.toRadians(180 + i);
                    break;
                case 1: // Top-right
                    angle = Math.toRadians(270 + i);
                    break;
                case 2: // Bottom-right
                    angle = Math.toRadians(i);
                    break;
                case 3: // Bottom-left
                    angle = Math.toRadians(90 + i);
                    break;
            }

            int x = (int) (centerX + Math.cos(angle) * radius);
            int y = (int) (centerY + Math.sin(angle) * radius);

            context.fill(x, y, x + 1, y + 1, color);
        }
    }
}
