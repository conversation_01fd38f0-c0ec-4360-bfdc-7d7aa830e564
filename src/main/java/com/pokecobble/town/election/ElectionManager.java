package com.pokecobble.town.election;

import com.pokecobble.town.Town;
import com.pokecobble.town.network.election.ElectionNetworkHandler;
import net.minecraft.entity.player.PlayerEntity;

import java.util.*;

/**
 * Manages elections for all towns.
 */
public class ElectionManager {
    private static ElectionManager instance;

    private final Map<UUID, Election> elections = new HashMap<>(); // Town UUID -> Election

    private ElectionManager() {
        // Private constructor for singleton
    }

    /**
     * Gets the singleton instance of the ElectionManager.
     */
    public static ElectionManager getInstance() {
        if (instance == null) {
            instance = new ElectionManager();
        }
        return instance;
    }

    /**
     * Starts a new election for a town.
     *
     * @param town The town to start an election for
     * @return The new election, or null if an election is already in progress
     */
    public Election startElection(Town town) {
        if (town == null) {
            return null;
        }

        // Check if an election is already in progress
        if (elections.containsKey(town.getId())) {
            Election existingElection = elections.get(town.getId());
            if (!existingElection.isCompleted()) {
                return null; // Election already in progress
            } else {
                // Remove the completed election
                elections.remove(town.getId());
            }
        }

        // Create a new election
        Election election = new Election(town);
        elections.put(town.getId(), election);

        return election;
    }

    /**
     * Gets the current election for a town.
     *
     * @param town The town to get the election for
     * @return The current election, or null if no election is in progress
     */
    public Election getElection(Town town) {
        if (town == null) {
            return null;
        }

        Election election = elections.get(town.getId());
        if (election != null) {
            // Check if the election has ended
            election.checkElectionEnd();

            // If the election is completed, remove it and return null
            if (election.isCompleted()) {
                elections.remove(town.getId());
                // Notify callbacks that the election has been updated (completed)
                ElectionNetworkHandler.notifyElectionUpdated(town.getId());
                return null;
            }
        }

        return election;
    }

    /**
     * Sets an election for a town.
     * This method is used by the network code.
     *
     * @param town The town to set the election for
     * @param election The election to set
     */
    public void setElection(Town town, Election election) {
        if (town == null || election == null) {
            return;
        }

        elections.put(town.getId(), election);
    }

    /**
     * Removes an election for a town.
     * This method is used by the network code.
     *
     * @param town The town to remove the election for
     */
    public void removeElection(Town town) {
        if (town == null) {
            return;
        }

        elections.remove(town.getId());
    }

    /**
     * Saves an election to persistent storage.
     * In a real implementation, this would save to a database.
     *
     * @param town The town holding the election
     * @param election The election to save
     */
    public void saveElection(Town town, Election election) {
        // In a real implementation, this would save to a database
        // For now, we just make sure it's in our map
        if (town == null || election == null) {
            return;
        }

        elections.put(town.getId(), election);
    }

    /**
     * Casts a vote in an election.
     *
     * @param town The town holding the election
     * @param voter The player casting the vote
     * @param candidate The candidate being voted for
     * @return True if the vote was cast successfully, false otherwise
     */
    public boolean castVote(Town town, UUID voter, UUID candidate) {
        if (town == null) {
            return false;
        }

        Election election = elections.get(town.getId());
        if (election == null || election.isCompleted()) {
            return false;
        }

        return election.castVote(voter, candidate);
    }

    /**
     * Updates all elections.
     * Should be called periodically to check for completed elections.
     */
    public void update() {
        // Check all elections for completion
        for (Map.Entry<UUID, Election> entry : elections.entrySet()) {
            Election election = entry.getValue();
            boolean wasCompleted = election.isCompleted();
            election.checkElectionEnd();

            // If the election just completed, notify callbacks
            if (!wasCompleted && election.isCompleted()) {
                ElectionNetworkHandler.notifyElectionUpdated(entry.getKey());
            }
        }

        // Remove completed elections that are more than a day old
        long currentTime = System.currentTimeMillis();
        elections.entrySet().removeIf(entry ->
            entry.getValue().isCompleted() &&
            currentTime - entry.getValue().getEndTime() > 24 * 60 * 60 * 1000);
    }

    /**
     * Notifies a player about any relevant elections.
     *
     * @param player The player to notify
     */
    public void notifyPlayer(PlayerEntity player) {
        if (player == null) {
            return;
        }

        // Check if the player is a member of any town with an active election
        for (Election election : elections.values()) {
            if (!election.isCompleted() && election.getTown().getPlayers().contains(player.getUuid())) {
                election.notifyPlayer(player);
                break; // Only notify about one election at a time
            }
        }
    }

    /**
     * Gets all active elections.
     *
     * @return A list of all active elections
     */
    public List<Election> getActiveElections() {
        List<Election> activeElections = new ArrayList<>();

        for (Election election : elections.values()) {
            if (!election.isCompleted()) {
                activeElections.add(election);
            }
        }

        return activeElections;
    }
}
