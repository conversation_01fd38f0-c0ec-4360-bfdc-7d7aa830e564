package com.pokecobble.town.logging;

import com.pokecobble.Pokecobbleclaim;
import net.minecraft.server.MinecraftServer;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Centralized error logging system for the mod.
 * Captures and stores errors for later viewing in the admin GUI.
 */
public class ErrorLogger {
    // Singleton instance
    private static final ErrorLogger INSTANCE = new ErrorLogger();

    // Error storage - using a thread-safe queue
    private final ConcurrentLinkedQueue<ErrorEntry> errors = new ConcurrentLinkedQueue<>();

    // Counter for error IDs
    private final AtomicInteger errorIdCounter = new AtomicInteger(0);

    // Maximum number of errors to store
    private static final int MAX_ERRORS = 1000;

    // Date formatter for timestamps
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");

    // Server instance
    private MinecraftServer server;

    /**
     * Private constructor for singleton pattern.
     */
    private ErrorLogger() {
        // Initialize with a welcome message
        logInfo("Error logging system initialized", "System");
    }

    /**
     * Gets the singleton instance.
     *
     * @return The ErrorLogger instance
     */
    public static ErrorLogger getInstance() {
        return INSTANCE;
    }

    /**
     * Sets the server instance.
     *
     * @param server The server instance
     */
    public void setServer(MinecraftServer server) {
        this.server = server;
    }

    /**
     * Logs an error message.
     *
     * @param message The error message
     * @param source The source of the error (e.g., "Network", "Data", "Player")
     * @param severity The severity of the error
     */
    public void logError(String message, String source, ErrorSeverity severity) {
        logError(message, null, source, severity);
    }

    /**
     * Logs an error message with an exception.
     *
     * @param message The error message
     * @param exception The exception that caused the error
     * @param source The source of the error (e.g., "Network", "Data", "Player")
     * @param severity The severity of the error
     */
    public void logError(String message, Throwable exception, String source, ErrorSeverity severity) {
        // Create error entry
        ErrorEntry entry = new ErrorEntry(
            errorIdCounter.incrementAndGet(),
            System.currentTimeMillis(),
            message,
            exception != null ? exception.toString() : null,
            exception != null ? getStackTraceAsString(exception) : null,
            source,
            severity,
            null,
            null
        );

        // Add to queue
        errors.add(entry);

        // Trim if necessary
        while (errors.size() > MAX_ERRORS) {
            errors.poll();
        }

        // Log to console as well
        if (severity == ErrorSeverity.CRITICAL) {
            Pokecobbleclaim.LOGGER.error("[" + source + "] " + message, exception);
        } else if (severity == ErrorSeverity.ERROR) {
            Pokecobbleclaim.LOGGER.error("[" + source + "] " + message, exception);
        } else if (severity == ErrorSeverity.WARNING) {
            Pokecobbleclaim.LOGGER.warn("[" + source + "] " + message, exception);
        }
    }

    /**
     * Logs a warning message.
     *
     * @param message The warning message
     * @param source The source of the warning
     */
    public void logWarning(String message, String source) {
        logError(message, null, source, ErrorSeverity.WARNING);
    }

    /**
     * Logs an error message with an exception and player information.
     *
     * @param message The error message
     * @param exception The exception that caused the error
     * @param source The source of the error (e.g., "Network", "Data", "Player")
     * @param severity The severity of the error
     * @param playerName The name of the player involved, or null if no player is involved
     * @param playerUuid The UUID of the player involved, or null if no player is involved
     */
    public void logError(String message, Throwable exception, String source, ErrorSeverity severity, String playerName, UUID playerUuid) {
        // Create error entry
        ErrorEntry entry = new ErrorEntry(
            errorIdCounter.incrementAndGet(),
            System.currentTimeMillis(),
            message,
            exception != null ? exception.toString() : null,
            exception != null ? getStackTraceAsString(exception) : null,
            source,
            severity,
            playerName,
            playerUuid != null ? playerUuid.toString() : null
        );

        // Add to queue
        errors.add(entry);

        // Trim if necessary
        while (errors.size() > MAX_ERRORS) {
            errors.poll();
        }

        // Log to console as well
        String playerInfo = playerName != null ? " [Player: " + playerName + "]" : "";
        if (severity == ErrorSeverity.CRITICAL) {
            Pokecobbleclaim.LOGGER.error("[" + source + "]" + playerInfo + " " + message, exception);
        } else if (severity == ErrorSeverity.ERROR) {
            Pokecobbleclaim.LOGGER.error("[" + source + "]" + playerInfo + " " + message, exception);
        } else if (severity == ErrorSeverity.WARNING) {
            Pokecobbleclaim.LOGGER.warn("[" + source + "]" + playerInfo + " " + message, exception);
        }
    }

    /**
     * Logs an info message.
     *
     * @param message The info message
     * @param source The source of the info
     */
    public void logInfo(String message, String source) {
        // Create error entry
        ErrorEntry entry = new ErrorEntry(
            errorIdCounter.incrementAndGet(),
            System.currentTimeMillis(),
            message,
            null,
            null,
            source,
            ErrorSeverity.INFO,
            null,
            null
        );

        // Add to queue
        errors.add(entry);

        // Trim if necessary
        while (errors.size() > MAX_ERRORS) {
            errors.poll();
        }

        // Log to console as well
        Pokecobbleclaim.LOGGER.info("[" + source + "] " + message);
    }

    /**
     * Gets all error entries.
     *
     * @return A list of all error entries
     */
    public List<ErrorEntry> getAllErrors() {
        List<ErrorEntry> result = new ArrayList<>(errors);
        // Sort by timestamp (newest first)
        result.sort(Comparator.comparing(ErrorEntry::getTimestamp).reversed());
        return result;
    }

    /**
     * Gets filtered error entries with minimum severity.
     *
     * @param minSeverity The minimum severity to include
     * @param source The source to filter by, or null for all sources
     * @param searchTerm The search term to filter by, or null for no text filtering
     * @return A list of filtered error entries
     */
    public List<ErrorEntry> getFilteredErrors(ErrorSeverity minSeverity, String source, String searchTerm) {
        List<ErrorEntry> result = new ArrayList<>();

        for (ErrorEntry entry : errors) {
            // Check severity
            if (entry.getSeverity().ordinal() < minSeverity.ordinal()) {
                continue;
            }

            // Check source
            if (source != null && !source.isEmpty() && !entry.getSource().equalsIgnoreCase(source)) {
                continue;
            }

            // Check search term
            if (searchTerm != null && !searchTerm.isEmpty()) {
                boolean matches = entry.getMessage().toLowerCase().contains(searchTerm.toLowerCase());
                if (!matches && entry.getExceptionMessage() != null) {
                    matches = entry.getExceptionMessage().toLowerCase().contains(searchTerm.toLowerCase());
                }
                if (!matches) {
                    continue;
                }
            }

            result.add(entry);
        }

        // Sort by timestamp (newest first)
        result.sort(Comparator.comparing(ErrorEntry::getTimestamp).reversed());

        return result;
    }

    /**
     * Gets filtered error entries with exact severity.
     *
     * @param exactSeverity The exact severity to include
     * @param source The source to filter by, or null for all sources
     * @param searchTerm The search term to filter by, or null for no text filtering
     * @return A list of filtered error entries
     */
    public List<ErrorEntry> getFilteredErrorsByExactSeverity(ErrorSeverity exactSeverity, String source, String searchTerm) {
        List<ErrorEntry> result = new ArrayList<>();

        for (ErrorEntry entry : errors) {
            // Check severity - must match exactly
            if (entry.getSeverity() != exactSeverity) {
                continue;
            }

            // Check source
            if (source != null && !source.isEmpty() && !entry.getSource().equalsIgnoreCase(source)) {
                continue;
            }

            // Check search term
            if (searchTerm != null && !searchTerm.isEmpty()) {
                boolean matches = entry.getMessage().toLowerCase().contains(searchTerm.toLowerCase());
                if (!matches && entry.getExceptionMessage() != null) {
                    matches = entry.getExceptionMessage().toLowerCase().contains(searchTerm.toLowerCase());
                }
                if (!matches) {
                    continue;
                }
            }

            result.add(entry);
        }

        // Sort by timestamp (newest first)
        result.sort(Comparator.comparing(ErrorEntry::getTimestamp).reversed());

        return result;
    }

    /**
     * Gets a list of all unique error sources.
     *
     * @return A list of all unique error sources
     */
    public List<String> getAllSources() {
        Set<String> sources = new HashSet<>();
        for (ErrorEntry entry : errors) {
            sources.add(entry.getSource());
        }
        List<String> result = new ArrayList<>(sources);
        Collections.sort(result);
        return result;
    }

    /**
     * Clears all errors.
     */
    public void clearErrors() {
        errors.clear();
        logInfo("Error log cleared", "System");
    }

    /**
     * Gets the stack trace of an exception as a string.
     *
     * @param throwable The exception
     * @return The stack trace as a string
     */
    private String getStackTraceAsString(Throwable throwable) {
        StringBuilder sb = new StringBuilder();
        for (StackTraceElement element : throwable.getStackTrace()) {
            sb.append("    at ").append(element.toString()).append("\n");
        }
        return sb.toString();
    }

    /**
     * Error severity levels.
     */
    public enum ErrorSeverity {
        INFO(0, "Info", 0xFF55FF55),      // Green
        WARNING(1, "Warning", 0xFFFFAA00), // Orange
        ERROR(2, "Error", 0xFFFF5555),     // Red
        CRITICAL(3, "Critical", 0xFFFF0000); // Bright Red

        private final int level;
        private final String name;
        private final int color;

        ErrorSeverity(int level, String name, int color) {
            this.level = level;
            this.name = name;
            this.color = color;
        }

        public int getLevel() {
            return level;
        }

        public String getName() {
            return name;
        }

        public int getColor() {
            return color;
        }
    }

    /**
     * Represents a single error entry.
     */
    public static class ErrorEntry {
        private final int id;
        private final long timestamp;
        private final String message;
        private final String exceptionMessage;
        private final String stackTrace;
        private final String source;
        private final ErrorSeverity severity;
        private final String playerName;
        private final String playerUuid;

        // Transient fields for UI rendering (not serialized)
        public transient int moreInfoButtonX = -1;
        public transient int moreInfoButtonY = -1;
        public transient int moreInfoButtonWidth = 0;
        public transient int moreInfoButtonHeight = 0;

        public ErrorEntry(int id, long timestamp, String message, String exceptionMessage,
                          String stackTrace, String source, ErrorSeverity severity,
                          String playerName, String playerUuid) {
            this.id = id;
            this.timestamp = timestamp;
            this.message = message;
            this.exceptionMessage = exceptionMessage;
            this.stackTrace = stackTrace;
            this.source = source;
            this.severity = severity;
            this.playerName = playerName;
            this.playerUuid = playerUuid;
        }

        public int getId() {
            return id;
        }

        public long getTimestamp() {
            return timestamp;
        }

        public String getFormattedTimestamp() {
            return DATE_FORMAT.format(new Date(timestamp));
        }

        public String getMessage() {
            return message;
        }

        public String getExceptionMessage() {
            return exceptionMessage;
        }

        public String getStackTrace() {
            return stackTrace;
        }

        public String getSource() {
            return source;
        }

        public ErrorSeverity getSeverity() {
            return severity;
        }

        public String getPlayerName() {
            return playerName;
        }

        public String getPlayerUuid() {
            return playerUuid;
        }

        public boolean hasPlayerInfo() {
            return playerName != null || playerUuid != null;
        }

        /**
         * Gets a complete text representation of this error entry for copying.
         *
         * @return A complete text representation of this error entry
         */
        public String getFullText() {
            StringBuilder sb = new StringBuilder();
            sb.append("Error ID: ").append(id).append("\n");
            sb.append("Timestamp: ").append(getFormattedTimestamp()).append("\n");
            sb.append("Severity: ").append(severity.getName()).append("\n");
            sb.append("Source: ").append(source).append("\n");

            if (hasPlayerInfo()) {
                sb.append("Player: ");
                if (playerName != null) {
                    sb.append(playerName);
                }
                if (playerUuid != null) {
                    sb.append(" (").append(playerUuid).append(")");
                }
                sb.append("\n");
            }

            sb.append("Message: ").append(message).append("\n");

            if (exceptionMessage != null) {
                sb.append("Exception: ").append(exceptionMessage).append("\n");
            }

            if (stackTrace != null) {
                sb.append("Stack Trace:\n").append(stackTrace).append("\n");
            }

            return sb.toString();
        }
    }
}
