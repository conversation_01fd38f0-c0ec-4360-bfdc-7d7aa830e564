package com.pokecobble.town.config;

import com.pokecobble.Pokecobbleclaim;
import net.fabricmc.loader.api.FabricLoader;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.Properties;

/**
 * Configuration for backup and compression settings.
 */
public class BackupConfig {
    private static final String CONFIG_FILE = "pokecobbleclaim-backup.properties";

    // Default values
    private static boolean enableAutoBackup = true;
    private static int backupIntervalHours = 24;
    private static int maxBackups = 7;
    private static boolean compressPlayerData = true;
    private static boolean backupOnServerStop = true;
    private static int compressionLevel = 6; // Default compression level (0-9, where 9 is highest)
    private static boolean enableIncrementalBackups = true;
    private static boolean enableDifferentialBackups = false;
    private static int backupThreadPriority = Thread.NORM_PRIORITY - 1; // Slightly below normal priority
    private static int bufferSize = 8192; // 8KB buffer size for I/O operations
    private static boolean verifyBackups = true;

    /**
     * Loads the backup configuration from the config file.
     */
    public static void load() {
        File configFile = new File(FabricLoader.getInstance().getConfigDir().toFile(), CONFIG_FILE);

        if (!configFile.exists()) {
            save(); // Create default config
            return;
        }

        try (FileReader reader = new FileReader(configFile)) {
            Properties props = new Properties();
            props.load(reader);

            enableAutoBackup = Boolean.parseBoolean(props.getProperty("enableAutoBackup", "true"));
            backupIntervalHours = Integer.parseInt(props.getProperty("backupIntervalHours", "24"));
            maxBackups = Integer.parseInt(props.getProperty("maxBackups", "7"));
            compressPlayerData = Boolean.parseBoolean(props.getProperty("compressPlayerData", "true"));
            backupOnServerStop = Boolean.parseBoolean(props.getProperty("backupOnServerStop", "true"));
            compressionLevel = Integer.parseInt(props.getProperty("compressionLevel", "6"));
            enableIncrementalBackups = Boolean.parseBoolean(props.getProperty("enableIncrementalBackups", "true"));
            enableDifferentialBackups = Boolean.parseBoolean(props.getProperty("enableDifferentialBackups", "false"));
            backupThreadPriority = Integer.parseInt(props.getProperty("backupThreadPriority", String.valueOf(Thread.NORM_PRIORITY - 1)));
            bufferSize = Integer.parseInt(props.getProperty("bufferSize", "8192"));
            verifyBackups = Boolean.parseBoolean(props.getProperty("verifyBackups", "true"));

            // Validate values
            compressionLevel = Math.max(0, Math.min(9, compressionLevel));
            backupThreadPriority = Math.max(Thread.MIN_PRIORITY, Math.min(Thread.MAX_PRIORITY, backupThreadPriority));
            bufferSize = Math.max(1024, Math.min(65536, bufferSize));

            Pokecobbleclaim.LOGGER.info("Loaded backup configuration");
        } catch (IOException | NumberFormatException e) {
            Pokecobbleclaim.LOGGER.error("Failed to load backup configuration", e);
            save(); // Create default config
        }
    }

    /**
     * Saves the backup configuration to the config file.
     */
    public static void save() {
        File configFile = new File(FabricLoader.getInstance().getConfigDir().toFile(), CONFIG_FILE);

        try (FileWriter writer = new FileWriter(configFile)) {
            Properties props = new Properties();

            props.setProperty("enableAutoBackup", String.valueOf(enableAutoBackup));
            props.setProperty("backupIntervalHours", String.valueOf(backupIntervalHours));
            props.setProperty("maxBackups", String.valueOf(maxBackups));
            props.setProperty("compressPlayerData", String.valueOf(compressPlayerData));
            props.setProperty("backupOnServerStop", String.valueOf(backupOnServerStop));
            props.setProperty("compressionLevel", String.valueOf(compressionLevel));
            props.setProperty("enableIncrementalBackups", String.valueOf(enableIncrementalBackups));
            props.setProperty("enableDifferentialBackups", String.valueOf(enableDifferentialBackups));
            props.setProperty("backupThreadPriority", String.valueOf(backupThreadPriority));
            props.setProperty("bufferSize", String.valueOf(bufferSize));
            props.setProperty("verifyBackups", String.valueOf(verifyBackups));

            props.store(writer, "PokeCobbleClaim Backup Configuration");
            Pokecobbleclaim.LOGGER.info("Saved backup configuration");
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Failed to save backup configuration", e);
        }
    }

    /**
     * Checks if automatic backups are enabled.
     *
     * @return true if automatic backups are enabled
     */
    public static boolean isAutoBackupEnabled() {
        return enableAutoBackup;
    }

    /**
     * Sets whether automatic backups are enabled.
     *
     * @param enabled Whether automatic backups are enabled
     */
    public static void setAutoBackupEnabled(boolean enabled) {
        enableAutoBackup = enabled;
        save();
    }

    /**
     * Gets the backup interval in hours.
     *
     * @return The backup interval in hours
     */
    public static int getBackupIntervalHours() {
        return backupIntervalHours;
    }

    /**
     * Sets the backup interval in hours.
     *
     * @param hours The backup interval in hours
     */
    public static void setBackupIntervalHours(int hours) {
        if (hours < 1) {
            hours = 1;
        }
        backupIntervalHours = hours;
        save();
    }

    /**
     * Gets the maximum number of backups to keep.
     *
     * @return The maximum number of backups
     */
    public static int getMaxBackups() {
        return maxBackups;
    }

    /**
     * Sets the maximum number of backups to keep.
     *
     * @param max The maximum number of backups
     */
    public static void setMaxBackups(int max) {
        if (max < 1) {
            max = 1;
        }
        maxBackups = max;
        save();
    }

    /**
     * Checks if player data compression is enabled.
     *
     * @return true if player data compression is enabled
     */
    public static boolean isCompressionEnabled() {
        return compressPlayerData;
    }

    /**
     * Sets whether player data compression is enabled.
     *
     * @param enabled Whether player data compression is enabled
     */
    public static void setCompressionEnabled(boolean enabled) {
        compressPlayerData = enabled;
        save();
    }

    /**
     * Checks if backup on server stop is enabled.
     *
     * @return true if backup on server stop is enabled
     */
    public static boolean isBackupOnServerStopEnabled() {
        return backupOnServerStop;
    }

    /**
     * Sets whether backup on server stop is enabled.
     *
     * @param enabled Whether backup on server stop is enabled
     */
    public static void setBackupOnServerStopEnabled(boolean enabled) {
        backupOnServerStop = enabled;
        save();
    }

    /**
     * Gets the compression level for backups.
     *
     * @return The compression level (0-9, where 9 is highest)
     */
    public static int getCompressionLevel() {
        return compressionLevel;
    }

    /**
     * Sets the compression level for backups.
     *
     * @param level The compression level (0-9, where 9 is highest)
     */
    public static void setCompressionLevel(int level) {
        compressionLevel = Math.max(0, Math.min(9, level));
        save();
    }

    /**
     * Checks if incremental backups are enabled.
     *
     * @return true if incremental backups are enabled
     */
    public static boolean isIncrementalBackupsEnabled() {
        return enableIncrementalBackups;
    }

    /**
     * Sets whether incremental backups are enabled.
     *
     * @param enabled Whether incremental backups are enabled
     */
    public static void setIncrementalBackupsEnabled(boolean enabled) {
        enableIncrementalBackups = enabled;
        save();
    }

    /**
     * Checks if differential backups are enabled.
     *
     * @return true if differential backups are enabled
     */
    public static boolean isDifferentialBackupsEnabled() {
        return enableDifferentialBackups;
    }

    /**
     * Sets whether differential backups are enabled.
     *
     * @param enabled Whether differential backups are enabled
     */
    public static void setDifferentialBackupsEnabled(boolean enabled) {
        enableDifferentialBackups = enabled;
        save();
    }

    /**
     * Gets the backup thread priority.
     *
     * @return The backup thread priority
     */
    public static int getBackupThreadPriority() {
        return backupThreadPriority;
    }

    /**
     * Sets the backup thread priority.
     *
     * @param priority The backup thread priority
     */
    public static void setBackupThreadPriority(int priority) {
        backupThreadPriority = Math.max(Thread.MIN_PRIORITY, Math.min(Thread.MAX_PRIORITY, priority));
        save();
    }

    /**
     * Gets the buffer size for I/O operations.
     *
     * @return The buffer size in bytes
     */
    public static int getBufferSize() {
        return bufferSize;
    }

    /**
     * Sets the buffer size for I/O operations.
     *
     * @param size The buffer size in bytes
     */
    public static void setBufferSize(int size) {
        bufferSize = Math.max(1024, Math.min(65536, size));
        save();
    }

    /**
     * Checks if backup verification is enabled.
     *
     * @return true if backup verification is enabled
     */
    public static boolean isVerifyBackupsEnabled() {
        return verifyBackups;
    }

    /**
     * Sets whether backup verification is enabled.
     *
     * @param enabled Whether backup verification is enabled
     */
    public static void setVerifyBackupsEnabled(boolean enabled) {
        verifyBackups = enabled;
        save();
    }
}
