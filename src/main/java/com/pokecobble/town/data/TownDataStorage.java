package com.pokecobble.town.data;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.TownManager;
import net.fabricmc.loader.api.FabricLoader;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import com.pokecobble.town.TownPlayerRank;

/**
 * Handles saving and loading town data to/from disk.
 */
public class TownDataStorage {
    // Constants for file paths
    private static final String DATA_FOLDER = "pokecobbleclaim";
    private static final String TOWNS_FILE = "towns.json";

    // GSON instance for serialization
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();

    /**
     * Saves all towns to disk.
     */
    public static void saveTowns() {
        try {
            // Get all towns from the TownManager
            Collection<Town> towns = TownManager.getInstance().getAllTowns();

            // Convert towns to serializable format
            List<SerializableTown> serializableTowns = new ArrayList<>();
            for (Town town : towns) {
                serializableTowns.add(new SerializableTown(town));
            }

            // Create data directory if it doesn't exist
            File dataDir = new File(FabricLoader.getInstance().getGameDir().toFile(), DATA_FOLDER);
            if (!dataDir.exists()) {
                dataDir.mkdirs();
            }

            // Save towns to file
            File townsFile = new File(dataDir, TOWNS_FILE);
            try (FileWriter writer = new FileWriter(townsFile)) {
                GSON.toJson(serializableTowns, writer);
            }

            Pokecobbleclaim.LOGGER.info("Saved " + towns.size() + " towns to disk");
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to save towns: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Loads all towns from disk.
     */
    public static void loadTowns() {
        try {
            // Create data directory if it doesn't exist
            File dataDir = new File(FabricLoader.getInstance().getGameDir().toFile(), DATA_FOLDER);
            if (!dataDir.exists()) {
                dataDir.mkdirs();
                Pokecobbleclaim.LOGGER.info("No town data found, creating new data directory");
                return;
            }

            // Check if towns file exists
            File townsFile = new File(dataDir, TOWNS_FILE);
            if (!townsFile.exists()) {
                Pokecobbleclaim.LOGGER.info("No town data file found");
                return;
            }

            // Load towns from file
            try (FileReader reader = new FileReader(townsFile)) {
                Type listType = new TypeToken<ArrayList<SerializableTown>>(){}.getType();
                List<SerializableTown> serializableTowns = GSON.fromJson(reader, listType);

                // Clear existing towns but keep player-town mappings
                TownManager.getInstance().clearTownsKeepMappings();

                // Convert serializable towns to Town objects and add them to the TownManager
                for (SerializableTown serializableTown : serializableTowns) {
                    Town town = serializableTown.toTown();
                    TownManager.getInstance().addTown(town);

                    // Update player-town mappings
                    for (UUID playerId : town.getPlayers()) {
                        TownManager.getInstance().updatePlayerTownMapping(playerId, town.getId());
                    }
                }

                Pokecobbleclaim.LOGGER.info("Loaded " + serializableTowns.size() + " towns from disk");
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to load towns: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * A serializable version of the Town class.
     */
    private static class SerializableTown {
        private String name;
        private List<String> playerIds;
        private String id;
        private String description;
        private boolean isOpen;
        private int maxPlayers;
        private String mayorId;
        private boolean inElection;
        private int claimCount;
        private Map<String, Map<String, Boolean>> permissions;
        private Map<String, String> playerRanks;
        private String image; // Town image name

        public SerializableTown() {
            // Default constructor for GSON
        }

        public SerializableTown(Town town) {
            this.name = town.getName();
            this.playerIds = new ArrayList<>();
            for (UUID playerId : town.getPlayers()) {
                this.playerIds.add(playerId.toString());
            }
            this.id = town.getId().toString();
            this.description = town.getDescription();
            this.isOpen = town.isOpen();
            this.maxPlayers = town.getMaxPlayers();

            // Save mayor ID (first player in the list is the mayor/owner)
            if (!town.getPlayers().isEmpty()) {
                UUID mayorId = town.getPlayers().get(0);
                this.mayorId = mayorId.toString();
            }

            this.inElection = town.isInElection();
            this.claimCount = town.getClaimCount();

            // Save permissions (town doesn't have permissions directly, only players do)
            this.permissions = new HashMap<>();

            // Save player ranks
            this.playerRanks = new HashMap<>();
            for (UUID playerId : town.getPlayers()) {
                TownPlayerRank rank = town.getPlayerRank(playerId);
                if (rank != null) {
                    this.playerRanks.put(playerId.toString(), rank.name());
                }
            }

            // Save town image
            this.image = town.getImage();
        }

        public Town toTown() {
            Town town = new Town(name);
            // Set ID
            try {
                UUID townId = UUID.fromString(id);
                town.setId(townId);
            } catch (IllegalArgumentException e) {
                Pokecobbleclaim.LOGGER.warn("Invalid town ID: " + id + ", generating new ID");
            }

            // Set other properties
            town.setDescription(description);
            town.setOpen(isOpen);
            town.setMaxPlayers(maxPlayers);

            // Add players
            for (String playerIdStr : playerIds) {
                try {
                    UUID playerId = UUID.fromString(playerIdStr);
                    town.addPlayer(playerId);
                } catch (IllegalArgumentException e) {
                    Pokecobbleclaim.LOGGER.warn("Invalid player ID: " + playerIdStr + ", skipping");
                }
            }

            // Set mayor (by setting player rank to OWNER)
            if (mayorId != null && !town.getPlayers().isEmpty()) {
                try {
                    UUID mayorUuid = UUID.fromString(mayorId);
                    // If the mayor is in the player list, set their rank to OWNER
                    if (town.getPlayers().contains(mayorUuid)) {
                        town.setPlayerRank(mayorUuid, TownPlayerRank.OWNER);
                    }
                } catch (IllegalArgumentException e) {
                    Pokecobbleclaim.LOGGER.warn("Invalid mayor ID: " + mayorId + ", skipping");
                }
            }

            // Set election status
            town.setInElection(inElection);

            // Set claim count
            town.setClaimCount(claimCount);

            // Set permissions (town doesn't have permissions directly, only players do)
            // We'll skip this for now

            // Set player ranks
            if (playerRanks != null) {
                for (Map.Entry<String, String> entry : playerRanks.entrySet()) {
                    try {
                        UUID playerId = UUID.fromString(entry.getKey());
                        TownPlayerRank rank = TownPlayerRank.valueOf(entry.getValue());
                        town.setPlayerRank(playerId, rank);
                    } catch (IllegalArgumentException e) {
                        Pokecobbleclaim.LOGGER.warn("Invalid player ID or rank: " + entry.getKey() + " -> " + entry.getValue());
                    }
                }
            }

            // Set town image
            if (image != null) {
                town.setImage(image);
            }

            return town;
        }
    }
}
