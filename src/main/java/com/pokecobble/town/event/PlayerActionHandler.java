package com.pokecobble.town.event;

import com.pokecobble.town.Town;
import com.pokecobble.town.claim.ChunkPermissionManager;
import net.minecraft.util.math.ChunkPos;
import net.minecraft.block.BlockState;
import net.minecraft.entity.Entity;
import net.minecraft.entity.passive.AnimalEntity;
import net.minecraft.entity.passive.VillagerEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.util.ActionResult;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.World;

/**
 * Handles player actions and checks permissions.
 * This class intercepts player actions and checks if they have permission
 * to perform the action in the current chunk.
 */
public class PlayerActionHandler {

    /**
     * Checks if a player can break a block.
     *
     * @param player The player
     * @param world The world
     * @param pos The block position
     * @param state The block state
     * @return ActionResult.SUCCESS if allowed, ActionResult.FAIL if denied
     */
    public static ActionResult onBreakBlock(PlayerEntity player, World world, BlockPos pos, BlockState state) {
        // Check if the player has permission to build in this chunk
        if (!ChunkPermissionManager.getInstance().hasPermission(player, world, pos, 0)) {
            // Get the town name
            String townName = getTownName(pos);

            // Notify the player
            ChunkPermissionManager.getInstance().notifyPermissionDenied(player, townName);

            // Cancel the action
            return ActionResult.FAIL;
        }

        // Allow the action
        return ActionResult.SUCCESS;
    }

    /**
     * Checks if a player can place a block.
     *
     * @param player The player
     * @param world The world
     * @param pos The block position
     * @param state The block state
     * @param hand The hand used
     * @return ActionResult.SUCCESS if allowed, ActionResult.FAIL if denied
     */
    public static ActionResult onPlaceBlock(PlayerEntity player, World world, BlockPos pos, BlockState state, Hand hand) {
        // Check if the player has permission to build in this chunk
        if (!ChunkPermissionManager.getInstance().hasPermission(player, world, pos, 0)) {
            // Get the town name
            String townName = getTownName(pos);

            // Notify the player
            ChunkPermissionManager.getInstance().notifyPermissionDenied(player, townName);

            // Cancel the action
            return ActionResult.FAIL;
        }

        // Allow the action
        return ActionResult.SUCCESS;
    }

    /**
     * Checks if a player can interact with a block.
     *
     * @param player The player
     * @param world The world
     * @param hand The hand used
     * @param hitResult The block hit result
     * @return ActionResult.SUCCESS if allowed, ActionResult.FAIL if denied
     */
    public static ActionResult onInteractBlock(PlayerEntity player, World world, Hand hand, BlockHitResult hitResult) {
        BlockPos pos = hitResult.getBlockPos();

        // Check if this is a container
        boolean isContainer = isContainer(world, pos);

        // Check if this is a door
        boolean isDoor = isDoor(world, pos);

        // Check if this is redstone
        boolean isRedstone = isRedstone(world, pos);

        // Determine the action type
        int actionType;
        if (isContainer) {
            actionType = 2; // Containers
        } else if (isDoor) {
            actionType = 4; // Doors
        } else if (isRedstone) {
            actionType = 3; // Redstone
        } else {
            actionType = 1; // General interaction
        }

        // Check if the player has permission for this action
        if (!ChunkPermissionManager.getInstance().hasPermission(player, world, pos, actionType)) {
            // Get the town name
            String townName = getTownName(pos);

            // Notify the player
            ChunkPermissionManager.getInstance().notifyPermissionDenied(player, townName);

            // Cancel the action
            return ActionResult.FAIL;
        }

        // Allow the action
        return ActionResult.SUCCESS;
    }

    /**
     * Checks if a player can interact with an entity.
     *
     * @param player The player
     * @param world The world
     * @param hand The hand used
     * @param entity The entity
     * @return ActionResult.SUCCESS if allowed, ActionResult.FAIL if denied
     */
    public static ActionResult onInteractEntity(PlayerEntity player, World world, Hand hand, Entity entity) {
        BlockPos pos = entity.getBlockPos();

        // Determine the action type based on entity type
        int actionType;
        if (entity instanceof AnimalEntity) {
            actionType = 6; // Animals
        } else if (entity instanceof VillagerEntity) {
            actionType = 7; // Villagers
        } else {
            actionType = 1; // General interaction
        }

        // Check if the player has permission for this action
        if (!ChunkPermissionManager.getInstance().hasPermission(player, world, pos, actionType)) {
            // Get the town name
            String townName = getTownName(pos);

            // Notify the player
            ChunkPermissionManager.getInstance().notifyPermissionDenied(player, townName);

            // Cancel the action
            return ActionResult.FAIL;
        }

        // Allow the action
        return ActionResult.SUCCESS;
    }

    /**
     * Checks if a player can harvest crops.
     *
     * @param player The player
     * @param world The world
     * @param pos The block position
     * @return ActionResult.SUCCESS if allowed, ActionResult.FAIL if denied
     */
    public static ActionResult onHarvestCrops(PlayerEntity player, World world, BlockPos pos) {
        // Check if the player has permission to harvest crops in this chunk
        if (!ChunkPermissionManager.getInstance().hasPermission(player, world, pos, 5)) {
            // Get the town name
            String townName = getTownName(pos);

            // Notify the player
            ChunkPermissionManager.getInstance().notifyPermissionDenied(player, townName);

            // Cancel the action
            return ActionResult.FAIL;
        }

        // Allow the action
        return ActionResult.SUCCESS;
    }

    /**
     * Gets the name of the town that owns a chunk.
     *
     * @param pos The block position
     * @return The town name, or "this town" if unknown
     */
    private static String getTownName(BlockPos pos) {
        ChunkPos chunkPos = new ChunkPos(pos);

        // Get the town from the ChunkPermissionManager
        Town town = ChunkPermissionManager.getInstance().getTownForChunk(chunkPos);

        if (town != null) {
            return town.getName();
        }

        // Default if town is unknown
        return "this town";
    }

    /**
     * Checks if a block is a container.
     *
     * @param world The world
     * @param pos The block position
     * @return True if the block is a container, false otherwise
     */
    private static boolean isContainer(World world, BlockPos pos) {
        // In a real implementation, this would check if the block is a container
        // For now, we'll just return a placeholder
        return false;
    }

    /**
     * Checks if a block is a door.
     *
     * @param world The world
     * @param pos The block position
     * @return True if the block is a door, false otherwise
     */
    private static boolean isDoor(World world, BlockPos pos) {
        // In a real implementation, this would check if the block is a door
        // For now, we'll just return a placeholder
        return false;
    }

    /**
     * Checks if a block is redstone-related.
     *
     * @param world The world
     * @param pos The block position
     * @return True if the block is redstone-related, false otherwise
     */
    private static boolean isRedstone(World world, BlockPos pos) {
        // In a real implementation, this would check if the block is redstone-related
        // For now, we'll just return a placeholder
        return false;
    }
}
