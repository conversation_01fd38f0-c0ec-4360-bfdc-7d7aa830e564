package com.pokecobble.town.util;

import com.pokecobble.Pokecobbleclaim;
import com.pokecobble.town.Town;
import com.pokecobble.town.network.town.TownImageSynchronizer;
import net.fabricmc.api.EnvType;
import net.fabricmc.api.Environment;
import net.fabricmc.loader.api.FabricLoader;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.texture.NativeImage;
import net.minecraft.client.texture.NativeImageBackedTexture;
import net.minecraft.server.MinecraftServer;
import net.minecraft.util.Identifier;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

/**
 * Utility class for handling town images.
 */
public class TownImageUtil {
    // Path to town images
    private static final String TOWN_IMAGES_PATH = "config/pokecobbleclaim/towns/";

    // No default image - we'll just use a colored circle instead

    // Cache for loaded textures
    private static final Map<String, Identifier> TEXTURE_CACHE = new HashMap<>();

    // Cache for image settings
    private static final Map<String, ImageSettings> SETTINGS_CACHE = new HashMap<>();

    // Cache for local client-side image settings (for immediate feedback)
    private static final Map<String, ImageSettings> LOCAL_SETTINGS_CACHE = new HashMap<>();

    // Gson instance for JSON serialization
    private static final Gson GSON = new GsonBuilder().setPrettyPrinting().create();

    /**
     * Ensures that the town directory exists.
     *
     * @param townName The name of the town
     * @return The path to the town directory
     */
    public static Path ensureTownDirectory(String townName) {
        Path townDir = Paths.get(FabricLoader.getInstance().getGameDir().toString(), TOWN_IMAGES_PATH, townName);
        try {
            if (!Files.exists(townDir)) {
                Files.createDirectories(townDir);
                Pokecobbleclaim.LOGGER.info("Created town directory: " + townDir);
            }
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Failed to create town directory: " + e.getMessage());
        }
        return townDir;
    }

    /**
     * Gets the identifier for a town image.
     *
     * @param town The town
     * @return The image identifier, or null if no image is available
     */
    public static Identifier getTownImageIdentifier(Town town) {
        if (town == null) {
            return null;
        }

        String imageName = town.getImage();
        if (imageName == null || imageName.isEmpty() || imageName.equals("default")) {
            return null;
        }

        return getImageIdentifier(town, imageName);
    }

    /**
     * Gets the identifier for a specific image by name.
     *
     * @param town The town
     * @param imageName The name of the image
     * @return The image identifier, or null if the image doesn't exist
     */
    public static Identifier getImageIdentifier(Town town, String imageName) {
        if (imageName == null || imageName.isEmpty()) {
            return null;
        }

        // Check if this is a test image
        if (imageName.startsWith("test_")) {
            String testImageName = imageName.substring(5); // Remove "test_" prefix
            String cacheKey = "test:" + testImageName;

            // Check if the image is already cached
            if (TEXTURE_CACHE.containsKey(cacheKey)) {
                return TEXTURE_CACHE.get(cacheKey);
            }

            // For test images, we need to use the correct resource path format
            // In Minecraft, textures are loaded from assets/<namespace>/textures/...
            Pokecobbleclaim.LOGGER.info("Attempting to load test image: " + testImageName);

            // Try different path formats to find the correct one
            String[] possiblePaths = {
                "textures/townpicturetest/" + testImageName,
                "townpicturetest/" + testImageName,
                "textures/ui/townpicturetest/" + testImageName
            };

            // Try different extensions
            String[] extensions = {
                ".png", ".jpg", ".jpeg"
            };

            // Try all combinations of paths and extensions
            for (String path : possiblePaths) {
                for (String ext : extensions) {
                    Identifier textureId = new Identifier("pokecobbleclaim", path + ext);
                    Pokecobbleclaim.LOGGER.info("Trying resource: " + textureId);

                    try {
                        // Check if the resource exists
                        if (MinecraftClient.getInstance().getResourceManager().getResource(textureId).isPresent()) {
                            Pokecobbleclaim.LOGGER.info("Successfully found test image: " + textureId);
                            TEXTURE_CACHE.put(cacheKey, textureId);
                            return textureId;
                        }
                    } catch (Exception e) {
                        // Just try the next path/extension
                    }
                }
            }

            // If we get here, we couldn't find the image with any path/extension
            // Generate a fallback texture
            Pokecobbleclaim.LOGGER.warn("Could not find test image: " + testImageName + ", generating fallback texture");
            Identifier fallbackId = generateFallbackTexture(testImageName);
            TEXTURE_CACHE.put(cacheKey, fallbackId);
            return fallbackId;
        }

        // Regular town image
        if (town != null) {
            // Check if the image is already cached
            String cacheKey = town.getName() + ":" + imageName;
            if (TEXTURE_CACHE.containsKey(cacheKey)) {
                return TEXTURE_CACHE.get(cacheKey);
            }

            // Try to load the image
            Path townDir = Paths.get(FabricLoader.getInstance().getGameDir().toString(), TOWN_IMAGES_PATH, town.getName());
            if (Files.exists(townDir)) {
                // Check for PNG, JPG, or JPEG
                for (String ext : new String[]{".png", ".jpg", ".jpeg"}) {
                    Path imagePath = townDir.resolve(imageName + ext);
                    if (Files.exists(imagePath)) {
                        try {
                            // Load the image and register it as a texture
                            Identifier textureId = loadTexture(imagePath.toFile(), cacheKey);
                            TEXTURE_CACHE.put(cacheKey, textureId);
                            return textureId;
                        } catch (IOException e) {
                            Pokecobbleclaim.LOGGER.error("Failed to load town image: " + e.getMessage());
                        }
                    }
                }
            }
        }

        // Return null if no image is found
        return null;
    }

    /**
     * Loads a texture from a file.
     *
     * @param file The image file
     * @param name The name for the texture
     * @return The texture identifier
     * @throws IOException If the image cannot be loaded
     */
    private static Identifier loadTexture(File file, String name) throws IOException {
        Identifier id = new Identifier("pokecobbleclaim", "towns/" + name.toLowerCase().replace(":", "_"));

        try (FileInputStream inputStream = new FileInputStream(file)) {
            NativeImage image = NativeImage.read(inputStream);
            NativeImageBackedTexture texture = new NativeImageBackedTexture(image);

            // Register the texture on the main thread
            MinecraftClient.getInstance().execute(() -> {
                MinecraftClient.getInstance().getTextureManager().registerTexture(id, texture);
            });

            return id;
        }
    }

    /**
     * Clears the texture cache.
     */
    public static void clearCache() {
        TEXTURE_CACHE.clear();
        SETTINGS_CACHE.clear();
        LOCAL_SETTINGS_CACHE.clear();
    }

    /**
     * Applies image settings locally on the client side for immediate feedback.
     * This doesn't save to disk but provides immediate visual updates.
     *
     * @param town The town
     * @param imageName The name of the image
     * @param scale The scale factor
     * @param offsetX The X offset
     * @param offsetY The Y offset
     */
    public static void applyImageSettingsLocally(Town town, String imageName, float scale, int offsetX, int offsetY) {
        if (town == null || imageName == null || imageName.isEmpty()) {
            return;
        }

        // Validate parameters
        scale = Math.max(0.1f, Math.min(scale, 5.0f)); // Limit scale between 0.1 and 5.0

        // Calculate the maximum allowed offset based on the image size and circle radius
        // Using constants similar to those in ImageEditorScreen
        final int IMAGE_DISPLAY_SIZE = 160; // Same as in ImageEditorScreen
        int scaledSize = (int)(IMAGE_DISPLAY_SIZE * scale);
        int maskRadius = IMAGE_DISPLAY_SIZE / 2;

        // Calculate the maximum offset that would keep the image within the circle
        int maxOffsetX = Math.max(0, (scaledSize / 2) - maskRadius);
        int maxOffsetY = Math.max(0, (scaledSize / 2) - maskRadius);

        // Apply stricter limits for larger images
        if (scale > 1.0f) {
            maxOffsetX = Math.min(maxOffsetX, maskRadius);
            maxOffsetY = Math.min(maxOffsetY, maskRadius);
        }

        // Clamp the offset values to ensure image stays within circle
        offsetX = Math.max(-maxOffsetX, Math.min(offsetX, maxOffsetX));
        offsetY = Math.max(-maxOffsetY, Math.min(offsetY, maxOffsetY));

        // Create settings object
        ImageSettings settings = new ImageSettings(scale, offsetX, offsetY);

        // Cache the settings locally
        String cacheKey = town.getName() + ":" + imageName;
        LOCAL_SETTINGS_CACHE.put(cacheKey, settings);

        // Mark the town as needing a visual update
        town.markChanged(Town.ASPECT_IMAGE);
    }

    /**
     * Generates a fallback texture for when an image can't be found.
     *
     * @param name The name to use for the texture
     * @return The identifier for the generated texture
     */
    private static Identifier generateFallbackTexture(String name) {
        try {
            // Create a unique identifier for this texture
            Identifier id = new Identifier("pokecobbleclaim", "generated/" + name);

            // Create a 128x128 image
            NativeImage image = new NativeImage(128, 128, false);

            // Generate a pattern based on the name
            Random random = new Random(name.hashCode());

            // Fill with a base color
            int baseColor = 0xFF000000 | random.nextInt(0xFFFFFF);
            for (int x = 0; x < 128; x++) {
                for (int y = 0; y < 128; y++) {
                    image.setColor(x, y, baseColor);
                }
            }

            // Add some patterns
            int patternColor = 0xFF000000 | random.nextInt(0xFFFFFF);
            for (int i = 0; i < 10; i++) {
                int size = 10 + random.nextInt(30);
                int x = random.nextInt(128 - size);
                int y = random.nextInt(128 - size);

                for (int dx = 0; dx < size; dx++) {
                    for (int dy = 0; dy < size; dy++) {
                        if ((dx + dy) % 2 == 0) {
                            image.setColor(x + dx, y + dy, patternColor);
                        }
                    }
                }
            }

            // Create a texture from the image
            NativeImageBackedTexture texture = new NativeImageBackedTexture(image);

            // Register the texture
            MinecraftClient.getInstance().execute(() -> {
                MinecraftClient.getInstance().getTextureManager().registerTexture(id, texture);
            });

            Pokecobbleclaim.LOGGER.info("Generated fallback texture: " + id);
            return id;
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to generate fallback texture: " + e.getMessage());
            return null;
        }
    }



    /**
     * Saves image settings for a town image.
     * This method handles both client and server-side operations.
     *
     * @param town The town
     * @param imageName The name of the image
     * @param scale The scale factor
     * @param offsetX The X offset
     * @param offsetY The Y offset
     */
    public static void saveImageSettings(Town town, String imageName, float scale, int offsetX, int offsetY) {
        if (town == null || imageName == null || imageName.isEmpty()) {
            return;
        }

        // Validate parameters
        scale = Math.max(0.1f, Math.min(scale, 5.0f)); // Limit scale between 0.1 and 5.0

        // Calculate the maximum allowed offset based on the image size and circle radius
        // Using constants similar to those in ImageEditorScreen
        final int IMAGE_DISPLAY_SIZE = 160; // Same as in ImageEditorScreen
        int scaledSize = (int)(IMAGE_DISPLAY_SIZE * scale);
        int maskRadius = IMAGE_DISPLAY_SIZE / 2;

        // Calculate the maximum offset that would keep the image within the circle
        int maxOffsetX = Math.max(0, (scaledSize / 2) - maskRadius);
        int maxOffsetY = Math.max(0, (scaledSize / 2) - maskRadius);

        // Apply stricter limits for larger images
        if (scale > 1.0f) {
            maxOffsetX = Math.min(maxOffsetX, maskRadius);
            maxOffsetY = Math.min(maxOffsetY, maskRadius);
        }

        // Clamp the offset values to ensure image stays within circle
        offsetX = Math.max(-maxOffsetX, Math.min(offsetX, maxOffsetX));
        offsetY = Math.max(-maxOffsetY, Math.min(offsetY, maxOffsetY));

        // Create settings object
        ImageSettings settings = new ImageSettings(scale, offsetX, offsetY);

        // Cache the settings
        String cacheKey = town.getName() + ":" + imageName;
        SETTINGS_CACHE.put(cacheKey, settings);

        // Apply locally for immediate feedback
        applyImageSettingsLocally(town, imageName, scale, offsetX, offsetY);

        // Save to file
        try {
            Path townDir = ensureTownDirectory(town.getName());
            Path settingsPath = townDir.resolve(imageName + ".json");

            JsonObject json = new JsonObject();
            json.addProperty("scale", scale);
            json.addProperty("offsetX", offsetX);
            json.addProperty("offsetY", offsetY);

            try (FileWriter writer = new FileWriter(settingsPath.toFile())) {
                GSON.toJson(json, writer);
            }

            Pokecobbleclaim.LOGGER.info("Saved image settings for " + town.getName() + ":" + imageName);

            // Sync to server if we're on a client
            syncImageSettingsToServer(town, imageName, scale, offsetX, offsetY);
        } catch (IOException e) {
            Pokecobbleclaim.LOGGER.error("Failed to save image settings: " + e.getMessage());
        }
    }

    /**
     * Syncs image settings to the server if we're on a client.
     * This is a no-op on the server.
     *
     * @param town The town
     * @param imageName The name of the image
     * @param scale The scale factor
     * @param offsetX The X offset
     * @param offsetY The Y offset
     */
    @Environment(EnvType.CLIENT)
    private static void syncImageSettingsToServer(Town town, String imageName, float scale, int offsetX, int offsetY) {
        try {
            // Create packet buffer
            net.minecraft.network.PacketByteBuf buf = net.fabricmc.fabric.api.networking.v1.PacketByteBufs.create();

            // Write town UUID
            buf.writeUuid(town.getId());

            // Write image name
            buf.writeString(imageName, com.pokecobble.town.network.NetworkConstants.MAX_STRING_LENGTH);

            // Write settings
            buf.writeFloat(scale);
            buf.writeInt(offsetX);
            buf.writeInt(offsetY);

            // Send packet to server
            net.fabricmc.fabric.api.client.networking.v1.ClientPlayNetworking.send(
                com.pokecobble.town.network.town.TownImageSynchronizer.TOWN_IMAGE_SETTINGS_UPDATE, buf);

            Pokecobbleclaim.LOGGER.debug("Sent image settings to server: " + town.getName() + ":" + imageName);
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Error sending image settings to server: " + e.getMessage());
        }
    }

    /**
     * Syncs image settings to all relevant clients.
     * This should be called on the server when image settings change.
     *
     * @param server The server instance
     * @param town The town
     * @param imageName The name of the image
     */
    public static void syncImageSettingsToClients(MinecraftServer server, Town town, String imageName) {
        if (server == null || town == null || imageName == null || imageName.isEmpty()) {
            return;
        }

        // Use the TownImageSynchronizer to send updates to clients
        TownImageSynchronizer.syncTownImageSettings(server, town, imageName);
    }

    /**
     * Gets image settings for a town image.
     *
     * @param town The town
     * @param imageName The name of the image
     * @return The image settings, or null if not found
     */
    public static ImageSettings getImageSettings(Town town, String imageName) {
        if (town == null || imageName == null || imageName.isEmpty()) {
            return null;
        }

        // Create cache key
        String cacheKey = town.getName() + ":" + imageName;

        // Check local client-side cache first (for immediate feedback)
        if (LOCAL_SETTINGS_CACHE.containsKey(cacheKey)) {
            return LOCAL_SETTINGS_CACHE.get(cacheKey);
        }

        // Then check the regular cache
        if (SETTINGS_CACHE.containsKey(cacheKey)) {
            return SETTINGS_CACHE.get(cacheKey);
        }

        // Try to load from file
        try {
            Path townDir = Paths.get(FabricLoader.getInstance().getGameDir().toString(), TOWN_IMAGES_PATH, town.getName());
            if (Files.exists(townDir)) {
                Path settingsPath = townDir.resolve(imageName + ".json");
                if (Files.exists(settingsPath)) {
                    String jsonContent = new String(Files.readAllBytes(settingsPath));
                    JsonObject json = JsonParser.parseString(jsonContent).getAsJsonObject();

                    float scale = json.has("scale") ? json.get("scale").getAsFloat() : 1.0f;
                    int offsetX = json.has("offsetX") ? json.get("offsetX").getAsInt() : 0;
                    int offsetY = json.has("offsetY") ? json.get("offsetY").getAsInt() : 0;

                    ImageSettings settings = new ImageSettings(scale, offsetX, offsetY);
                    SETTINGS_CACHE.put(cacheKey, settings);
                    return settings;
                }
            }
        } catch (Exception e) {
            Pokecobbleclaim.LOGGER.error("Failed to load image settings: " + e.getMessage());
        }

        // Return default settings if not found
        return null;
    }

    /**
     * Class to store image manipulation settings.
     */
    public static class ImageSettings {
        public final float scale;
        public final int offsetX;
        public final int offsetY;

        public ImageSettings(float scale, int offsetX, int offsetY) {
            this.scale = scale;
            this.offsetX = offsetX;
            this.offsetY = offsetY;
        }
    }
}
