package com.pokecobble.town.util;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * Utility class for validating town names.
 */
public class TownNameValidator {
    // Blacklisted words in various languages
    private static final Set<String> BLACKLISTED_WORDS = new HashSet<>(Arrays.asList(
        // English offensive words
        "fuck", "shit", "ass", "bitch", "cunt", "dick", "cock", "pussy", "whore", "slut",
        // French offensive words
        "merde", "putain", "connard", "salope", "enculé", "bite", "couille",
        // Spanish offensive words
        "puta", "mierda", "coño", "joder", "cabron", "pendejo", "culo",
        // German offensive words
        "scheiße", "arschloch", "fotze", "schwanz", "hure", "wichser",
        // Italian offensive words
        "cazzo", "stronzo", "figa", "puttana", "merda", "troia",
        // Common offensive terms
        "nazi", "hitler", "kkk", "terrorist", "rape", "pedo", "porn", "sex",
        // Minecraft-specific inappropriate terms
        "hack", "cheat", "grief", "admin", "moderator", "staff", "server"
    ));
    
    // Pattern for detecting similar words with numbers or special characters
    private static final Pattern EVASION_PATTERN = Pattern.compile(".*[0-9!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>/?].*");
    
    /**
     * Checks if a town name is valid.
     *
     * @param name The town name to validate
     * @return ValidationResult containing the result and a message
     */
    public static ValidationResult validate(String name) {
        // Check for empty name
        if (name == null || name.trim().isEmpty()) {
            return new ValidationResult(false, "Town name cannot be empty");
        }
        
        // Check length
        if (name.length() < 3) {
            return new ValidationResult(false, "Town name must be at least 3 characters");
        }
        
        if (name.length() > 32) {
            return new ValidationResult(false, "Town name must be at most 32 characters");
        }
        
        // Check for blacklisted words
        String lowerName = name.toLowerCase();
        for (String word : BLACKLISTED_WORDS) {
            if (lowerName.contains(word)) {
                return new ValidationResult(false, "Town name contains inappropriate language");
            }
        }
        
        // Check for potential evasion (replacing letters with numbers/symbols)
        if (EVASION_PATTERN.matcher(name).matches()) {
            // If there are numbers or special characters, do a more thorough check
            String stripped = name.replaceAll("[0-9!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>/?]", "").toLowerCase();
            for (String word : BLACKLISTED_WORDS) {
                // Check if the stripped name is similar to any blacklisted word
                if (levenshteinDistance(stripped, word) <= Math.max(2, word.length() / 3)) {
                    return new ValidationResult(false, "Town name contains inappropriate language");
                }
            }
        }
        
        // All checks passed
        return new ValidationResult(true, "Valid town name");
    }
    
    /**
     * Calculates the Levenshtein distance between two strings.
     * This helps detect similar words with slight modifications.
     */
    private static int levenshteinDistance(String a, String b) {
        int[][] dp = new int[a.length() + 1][b.length() + 1];
        
        for (int i = 0; i <= a.length(); i++) {
            dp[i][0] = i;
        }
        
        for (int j = 0; j <= b.length(); j++) {
            dp[0][j] = j;
        }
        
        for (int i = 1; i <= a.length(); i++) {
            for (int j = 1; j <= b.length(); j++) {
                int cost = (a.charAt(i - 1) == b.charAt(j - 1)) ? 0 : 1;
                dp[i][j] = Math.min(
                    Math.min(dp[i - 1][j] + 1, dp[i][j - 1] + 1),
                    dp[i - 1][j - 1] + cost
                );
            }
        }
        
        return dp[a.length()][b.length()];
    }
    
    /**
     * Result of a name validation check.
     */
    public static class ValidationResult {
        private final boolean valid;
        private final String message;
        
        public ValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getMessage() {
            return message;
        }
    }
}
