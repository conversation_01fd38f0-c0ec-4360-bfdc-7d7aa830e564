package com.pokecobble.town;

/**
 * Represents the rank of a player in a town.
 */
public enum TownPlayerRank {
    OWNER("Mayor", "👑", 0xFFFFD700), // Crown icon, gold color
    ADMIN("Deputy", "⭐", 0xFFFF5555), // Star icon, red color
    MODERATOR("Council", "🏛", 0xFF55FFFF), // Building icon, cyan color
    MEMBER("Resident", "🏠", 0xFF55FF55), // House icon, green color
    VISITOR("Citizen", "🧑", 0xFFAAAAAA); // Person icon, gray color

    private final String displayName;
    private final String icon;
    private final int color;

    TownPlayerRank(String displayName, String icon, int color) {
        this.displayName = displayName;
        this.icon = icon;
        this.color = color;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getIcon() {
        return icon;
    }

    public int getColor() {
        return color;
    }
}
