package com.pokecobble.town.sound;

import net.minecraft.client.MinecraftClient;
import net.minecraft.client.sound.PositionedSoundInstance;

/**
 * Utility class for playing sounds in the mod.
 */
public class SoundUtil {
    /**
     * Plays the button click sound.
     * This should be used for all button clicks in the mod.
     */
    public static void playButtonClickSound() {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client != null) {
            client.getSoundManager().play(
                PositionedSoundInstance.master(SoundRegistry.BUTTON_CLICK, 1.0F)
            );
        }
    }
    
    /**
     * Plays the button click sound with a custom pitch.
     * This can be used for variations of the click sound.
     * 
     * @param pitch The pitch of the sound (1.0F is normal)
     */
    public static void playButtonClickSound(float pitch) {
        MinecraftClient client = MinecraftClient.getInstance();
        if (client != null) {
            client.getSoundManager().play(
                PositionedSoundInstance.master(SoundRegistry.BUTTON_CLICK, pitch)
            );
        }
    }
}
