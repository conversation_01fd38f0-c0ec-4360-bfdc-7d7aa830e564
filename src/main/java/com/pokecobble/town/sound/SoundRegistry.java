package com.pokecobble.town.sound;

import com.pokecobble.Pokecobbleclaim;
import net.minecraft.registry.Registries;
import net.minecraft.registry.Registry;
import net.minecraft.sound.SoundEvent;
import net.minecraft.util.Identifier;

/**
 * Registers custom sounds for the mod.
 */
public class SoundRegistry {
    // Sound identifiers
    public static final Identifier INVITE_NOTIFICATION_ID = new Identifier(Pokecobbleclaim.MOD_ID, "notification.invite");
    public static final Identifier BUTTON_CLICK_ID = new Identifier(Pokecobbleclaim.MOD_ID, "ui.button.click");

    // Sound events
    public static SoundEvent INVITE_NOTIFICATION;
    public static SoundEvent BUTTON_CLICK;

    /**
     * Registers all sounds.
     */
    public static void register() {
        Pokecobbleclaim.LOGGER.info("Registering sounds");

        // Check if the sounds are already registered
        if (Registries.SOUND_EVENT.containsId(INVITE_NOTIFICATION_ID)) {
            Pokecobbleclaim.LOGGER.info("Sound already registered: " + INVITE_NOTIFICATION_ID);
            INVITE_NOTIFICATION = Registries.SOUND_EVENT.get(INVITE_NOTIFICATION_ID);
        } else {
            // Register invite notification sound
            INVITE_NOTIFICATION = Registry.register(
                Registries.SOUND_EVENT,
                INVITE_NOTIFICATION_ID,
                SoundEvent.of(INVITE_NOTIFICATION_ID)
            );
            Pokecobbleclaim.LOGGER.info("Successfully registered sound: " + INVITE_NOTIFICATION_ID);
        }

        if (Registries.SOUND_EVENT.containsId(BUTTON_CLICK_ID)) {
            Pokecobbleclaim.LOGGER.info("Sound already registered: " + BUTTON_CLICK_ID);
            BUTTON_CLICK = Registries.SOUND_EVENT.get(BUTTON_CLICK_ID);
        } else {
            // Register button click sound
            BUTTON_CLICK = Registry.register(
                Registries.SOUND_EVENT,
                BUTTON_CLICK_ID,
                SoundEvent.of(BUTTON_CLICK_ID)
            );
            Pokecobbleclaim.LOGGER.info("Successfully registered sound: " + BUTTON_CLICK_ID);
        }
    }
}
