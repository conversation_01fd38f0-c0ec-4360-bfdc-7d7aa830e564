package com.pokecobble.town.test;

import com.pokecobble.town.Town;
import com.pokecobble.town.claim.ClaimTool;
import com.pokecobble.town.client.ModernClaimToolHud;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;

/**
 * Test class for the ModernClaimToolHud.
 * This can be used to manually test the HUD rendering.
 */
public class ClaimToolHudTest {
    /**
     * Runs a test of the ModernClaimToolHud.
     * This method should be called from a keybind or command for testing.
     */
    public static void testHud() {
        // Get the client instance
        MinecraftClient client = MinecraftClient.getInstance();

        // Create a test town
        Town testTown = new Town("Test Town");

        // Activate the claim tool with the test town
        ClaimTool.getInstance().activate(testTown);

        // Print a message to the chat
        if (client.player != null) {
            client.player.sendMessage(net.minecraft.text.Text.literal("§aModern Claim Tool HUD activated for testing."), false);
            client.player.sendMessage(net.minecraft.text.Text.literal("§ePress H to toggle help, ESC to exit."), false);
        }
    }

    /**
     * Tests the rendering of the HUD directly.
     * This can be called from a debug screen or other test harness.
     */
    public static void testRender() {
        // Note: We can't create a DrawContext directly for testing
        // as it requires an Immediate parameter in Minecraft 1.20.1
        // Instead, we'll just log a message and suggest using the testHud() method

        System.out.println("Cannot directly test rendering outside of game context.");
        System.out.println("Use testHud() method instead to activate the claim tool in-game.");
    }
}
