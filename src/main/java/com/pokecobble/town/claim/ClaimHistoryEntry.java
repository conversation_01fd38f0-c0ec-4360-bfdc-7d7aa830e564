package com.pokecobble.town.claim;

import net.minecraft.util.math.ChunkPos;

import java.util.Date;
import java.util.UUID;

/**
 * Represents an entry in a town's claim history.
 */
public class ClaimHistoryEntry {
    public enum ActionType {
        CLAIM,
        UNCLAIM,
        MODIFY
    }

    private final ActionType action;
    private final ChunkPos chunkPos;
    private final Date timestamp;
    private final UUID playerId;
    private final String playerName;
    private final ClaimTag tag;
    private final ClaimTag previousTag; // Only used for MODIFY actions

    /**
     * Creates a new claim history entry.
     *
     * @param action The action type
     * @param chunkPos The chunk position
     * @param timestamp The timestamp
     * @param playerId The player ID
     * @param playerName The player name
     * @param tag The claim tag
     */
    public ClaimHistoryEntry(ActionType action, ChunkPos chunkPos, Date timestamp, UUID playerId, String playerName, ClaimTag tag) {
        this(action, chunkPos, timestamp, playerId, playerName, tag, null);
    }

    /**
     * Creates a new claim history entry with a previous tag.
     *
     * @param action The action type
     * @param chunkPos The chunk position
     * @param timestamp The timestamp
     * @param playerId The player ID
     * @param playerName The player name
     * @param tag The claim tag
     * @param previousTag The previous tag (for MODIFY actions)
     */
    public ClaimHistoryEntry(ActionType action, ChunkPos chunkPos, Date timestamp, UUID playerId, String playerName, ClaimTag tag, ClaimTag previousTag) {
        this.action = action;
        this.chunkPos = chunkPos;
        this.timestamp = timestamp;
        this.playerId = playerId;
        this.playerName = playerName;
        this.tag = tag;
        this.previousTag = previousTag;
    }

    /**
     * Gets the action type.
     *
     * @return The action type
     */
    public ActionType getAction() {
        return action;
    }

    /**
     * Gets the chunk position.
     *
     * @return The chunk position
     */
    public ChunkPos getChunkPos() {
        return chunkPos;
    }

    /**
     * Gets the timestamp.
     *
     * @return The timestamp
     */
    public Date getTimestamp() {
        return timestamp;
    }

    /**
     * Gets the player ID.
     *
     * @return The player ID
     */
    public UUID getPlayerId() {
        return playerId;
    }

    /**
     * Gets the player name.
     *
     * @return The player name
     */
    public String getPlayerName() {
        return playerName;
    }

    /**
     * Gets the claim tag.
     *
     * @return The claim tag
     */
    public ClaimTag getTag() {
        return tag;
    }

    /**
     * Gets the previous tag.
     *
     * @return The previous tag, or null if not applicable
     */
    public ClaimTag getPreviousTag() {
        return previousTag;
    }

    /**
     * Gets a formatted string representation of the action.
     *
     * @return A formatted string
     */
    public String getFormattedAction() {
        switch (action) {
            case CLAIM:
                return "Claimed";
            case UNCLAIM:
                return "Unclaimed";
            case MODIFY:
                return "Modified";
            default:
                return "Unknown";
        }
    }

    /**
     * Gets a formatted string representation of the chunk position.
     *
     * @return A formatted string
     */
    public String getFormattedChunkPos() {
        return "(" + chunkPos.x + ", " + chunkPos.z + ")";
    }

    /**
     * Gets a formatted string representation of the timestamp.
     *
     * @return A formatted string
     */
    public String getFormattedTimestamp() {
        // Calculate time difference from now
        long diffMillis = System.currentTimeMillis() - timestamp.getTime();
        long diffSeconds = diffMillis / 1000;
        long diffMinutes = diffSeconds / 60;
        long diffHours = diffMinutes / 60;
        long diffDays = diffHours / 24;

        if (diffDays > 0) {
            return diffDays + (diffDays == 1 ? " day ago" : " days ago");
        } else if (diffHours > 0) {
            return diffHours + (diffHours == 1 ? " hour ago" : " hours ago");
        } else if (diffMinutes > 0) {
            return diffMinutes + (diffMinutes == 1 ? " minute ago" : " minutes ago");
        } else {
            return "Just now";
        }
    }

    /**
     * Gets a formatted string representation of the tag.
     *
     * @return A formatted string
     */
    public String getFormattedTag() {
        return tag != null ? tag.getName() : "None";
    }

    /**
     * Gets a formatted string representation of the previous tag.
     *
     * @return A formatted string
     */
    public String getFormattedPreviousTag() {
        return previousTag != null ? previousTag.getName() : "None";
    }

    /**
     * Gets a short description of the action.
     *
     * @return A short description
     */
    public String getShortDescription() {
        switch (action) {
            case CLAIM:
                return getFormattedAction() + " chunk " + getFormattedChunkPos();
            case UNCLAIM:
                return getFormattedAction() + " chunk " + getFormattedChunkPos();
            case MODIFY:
                return getFormattedAction() + " chunk " + getFormattedChunkPos();
            default:
                return "Unknown action";
        }
    }

    /**
     * Gets a detailed description of the action.
     *
     * @return A detailed description
     */
    public String getDetailedDescription() {
        switch (action) {
            case CLAIM:
                return getFormattedAction() + " chunk " + getFormattedChunkPos() + " with tag " + getFormattedTag();
            case UNCLAIM:
                return getFormattedAction() + " chunk " + getFormattedChunkPos() + " (was tagged as " + getFormattedTag() + ")";
            case MODIFY:
                return getFormattedAction() + " chunk " + getFormattedChunkPos() + " from " + getFormattedPreviousTag() + " to " + getFormattedTag();
            default:
                return "Unknown action";
        }
    }
}
