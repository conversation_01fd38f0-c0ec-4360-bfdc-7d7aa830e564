package com.pokecobble.town.claim;

import com.pokecobble.town.Town;
import com.pokecobble.town.TownPlayer;
import com.pokecobble.town.gui.NotificationManager;
import net.minecraft.client.MinecraftClient;

/**
 * Utility class for checking claim tool permissions.
 */
public class ClaimToolPermissionChecker {

    /**
     * Checks if the player has permission to use the claim tool.
     *
     * @param town The town to check permissions for
     * @return true if the player has permission, false otherwise
     */
    public static boolean canUseClaimTool(Town town) {
        MinecraftClient client = MinecraftClient.getInstance();

        if (client.player == null || town == null) {
            return false;
        }

        // Get the player's town player object
        TownPlayer townPlayer = town.getPlayer(client.player.getUuid());
        if (townPlayer == null) {
            return false;
        }

        // Check if the player has the "Can access claim tool" permission
        return townPlayer.hasPermission("Claim Tool", "Can access claim tool");
    }

    /**
     * Checks if the player has permission to delete claims.
     *
     * @param town The town to check permissions for
     * @return true if the player has permission, false otherwise
     */
    public static boolean canDeleteClaims(Town town) {
        MinecraftClient client = MinecraftClient.getInstance();

        if (client.player == null || town == null) {
            return false;
        }

        // Get the player's town player object
        TownPlayer townPlayer = town.getPlayer(client.player.getUuid());
        if (townPlayer == null) {
            return false;
        }

        // Check if the player has the "Can delete claims" permission
        return townPlayer.hasPermission("Claim Tool", "Can delete claims");
    }

    /**
     * Notifies the player that they don't have permission to use the claim tool.
     */
    public static void notifyNoPermission() {
        NotificationManager.getInstance().addErrorNotification("You don't have permission to use the claim tool.");
    }
}
