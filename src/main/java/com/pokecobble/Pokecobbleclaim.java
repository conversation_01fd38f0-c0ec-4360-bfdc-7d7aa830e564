package com.pokecobble;

import com.pokecobble.phone.command.PhoneDevModeCommand;
import com.pokecobble.town.command.ErrorLogCommand;
import com.pokecobble.town.command.TownCommand;
import net.fabricmc.api.ModInitializer;
import net.fabricmc.fabric.api.command.v2.CommandRegistrationCallback;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Pokecobbleclaim implements ModInitializer {
	public static final String MOD_ID = "pokecobbleclaim";

	// This logger is used to write text to the console and the log file.
	// It is considered best practice to use your mod id as the logger's name.
	// That way, it's clear which mod wrote info, warnings, and errors.
	public static final Logger LOGGER = LoggerFactory.getLogger(MOD_ID);

	@Override
	public void onInitialize() {
		// This code runs as soon as Minecraft is in a mod-load-ready state.
		// However, some things (like resources) may still be uninitialized.
		// Proceed with mild caution.

		LOGGER.info("Initializing PokeCobbleClaim mod");

		// Register town commands
		TownCommand.registerServerCommands();

		// Register error log commands
		ErrorLogCommand.registerServerCommand();

		// Register phone commands
		CommandRegistrationCallback.EVENT.register(PhoneDevModeCommand::register);

		// Register network handlers
		com.pokecobble.town.network.NetworkManager.registerHandlers();

		// Reset all player data versions
		com.pokecobble.town.TownManager.getInstance().resetAllPlayerDataVersions();

		// Load town data from disk
		com.pokecobble.town.data.TownDataStorage.loadTowns();

		LOGGER.info("PokeCobbleClaim mod initialized successfully");
	}
}