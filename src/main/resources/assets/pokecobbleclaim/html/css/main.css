/* Main CSS for PokeCobbleClaim HTML Interface */

/* CSS Variables for theming */
:root {
    /* Dark theme colors */
    --bg-primary: #1e1e1e;
    --bg-secondary: #2d2d30;
    --bg-tertiary: #3c3c3c;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #999999;
    --accent-primary: #007acc;
    --accent-secondary: #005a9e;
    --success: #4caf50;
    --warning: #ff9800;
    --danger: #f44336;
    --border: #555555;
    --shadow: rgba(0, 0, 0, 0.3);
    
    /* Spacing */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    
    /* Border radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Light theme */
.light-theme {
    --bg-primary: #ffffff;
    --bg-secondary: #f5f5f5;
    --bg-tertiary: #e0e0e0;
    --text-primary: #333333;
    --text-secondary: #666666;
    --text-muted: #999999;
    --border: #cccccc;
    --shadow: rgba(0, 0, 0, 0.1);
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* App container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    max-width: 1200px;
    margin: 0 auto;
    background-color: var(--bg-primary);
    box-shadow: 0 0 20px var(--shadow);
}

/* Header */
.app-header {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border);
    padding: var(--spacing-md);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.app-title {
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.title-icon {
    font-size: 1.8rem;
}

.header-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* Navigation tabs */
.tab-navigation {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border);
    display: flex;
    overflow-x: auto;
}

.tab-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    padding: var(--spacing-md) var(--spacing-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    white-space: nowrap;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.tab-btn.active {
    color: var(--accent-primary);
    border-bottom-color: var(--accent-primary);
    background-color: var(--bg-primary);
}

.tab-icon {
    font-size: 1.1rem;
}

/* Main content */
.main-content {
    flex: 1;
    padding: var(--spacing-lg);
    overflow-y: auto;
}

.tab-content {
    display: none;
    animation: fadeIn var(--transition-normal);
}

.tab-content.active {
    display: block;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.content-header h2 {
    font-size: 1.3rem;
    font-weight: 600;
}

/* Search container */
.search-container {
    position: relative;
    min-width: 250px;
}

.search-input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-xl) var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 0.9rem;
    transition: border-color var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--accent-primary);
}

.search-icon {
    position: absolute;
    right: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    pointer-events: none;
}

/* Towns grid */
.towns-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-lg);
}

/* Loading placeholder */
.loading-placeholder {
    grid-column: 1 / -1;
    text-align: center;
    padding: var(--spacing-xl);
    color: var(--text-muted);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border);
    border-top: 3px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

/* No town message */
.no-town-message {
    text-align: center;
    padding: var(--spacing-xl);
    background-color: var(--bg-secondary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border);
}

.message-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.no-town-message h3 {
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.no-town-message p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

/* Create town form */
.create-town-form {
    max-width: 500px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.form-input,
.form-textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 1px solid var(--border);
    border-radius: var(--radius-md);
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 1rem;
    transition: border-color var(--transition-fast);
    resize: vertical;
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--accent-primary);
}

.form-textarea {
    min-height: 80px;
}

.input-help {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: var(--spacing-xs);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-weight: normal;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkbox-custom {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border);
    border-radius: var(--radius-sm);
    background-color: var(--bg-secondary);
    position: relative;
    transition: all var(--transition-fast);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
    background-color: var(--accent-primary);
    border-color: var(--accent-primary);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.form-actions {
    text-align: center;
    margin-top: var(--spacing-xl);
}

/* Status bar */
.status-bar {
    background-color: var(--bg-secondary);
    border-top: 1px solid var(--border);
    padding: var(--spacing-sm) var(--spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
}

.status-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    color: var(--text-secondary);
}

.separator {
    color: var(--text-muted);
}

.status-actions {
    display: flex;
    gap: var(--spacing-sm);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
    .app-container {
        margin: 0;
        min-height: 100vh;
    }
    
    .header-content {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }
    
    .content-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-container {
        min-width: auto;
    }
    
    .towns-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .tab-navigation {
        flex-wrap: wrap;
    }
    
    .tab-btn {
        flex: 1;
        min-width: 120px;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: var(--spacing-md);
    }

    .app-header {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .app-title {
        font-size: 1.3rem;
    }

    .create-town-form {
        max-width: none;
    }
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}
