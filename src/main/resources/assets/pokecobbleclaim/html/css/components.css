/* Component styles for PokeCobbleClaim HTML Interface */

/* Button components */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    white-space: nowrap;
    user-select: none;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-small {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: 0.8rem;
}

.btn-large {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1rem;
}

/* Button variants */
.btn-primary {
    background-color: var(--accent-primary);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--accent-secondary);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px var(--shadow);
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border);
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--border);
    transform: translateY(-1px);
}

.btn-success {
    background-color: var(--success);
    color: white;
}

.btn-success:hover:not(:disabled) {
    background-color: #45a049;
    transform: translateY(-1px);
}

.btn-warning {
    background-color: var(--warning);
    color: white;
}

.btn-warning:hover:not(:disabled) {
    background-color: #f57c00;
    transform: translateY(-1px);
}

.btn-danger {
    background-color: var(--danger);
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background-color: #d32f2f;
    transform: translateY(-1px);
}

.btn-icon {
    font-size: 1em;
}

/* Town card component */
.town-card {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.town-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px var(--shadow);
    border-color: var(--accent-primary);
}

.town-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-primary), var(--success));
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.town-card:hover::before {
    opacity: 1;
}

.town-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-md);
}

.town-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
    flex: 1;
}

.town-status {
    background-color: var(--success);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.town-status.private {
    background-color: var(--warning);
}

.town-status.full {
    background-color: var(--danger);
}

.town-info {
    margin-bottom: var(--spacing-lg);
}

.town-stat {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
    color: var(--text-secondary);
}

.stat-icon {
    font-size: 1.1rem;
}

.stat-value {
    font-weight: 600;
    color: var(--text-primary);
}

.stat-label {
    font-size: 0.9rem;
}

.town-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
    margin-top: var(--spacing-sm);
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.town-actions {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

/* Modal component */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    animation: modalFadeIn var(--transition-normal);
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg);
}

.modal-content {
    background-color: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    max-width: 500px;
    width: 100%;
    max-height: 80vh;
    overflow: hidden;
    animation: modalSlideIn var(--transition-normal);
}

.modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--bg-secondary);
}

.modal-header h3 {
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-muted);
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
    overflow-y: auto;
    max-height: 60vh;
}

.modal-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--border);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
    background-color: var(--bg-secondary);
}

/* Alert component */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-md);
    border-left: 4px solid;
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-sm);
}

.alert-success {
    background-color: rgba(76, 175, 80, 0.1);
    border-left-color: var(--success);
    color: var(--success);
}

.alert-warning {
    background-color: rgba(255, 152, 0, 0.1);
    border-left-color: var(--warning);
    color: var(--warning);
}

.alert-danger {
    background-color: rgba(244, 67, 54, 0.1);
    border-left-color: var(--danger);
    color: var(--danger);
}

.alert-info {
    background-color: rgba(0, 122, 204, 0.1);
    border-left-color: var(--accent-primary);
    color: var(--accent-primary);
}

.alert-icon {
    font-size: 1.2rem;
    margin-top: 2px;
}

.alert-content {
    flex: 1;
}

.alert-title {
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.alert-message {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Badge component */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary {
    background-color: var(--accent-primary);
    color: white;
}

.badge-success {
    background-color: var(--success);
    color: white;
}

.badge-warning {
    background-color: var(--warning);
    color: white;
}

.badge-danger {
    background-color: var(--danger);
    color: white;
}

.badge-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
}

/* Tooltip component */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip-text {
    visibility: hidden;
    width: 200px;
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    text-align: center;
    border-radius: var(--radius-sm);
    padding: var(--spacing-sm);
    position: absolute;
    z-index: 1001;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity var(--transition-fast);
    font-size: 0.8rem;
    box-shadow: 0 4px 8px var(--shadow);
}

.tooltip-text::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--bg-tertiary) transparent transparent transparent;
}

.tooltip:hover .tooltip-text {
    visibility: visible;
    opacity: 1;
}

/* Progress bar component */
.progress {
    width: 100%;
    height: 8px;
    background-color: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background-color: var(--accent-primary);
    transition: width var(--transition-normal);
    border-radius: var(--radius-sm);
}

.progress-bar.success {
    background-color: var(--success);
}

.progress-bar.warning {
    background-color: var(--warning);
}

.progress-bar.danger {
    background-color: var(--danger);
}

/* Animations */
@keyframes modalFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .town-card {
        padding: var(--spacing-md);
    }
    
    .town-actions {
        justify-content: stretch;
    }
    
    .town-actions .btn {
        flex: 1;
    }
    
    .modal-content {
        margin: var(--spacing-md);
        max-width: none;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-md);
    }
}
