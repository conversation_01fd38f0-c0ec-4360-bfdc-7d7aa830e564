// Utility functions for PokeCobbleClaim HTML Interface

// Theme management
function toggleTheme() {
    const body = document.body;
    const isDark = body.classList.contains('dark-theme');
    
    if (isDark) {
        body.classList.remove('dark-theme');
        body.classList.add('light-theme');
        localStorage.setItem('pokecobbleclaim-theme', 'light');
    } else {
        body.classList.remove('light-theme');
        body.classList.add('dark-theme');
        localStorage.setItem('pokecobbleclaim-theme', 'dark');
    }
    
    console.log('Theme toggled to:', isDark ? 'light' : 'dark');
}

// Load saved theme
function loadTheme() {
    const savedTheme = localStorage.getItem('pokecobbleclaim-theme') || 'dark';
    const body = document.body;
    
    body.classList.remove('dark-theme', 'light-theme');
    body.classList.add(savedTheme + '-theme');
}

// Initialize theme on page load
document.addEventListener('DOMContentLoaded', loadTheme);

// Modal management
function showModal(title, content, footer) {
    const modal = document.getElementById('modal');
    const modalTitle = document.getElementById('modalTitle');
    const modalBody = document.getElementById('modalBody');
    const modalFooter = document.getElementById('modalFooter');
    
    modalTitle.textContent = title;
    modalBody.innerHTML = content;
    
    if (footer) {
        modalFooter.innerHTML = footer;
    } else {
        modalFooter.innerHTML = '<button class="btn btn-secondary" onclick="closeModal()">Close</button>';
    }
    
    modal.classList.add('show');
    
    // Focus trap
    const focusableElements = modal.querySelectorAll('button, input, textarea, select');
    if (focusableElements.length > 0) {
        focusableElements[0].focus();
    }
}

function closeModal() {
    const modal = document.getElementById('modal');
    modal.classList.remove('show');
}

// Alert system
function showAlert(type, title, message, duration = 5000) {
    // Remove existing alerts
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => alert.remove());
    
    // Create alert element
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    
    const icon = getAlertIcon(type);
    
    alert.innerHTML = `
        <div class="alert-icon">${icon}</div>
        <div class="alert-content">
            <div class="alert-title">${title}</div>
            <div class="alert-message">${message}</div>
        </div>
    `;
    
    // Insert at the top of main content
    const mainContent = document.querySelector('.main-content');
    mainContent.insertBefore(alert, mainContent.firstChild);
    
    // Auto-remove after duration
    if (duration > 0) {
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, duration);
    }
    
    // Add click to dismiss
    alert.addEventListener('click', () => {
        alert.remove();
    });
}

function getAlertIcon(type) {
    switch(type) {
        case 'success': return '✅';
        case 'warning': return '⚠️';
        case 'danger': 
        case 'error': return '❌';
        case 'info': return 'ℹ️';
        default: return 'ℹ️';
    }
}

// Text filtering and search
function filterTowns() {
    const searchTerm = document.getElementById('townSearch').value.toLowerCase();
    const townCards = document.querySelectorAll('.town-card');
    
    let visibleCount = 0;
    
    townCards.forEach(card => {
        const townName = card.querySelector('.town-name').textContent.toLowerCase();
        const townDescription = card.querySelector('.town-description').textContent.toLowerCase();
        
        const matches = townName.includes(searchTerm) || townDescription.includes(searchTerm);
        
        if (matches) {
            card.style.display = 'block';
            visibleCount++;
        } else {
            card.style.display = 'none';
        }
    });
    
    // Show "no results" message if needed
    const grid = document.getElementById('townsGrid');
    let noResultsMsg = grid.querySelector('.no-results-message');
    
    if (visibleCount === 0 && searchTerm.length > 0) {
        if (!noResultsMsg) {
            noResultsMsg = document.createElement('div');
            noResultsMsg.className = 'no-results-message loading-placeholder';
            noResultsMsg.innerHTML = `
                <div class="message-icon">🔍</div>
                <h3>No towns found</h3>
                <p>Try adjusting your search terms</p>
            `;
            grid.appendChild(noResultsMsg);
        }
    } else if (noResultsMsg) {
        noResultsMsg.remove();
    }
}

// Form validation
function validateTownName() {
    const input = document.getElementById('townName');
    const value = input.value.trim();
    const createBtn = document.getElementById('createTownBtn');
    
    // Remove existing validation messages
    const existingMsg = input.parentNode.querySelector('.validation-message');
    if (existingMsg) {
        existingMsg.remove();
    }
    
    let isValid = true;
    let message = '';
    
    if (value.length === 0) {
        isValid = false;
        message = 'Town name is required';
    } else if (value.length < 3) {
        isValid = false;
        message = 'Town name must be at least 3 characters';
    } else if (value.length > 32) {
        isValid = false;
        message = 'Town name must be 32 characters or less';
    } else if (!/^[a-zA-Z0-9\s\-_]+$/.test(value)) {
        isValid = false;
        message = 'Town name can only contain letters, numbers, spaces, hyphens, and underscores';
    }
    
    // Show validation message
    if (!isValid && value.length > 0) {
        const validationMsg = document.createElement('div');
        validationMsg.className = 'validation-message';
        validationMsg.style.color = 'var(--danger)';
        validationMsg.style.fontSize = '0.8rem';
        validationMsg.style.marginTop = 'var(--spacing-xs)';
        validationMsg.textContent = message;
        input.parentNode.appendChild(validationMsg);
    }
    
    // Update button state
    createBtn.disabled = !isValid || value.length === 0;
    
    return isValid;
}

// Town actions
function joinTown(townId) {
    if (!townId) {
        showAlert('error', 'Error', 'Invalid town ID');
        return;
    }
    
    // Show confirmation modal
    const modalContent = `
        <p>Are you sure you want to join this town?</p>
        <p class="text-muted">You can only be in one town at a time.</p>
    `;
    
    const modalFooter = `
        <button class="btn btn-secondary" onclick="closeModal()">Cancel</button>
        <button class="btn btn-primary" onclick="confirmJoinTown('${townId}')">Join Town</button>
    `;
    
    showModal('Join Town', modalContent, modalFooter);
}

function confirmJoinTown(townId) {
    closeModal();
    showAlert('info', 'Joining Town', 'Sending join request...');
    
    if (window.MinecraftBridge) {
        MinecraftBridge.sendMessage('joinTown', { townId: townId });
    } else {
        showAlert('error', 'Error', 'Unable to communicate with Minecraft');
    }
}

function showTownInfo(town) {
    const modalContent = `
        <div class="town-info-modal">
            <h4>${town.name}</h4>
            <div class="info-grid">
                <div class="info-item">
                    <strong>Players:</strong> ${town.playerCount}
                </div>
                <div class="info-item">
                    <strong>Type:</strong> ${town.isPublic ? 'Public' : 'Private'}
                </div>
                <div class="info-item">
                    <strong>Created:</strong> ${formatDate(town.createdDate)}
                </div>
            </div>
            <div class="town-description-full">
                <strong>Description:</strong>
                <p>${town.description || 'No description available.'}</p>
            </div>
        </div>
    `;
    
    const modalFooter = `
        <button class="btn btn-secondary" onclick="closeModal()">Close</button>
        ${!playerTown ? `<button class="btn btn-primary" onclick="closeModal(); joinTown('${town.id}')">Join Town</button>` : ''}
    `;
    
    showModal('Town Information', modalContent, modalFooter);
}

function createTown() {
    if (!validateTownName()) {
        return;
    }
    
    const townName = document.getElementById('townName').value.trim();
    const townDescription = document.getElementById('townDescription').value.trim();
    const isPublic = document.getElementById('townPublic').checked;
    
    const townData = {
        townName: townName,
        description: townDescription,
        isPublic: isPublic
    };
    
    showAlert('info', 'Creating Town', 'Sending creation request...');
    
    if (window.MinecraftBridge) {
        MinecraftBridge.sendMessage('createTown', townData);
    } else {
        showAlert('error', 'Error', 'Unable to communicate with Minecraft');
    }
}

// Utility functions
function formatDate(dateString) {
    if (!dateString) return 'Unknown';
    
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString();
    } catch (e) {
        return 'Unknown';
    }
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Debounced search
const debouncedFilterTowns = debounce(filterTowns, 300);

// Add event listener for search input
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('townSearch');
    if (searchInput) {
        searchInput.addEventListener('input', debouncedFilterTowns);
    }
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // ESC to close modal
    if (e.key === 'Escape') {
        const modal = document.getElementById('modal');
        if (modal && modal.classList.contains('show')) {
            closeModal();
            e.preventDefault();
        }
    }
    
    // Ctrl+F to focus search
    if (e.ctrlKey && e.key === 'f') {
        const searchInput = document.getElementById('townSearch');
        if (searchInput) {
            searchInput.focus();
            e.preventDefault();
        }
    }
});
