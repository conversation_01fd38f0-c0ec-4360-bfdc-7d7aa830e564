// Reusable components for PokeCobbleClaim HTML Interface

/**
 * Component registry for managing reusable UI components
 */
class ComponentRegistry {
    constructor() {
        this.components = new Map();
        this.instances = new Map();
    }
    
    /**
     * Register a new component
     * @param {string} name - Component name
     * @param {function} componentClass - Component class constructor
     */
    register(name, componentClass) {
        this.components.set(name, componentClass);
    }
    
    /**
     * Create a component instance
     * @param {string} name - Component name
     * @param {HTMLElement} element - Target element
     * @param {object} options - Component options
     */
    create(name, element, options = {}) {
        const ComponentClass = this.components.get(name);
        if (!ComponentClass) {
            throw new Error(`Component '${name}' not found`);
        }
        
        const instance = new ComponentClass(element, options);
        this.instances.set(element, instance);
        return instance;
    }
    
    /**
     * Get component instance for an element
     * @param {HTMLElement} element - Target element
     */
    getInstance(element) {
        return this.instances.get(element);
    }
    
    /**
     * Destroy component instance
     * @param {HTMLElement} element - Target element
     */
    destroy(element) {
        const instance = this.instances.get(element);
        if (instance && typeof instance.destroy === 'function') {
            instance.destroy();
        }
        this.instances.delete(element);
    }
}

/**
 * Base component class
 */
class BaseComponent {
    constructor(element, options = {}) {
        this.element = element;
        this.options = { ...this.defaultOptions, ...options };
        this.initialized = false;
        
        this.init();
    }
    
    get defaultOptions() {
        return {};
    }
    
    init() {
        if (this.initialized) return;
        
        this.bindEvents();
        this.initialized = true;
    }
    
    bindEvents() {
        // Override in subclasses
    }
    
    destroy() {
        this.unbindEvents();
        this.initialized = false;
    }
    
    unbindEvents() {
        // Override in subclasses
    }
}

/**
 * Tooltip component
 */
class Tooltip extends BaseComponent {
    get defaultOptions() {
        return {
            placement: 'top',
            trigger: 'hover',
            delay: 500,
            content: ''
        };
    }
    
    init() {
        super.init();
        this.createTooltip();
    }
    
    createTooltip() {
        this.tooltip = document.createElement('div');
        this.tooltip.className = 'tooltip-popup';
        this.tooltip.textContent = this.options.content || this.element.getAttribute('data-tooltip');
        document.body.appendChild(this.tooltip);
    }
    
    bindEvents() {
        if (this.options.trigger === 'hover') {
            this.element.addEventListener('mouseenter', this.show.bind(this));
            this.element.addEventListener('mouseleave', this.hide.bind(this));
        } else if (this.options.trigger === 'click') {
            this.element.addEventListener('click', this.toggle.bind(this));
        }
    }
    
    show() {
        if (this.showTimeout) clearTimeout(this.showTimeout);
        
        this.showTimeout = setTimeout(() => {
            this.updatePosition();
            this.tooltip.classList.add('show');
        }, this.options.delay);
    }
    
    hide() {
        if (this.showTimeout) clearTimeout(this.showTimeout);
        this.tooltip.classList.remove('show');
    }
    
    toggle() {
        if (this.tooltip.classList.contains('show')) {
            this.hide();
        } else {
            this.show();
        }
    }
    
    updatePosition() {
        const rect = this.element.getBoundingClientRect();
        const tooltipRect = this.tooltip.getBoundingClientRect();
        
        let top, left;
        
        switch (this.options.placement) {
            case 'top':
                top = rect.top - tooltipRect.height - 8;
                left = rect.left + (rect.width - tooltipRect.width) / 2;
                break;
            case 'bottom':
                top = rect.bottom + 8;
                left = rect.left + (rect.width - tooltipRect.width) / 2;
                break;
            case 'left':
                top = rect.top + (rect.height - tooltipRect.height) / 2;
                left = rect.left - tooltipRect.width - 8;
                break;
            case 'right':
                top = rect.top + (rect.height - tooltipRect.height) / 2;
                left = rect.right + 8;
                break;
        }
        
        this.tooltip.style.top = Math.max(0, top) + 'px';
        this.tooltip.style.left = Math.max(0, left) + 'px';
    }
    
    destroy() {
        super.destroy();
        if (this.tooltip) {
            this.tooltip.remove();
        }
        if (this.showTimeout) {
            clearTimeout(this.showTimeout);
        }
    }
}

/**
 * Dropdown component
 */
class Dropdown extends BaseComponent {
    get defaultOptions() {
        return {
            trigger: 'click',
            closeOnClickOutside: true
        };
    }
    
    init() {
        super.init();
        this.menu = this.element.querySelector('.dropdown-menu');
        this.toggle = this.element.querySelector('.dropdown-toggle');
    }
    
    bindEvents() {
        if (this.toggle) {
            this.toggle.addEventListener('click', this.toggleMenu.bind(this));
        }
        
        if (this.options.closeOnClickOutside) {
            document.addEventListener('click', this.handleOutsideClick.bind(this));
        }
    }
    
    toggleMenu(event) {
        event.preventDefault();
        event.stopPropagation();
        
        if (this.isOpen()) {
            this.close();
        } else {
            this.open();
        }
    }
    
    open() {
        this.element.classList.add('open');
        this.menu.classList.add('show');
    }
    
    close() {
        this.element.classList.remove('open');
        this.menu.classList.remove('show');
    }
    
    isOpen() {
        return this.element.classList.contains('open');
    }
    
    handleOutsideClick(event) {
        if (!this.element.contains(event.target)) {
            this.close();
        }
    }
    
    unbindEvents() {
        if (this.toggle) {
            this.toggle.removeEventListener('click', this.toggleMenu.bind(this));
        }
        document.removeEventListener('click', this.handleOutsideClick.bind(this));
    }
}

/**
 * Progress bar component
 */
class ProgressBar extends BaseComponent {
    get defaultOptions() {
        return {
            value: 0,
            max: 100,
            animated: false,
            showText: true
        };
    }
    
    init() {
        super.init();
        this.createProgressBar();
        this.setValue(this.options.value);
    }
    
    createProgressBar() {
        this.element.className = 'progress';
        
        this.bar = document.createElement('div');
        this.bar.className = 'progress-bar';
        
        if (this.options.animated) {
            this.bar.classList.add('animated');
        }
        
        this.element.appendChild(this.bar);
        
        if (this.options.showText) {
            this.text = document.createElement('span');
            this.text.className = 'progress-text';
            this.element.appendChild(this.text);
        }
    }
    
    setValue(value) {
        this.value = Math.max(0, Math.min(this.options.max, value));
        const percentage = (this.value / this.options.max) * 100;
        
        this.bar.style.width = percentage + '%';
        
        if (this.text) {
            this.text.textContent = Math.round(percentage) + '%';
        }
    }
    
    setMax(max) {
        this.options.max = max;
        this.setValue(this.value);
    }
    
    increment(amount = 1) {
        this.setValue(this.value + amount);
    }
    
    decrement(amount = 1) {
        this.setValue(this.value - amount);
    }
}

/**
 * Loading spinner component
 */
class LoadingSpinner extends BaseComponent {
    get defaultOptions() {
        return {
            size: 'medium',
            text: 'Loading...',
            overlay: false
        };
    }
    
    init() {
        super.init();
        this.createSpinner();
    }
    
    createSpinner() {
        this.element.className = `loading-spinner ${this.options.size}`;
        
        if (this.options.overlay) {
            this.element.classList.add('overlay');
        }
        
        const spinner = document.createElement('div');
        spinner.className = 'spinner';
        this.element.appendChild(spinner);
        
        if (this.options.text) {
            const text = document.createElement('div');
            text.className = 'loading-text';
            text.textContent = this.options.text;
            this.element.appendChild(text);
        }
    }
    
    show() {
        this.element.style.display = 'flex';
    }
    
    hide() {
        this.element.style.display = 'none';
    }
    
    setText(text) {
        const textElement = this.element.querySelector('.loading-text');
        if (textElement) {
            textElement.textContent = text;
        }
    }
}

// Create global component registry
window.ComponentRegistry = new ComponentRegistry();

// Register built-in components
window.ComponentRegistry.register('tooltip', Tooltip);
window.ComponentRegistry.register('dropdown', Dropdown);
window.ComponentRegistry.register('progress-bar', ProgressBar);
window.ComponentRegistry.register('loading-spinner', LoadingSpinner);

// Auto-initialize components on page load
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    document.querySelectorAll('[data-tooltip]').forEach(element => {
        window.ComponentRegistry.create('tooltip', element);
    });
    
    // Initialize dropdowns
    document.querySelectorAll('.dropdown').forEach(element => {
        window.ComponentRegistry.create('dropdown', element);
    });
    
    // Initialize progress bars
    document.querySelectorAll('.progress[data-component="progress-bar"]').forEach(element => {
        const value = parseInt(element.getAttribute('data-value')) || 0;
        const max = parseInt(element.getAttribute('data-max')) || 100;
        window.ComponentRegistry.create('progress-bar', element, { value, max });
    });
});

// Utility function to initialize a component
window.initComponent = function(name, element, options) {
    return window.ComponentRegistry.create(name, element, options);
};

// Utility function to destroy a component
window.destroyComponent = function(element) {
    window.ComponentRegistry.destroy(element);
};
