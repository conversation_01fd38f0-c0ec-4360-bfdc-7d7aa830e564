// API communication layer for PokeCobbleClaim HTML Interface

/**
 * API wrapper for communicating with Minecraft through the JavaScript bridge
 */
class PokeCobbleAPI {
    constructor() {
        this.isConnected = false;
        this.requestId = 0;
        this.pendingRequests = new Map();
        
        // Initialize connection check
        this.checkConnection();
    }
    
    /**
     * Check if the API is connected to Minecraft
     */
    checkConnection() {
        if (window.MinecraftBridge && typeof window.MinecraftBridge.sendMessage === 'function') {
            this.isConnected = true;
            console.log('PokeCobble API connected to Minecraft');
        } else {
            this.isConnected = false;
            console.warn('PokeCobble API not connected to Minecraft - running in development mode');
        }
        
        // Update connection status in UI
        this.updateConnectionStatus();
    }
    
    /**
     * Update the connection status display
     */
    updateConnectionStatus() {
        const statusElement = document.getElementById('connectionStatus');
        if (statusElement) {
            if (this.isConnected) {
                statusElement.textContent = 'Connected';
                statusElement.style.color = 'var(--success)';
            } else {
                statusElement.textContent = 'Development Mode';
                statusElement.style.color = 'var(--warning)';
            }
        }
    }
    
    /**
     * Send a request to Minecraft
     * @param {string} action - The action to perform
     * @param {object} data - The data to send
     * @returns {Promise} Promise that resolves with the response
     */
    async request(action, data = {}) {
        return new Promise((resolve, reject) => {
            const requestId = ++this.requestId;
            
            // Store the request for response handling
            this.pendingRequests.set(requestId, { resolve, reject, action });
            
            const request = {
                id: requestId,
                action: action,
                data: data,
                timestamp: Date.now()
            };
            
            if (this.isConnected && window.MinecraftBridge) {
                window.MinecraftBridge.sendMessage(action, data);
            } else {
                // Development mode - simulate response
                this.simulateResponse(action, data).then(resolve).catch(reject);
            }
            
            // Set timeout for request
            setTimeout(() => {
                if (this.pendingRequests.has(requestId)) {
                    this.pendingRequests.delete(requestId);
                    reject(new Error('Request timeout'));
                }
            }, 10000); // 10 second timeout
        });
    }
    
    /**
     * Handle response from Minecraft
     * @param {object} response - The response object
     */
    handleResponse(response) {
        if (response.id && this.pendingRequests.has(response.id)) {
            const request = this.pendingRequests.get(response.id);
            this.pendingRequests.delete(response.id);
            
            if (response.success) {
                request.resolve(response.data);
            } else {
                request.reject(new Error(response.error || 'Unknown error'));
            }
        }
    }
    
    /**
     * Simulate API responses for development mode
     */
    async simulateResponse(action, data) {
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 300));
        
        switch (action) {
            case 'getSystemInfo':
                return {
                    playerName: 'DevPlayer',
                    modVersion: '1.0.0-dev',
                    minecraftVersion: '1.20.1',
                    timestamp: Date.now()
                };
                
            case 'getTowns':
                return [
                    {
                        id: 'dev-town-1',
                        name: 'Development Town',
                        description: 'A town for testing the HTML interface',
                        playerCount: 3,
                        isPublic: true,
                        createdDate: '2024-01-01'
                    },
                    {
                        id: 'dev-town-2',
                        name: 'Private Haven',
                        description: 'A private town for close friends',
                        playerCount: 7,
                        isPublic: false,
                        createdDate: '2024-01-15'
                    },
                    {
                        id: 'dev-town-3',
                        name: 'Mega City',
                        description: 'The largest town on the server with amazing builds',
                        playerCount: 25,
                        isPublic: true,
                        createdDate: '2023-12-01'
                    }
                ];
                
            case 'getPlayerTown':
                // Simulate player not in a town
                return null;
                
            case 'joinTown':
                return 'Successfully joined town: ' + (data.townName || 'Unknown Town');
                
            case 'leaveTown':
                return 'Successfully left your town';
                
            case 'createTown':
                return 'Successfully created town: ' + (data.townName || 'New Town');
                
            case 'getTownDetails':
                return {
                    id: data.townId,
                    name: 'Sample Town',
                    description: 'A detailed view of this town',
                    playerCount: 5,
                    isPublic: true,
                    createdDate: '2024-01-01',
                    players: ['Player1', 'Player2', 'Player3', 'Player4', 'Player5']
                };
                
            default:
                throw new Error('Unknown action: ' + action);
        }
    }
    
    // Convenience methods for common API calls
    
    /**
     * Get system information
     */
    async getSystemInfo() {
        return this.request('getSystemInfo');
    }
    
    /**
     * Get all towns
     */
    async getTowns() {
        return this.request('getTowns');
    }
    
    /**
     * Get player's current town
     */
    async getPlayerTown() {
        return this.request('getPlayerTown');
    }
    
    /**
     * Join a town
     * @param {string} townId - The ID of the town to join
     */
    async joinTown(townId) {
        return this.request('joinTown', { townId });
    }
    
    /**
     * Leave current town
     */
    async leaveTown() {
        return this.request('leaveTown');
    }
    
    /**
     * Create a new town
     * @param {object} townData - The town data
     */
    async createTown(townData) {
        return this.request('createTown', townData);
    }
    
    /**
     * Get detailed information about a town
     * @param {string} townId - The ID of the town
     */
    async getTownDetails(townId) {
        return this.request('getTownDetails', { townId });
    }
    
    /**
     * Close the current screen
     * @param {string} screenId - The ID of the screen to close
     */
    async closeScreen(screenId) {
        return this.request('closeScreen', { screenId });
    }
    
    /**
     * Log a message to Minecraft console
     * @param {string} level - Log level (info, warn, error, debug)
     * @param {string} message - The message to log
     */
    async log(level, message) {
        return this.request('log', { level, message });
    }
}

// Create global API instance
window.PokeCobbleAPI = new PokeCobbleAPI();

// Enhanced error handling
window.addEventListener('error', function(event) {
    console.error('JavaScript error:', event.error);
    
    if (window.PokeCobbleAPI) {
        window.PokeCobbleAPI.log('error', `JavaScript error: ${event.error.message} at ${event.filename}:${event.lineno}`);
    }
});

window.addEventListener('unhandledrejection', function(event) {
    console.error('Unhandled promise rejection:', event.reason);
    
    if (window.PokeCobbleAPI) {
        window.PokeCobbleAPI.log('error', `Unhandled promise rejection: ${event.reason}`);
    }
});

// Connection monitoring
setInterval(() => {
    if (window.PokeCobbleAPI) {
        window.PokeCobbleAPI.checkConnection();
    }
}, 5000); // Check every 5 seconds

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PokeCobbleAPI;
}
