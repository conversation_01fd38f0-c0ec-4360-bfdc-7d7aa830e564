// Main JavaScript for PokeCobbleClaim HTML Interface

// Global state
let currentTab = 'all-towns';
let towns = [];
let playerTown = null;
let playerInfo = null;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    console.log('PokeCobbleClaim HTML Interface initializing...');
    
    // Initialize components
    initializeTabs();
    initializeEventListeners();
    initializeMinecraftBridge();
    
    // Load initial data
    loadInitialData();
    
    console.log('PokeCobbleClaim HTML Interface initialized');
});

// Initialize tab navigation
function initializeTabs() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabId = button.getAttribute('data-tab');
            switchTab(tabId);
        });
    });
}

// Switch to a specific tab
function switchTab(tabId) {
    // Update tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');
    
    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(tabId).classList.add('active');
    
    currentTab = tabId;
    
    // Load tab-specific data
    switch(tabId) {
        case 'all-towns':
            loadTowns();
            break;
        case 'my-town':
            loadMyTown();
            break;
        case 'create-town':
            // No additional loading needed
            break;
    }
}

// Initialize event listeners
function initializeEventListeners() {
    // Header buttons
    document.getElementById('refreshBtn').addEventListener('click', refreshData);
    document.getElementById('closeBtn').addEventListener('click', closeInterface);
    
    // Search functionality
    document.getElementById('townSearch').addEventListener('input', filterTowns);
    
    // Create town form
    document.getElementById('createTownBtn').addEventListener('click', createTown);
    
    // Town name input validation
    document.getElementById('townName').addEventListener('input', validateTownName);
    
    // Modal close on background click
    document.getElementById('modal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });
}

// Initialize Minecraft bridge communication
function initializeMinecraftBridge() {
    // Set up the bridge object for communication with Java
    window.MinecraftBridge = {
        // Handle messages from Minecraft
        handleMessage: function(message) {
            console.log('Received message from Minecraft:', message);
            
            switch(message.action) {
                case 'townsLoaded':
                    handleTownsLoaded(message.data);
                    break;
                case 'playerTownLoaded':
                    handlePlayerTownLoaded(message.data);
                    break;
                case 'playerInfoLoaded':
                    handlePlayerInfoLoaded(message.data);
                    break;
                case 'townJoined':
                    handleTownJoined(message.data);
                    break;
                case 'townLeft':
                    handleTownLeft(message.data);
                    break;
                case 'townCreated':
                    handleTownCreated(message.data);
                    break;
                case 'error':
                    handleError(message.data);
                    break;
                default:
                    console.warn('Unknown message action:', message.action);
            }
        },
        
        // Send messages to Minecraft
        sendMessage: function(action, data) {
            const message = {
                action: action,
                data: data || {},
                timestamp: Date.now()
            };
            
            console.log('Sending message to Minecraft:', message);
            
            // Use CEF query to send message to Java
            if (window.cefQuery) {
                window.cefQuery({
                    request: JSON.stringify(message),
                    onSuccess: function(response) {
                        console.log('Message sent successfully:', response);
                        try {
                            const parsedResponse = JSON.parse(response);
                            if (parsedResponse.success && parsedResponse.data) {
                                // Handle successful response
                                handleSuccessResponse(action, parsedResponse.data);
                            } else if (!parsedResponse.success) {
                                console.error('Server error:', parsedResponse.error);
                                showAlert('error', 'Server Error', parsedResponse.error);
                            }
                        } catch (e) {
                            console.error('Error parsing response:', e);
                        }
                    },
                    onFailure: function(error_code, error_message) {
                        console.error('Failed to send message:', error_code, error_message);
                        showAlert('error', 'Communication Error', 'Failed to communicate with Minecraft: ' + error_message);
                    }
                });
            } else {
                console.warn('CEF query not available, running in development mode');
                // For development/testing without CEF
                handleDevelopmentMode(action, data);
            }
        }
    };
    
    console.log('Minecraft bridge initialized');
}

// Load initial data
function loadInitialData() {
    updatePlayerInfo();
    loadTowns();
    loadPlayerTown();
}

// Update player information
function updatePlayerInfo() {
    MinecraftBridge.sendMessage('getSystemInfo');
}

// Load all towns
function loadTowns() {
    showLoadingInGrid();
    MinecraftBridge.sendMessage('getTowns');
}

// Load player's town
function loadPlayerTown() {
    MinecraftBridge.sendMessage('getPlayerTown');
}

// Refresh all data
function refreshData() {
    console.log('Refreshing data...');
    showAlert('info', 'Refreshing', 'Loading latest data...');
    loadInitialData();
}

// Close the interface
function closeInterface() {
    MinecraftBridge.sendMessage('closeScreen', { screenId: 'modern_town' });
}

// Handle successful responses
function handleSuccessResponse(action, data) {
    switch(action) {
        case 'getTowns':
            handleTownsLoaded(data);
            break;
        case 'getPlayerTown':
            handlePlayerTownLoaded(data);
            break;
        case 'getSystemInfo':
            handlePlayerInfoLoaded(data);
            break;
        case 'joinTown':
            showAlert('success', 'Success', data);
            loadTowns(); // Refresh towns
            loadPlayerTown(); // Refresh player town
            break;
        case 'leaveTown':
            showAlert('success', 'Success', data);
            loadTowns(); // Refresh towns
            loadPlayerTown(); // Refresh player town
            break;
        case 'createTown':
            showAlert('success', 'Town Created', data);
            loadTowns(); // Refresh towns
            loadPlayerTown(); // Refresh player town
            switchTab('my-town'); // Switch to my town tab
            break;
    }
}

// Handle towns loaded
function handleTownsLoaded(townsData) {
    towns = Array.isArray(townsData) ? townsData : [];
    console.log('Loaded towns:', towns);
    renderTowns();
}

// Handle player town loaded
function handlePlayerTownLoaded(townData) {
    playerTown = townData;
    console.log('Loaded player town:', playerTown);
    renderMyTown();
}

// Handle player info loaded
function handlePlayerInfoLoaded(info) {
    playerInfo = info;
    console.log('Loaded player info:', playerInfo);
    updatePlayerDisplay();
}

// Handle errors
function handleError(error) {
    console.error('Received error from Minecraft:', error);
    showAlert('error', 'Error', error);
}

// Update player display
function updatePlayerDisplay() {
    if (playerInfo && playerInfo.playerName) {
        document.getElementById('playerInfo').textContent = `Player: ${playerInfo.playerName}`;
    }
}

// Show loading in towns grid
function showLoadingInGrid() {
    const grid = document.getElementById('townsGrid');
    grid.innerHTML = `
        <div class="loading-placeholder">
            <div class="loading-spinner"></div>
            <p>Loading towns...</p>
        </div>
    `;
}

// Render towns in the grid
function renderTowns() {
    const grid = document.getElementById('townsGrid');
    
    if (!towns || towns.length === 0) {
        grid.innerHTML = `
            <div class="loading-placeholder">
                <div class="message-icon">🏘️</div>
                <h3>No towns available</h3>
                <p>Be the first to create a town!</p>
                <button class="btn btn-primary" onclick="switchTab('create-town')">
                    <span class="btn-icon">➕</span>
                    Create Town
                </button>
            </div>
        `;
        return;
    }
    
    grid.innerHTML = '';
    
    towns.forEach(town => {
        const townCard = createTownCard(town);
        grid.appendChild(townCard);
    });
}

// Create a town card element
function createTownCard(town) {
    const template = document.getElementById('townCardTemplate');
    const card = template.content.cloneNode(true);
    
    // Fill in town data
    card.querySelector('.town-name').textContent = town.name || 'Unknown Town';
    card.querySelector('.town-status').textContent = town.isPublic ? 'Public' : 'Private';
    card.querySelector('.town-status').className = `town-status ${town.isPublic ? 'public' : 'private'}`;
    card.querySelector('.stat-value').textContent = town.playerCount || 0;
    card.querySelector('.town-description').textContent = town.description || 'No description available.';
    
    // Set up event listeners
    const joinBtn = card.querySelector('.join-btn');
    const infoBtn = card.querySelector('.info-btn');
    
    joinBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        joinTown(town.id);
    });
    
    infoBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        showTownInfo(town);
    });
    
    // Disable join button if player is already in a town
    if (playerTown) {
        joinBtn.disabled = true;
        joinBtn.innerHTML = '<span class="btn-icon">✓</span> In Town';
    }
    
    return card;
}

// Render my town content
function renderMyTown() {
    const content = document.getElementById('myTownContent');

    if (!playerTown) {
        content.innerHTML = `
            <div class="no-town-message">
                <div class="message-icon">🏘️</div>
                <h3>You're not in a town yet</h3>
                <p>Join an existing town or create your own to get started!</p>
                <button class="btn btn-primary" onclick="switchTab('all-towns')">
                    Browse Towns
                </button>
                <button class="btn btn-secondary" onclick="switchTab('create-town')">
                    Create Town
                </button>
            </div>
        `;
    } else {
        content.innerHTML = `
            <div class="my-town-info">
                <div class="town-header">
                    <h3>${playerTown.name}</h3>
                    <span class="badge badge-success">Member</span>
                </div>
                <div class="town-stats">
                    <div class="stat-item">
                        <span class="stat-icon">👥</span>
                        <span class="stat-value">${playerTown.playerCount}</span>
                        <span class="stat-label">Players</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-icon">🏠</span>
                        <span class="stat-value">${playerTown.isPublic ? 'Public' : 'Private'}</span>
                        <span class="stat-label">Type</span>
                    </div>
                </div>
                <div class="town-description">
                    <h4>Description</h4>
                    <p>${playerTown.description || 'No description available.'}</p>
                </div>
                <div class="town-actions">
                    <button class="btn btn-danger" onclick="leaveTown()">
                        <span class="btn-icon">🚪</span>
                        Leave Town
                    </button>
                    <button class="btn btn-secondary" onclick="manageTown()">
                        <span class="btn-icon">⚙️</span>
                        Manage
                    </button>
                </div>
            </div>
        `;
    }
}

// Town management functions
function leaveTown() {
    const modalContent = `
        <p>Are you sure you want to leave your current town?</p>
        <p class="text-muted">This action cannot be undone.</p>
    `;

    const modalFooter = `
        <button class="btn btn-secondary" onclick="closeModal()">Cancel</button>
        <button class="btn btn-danger" onclick="confirmLeaveTown()">Leave Town</button>
    `;

    showModal('Leave Town', modalContent, modalFooter);
}

function confirmLeaveTown() {
    closeModal();
    showAlert('info', 'Leaving Town', 'Processing request...');

    if (window.MinecraftBridge) {
        MinecraftBridge.sendMessage('leaveTown');
    } else {
        showAlert('error', 'Error', 'Unable to communicate with Minecraft');
    }
}

function manageTown() {
    showAlert('info', 'Coming Soon', 'Town management features will be available in a future update.');
}

// Handle town events
function handleTownJoined(data) {
    showAlert('success', 'Town Joined', data);
    loadTowns();
    loadPlayerTown();
}

function handleTownLeft(data) {
    showAlert('success', 'Town Left', data);
    loadTowns();
    loadPlayerTown();
}

function handleTownCreated(data) {
    showAlert('success', 'Town Created', data);

    // Clear the form
    document.getElementById('townName').value = '';
    document.getElementById('townDescription').value = '';
    document.getElementById('townPublic').checked = true;

    loadTowns();
    loadPlayerTown();
    switchTab('my-town');
}

// Development mode handler (for testing without CEF)
function handleDevelopmentMode(action, data) {
    console.log('Development mode - simulating response for:', action);

    setTimeout(() => {
        switch(action) {
            case 'getSystemInfo':
                handlePlayerInfoLoaded({
                    playerName: 'TestPlayer',
                    modVersion: '1.0.0',
                    minecraftVersion: '1.20.1'
                });
                break;
            case 'getTowns':
                handleTownsLoaded([
                    {
                        id: '1',
                        name: 'Test Town 1',
                        description: 'A test town for development',
                        playerCount: 5,
                        isPublic: true
                    },
                    {
                        id: '2',
                        name: 'Test Town 2',
                        description: 'Another test town',
                        playerCount: 3,
                        isPublic: false
                    }
                ]);
                break;
            case 'getPlayerTown':
                // Simulate no town for testing
                handlePlayerTownLoaded(null);
                break;
        }
    }, 500); // Simulate network delay
}
