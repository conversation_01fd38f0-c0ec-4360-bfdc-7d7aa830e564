<!DOCTYPE html>
<html>
<head>
    <title>Town Management</title>
</head>
<body bgcolor="#2d2d30" text="white">
    <center>
        <h1><font color="#00AAFF">🏘️ Town Management</font></h1>
        
        <p>
            <input type="button" value="🔄 Refresh" onclick="loadTowns()">
            <input type="button" value="✕ Close" onclick="closeScreen()">
        </p>
        
        <h2>Available Towns</h2>
        
        <table border="1" cellpadding="10" cellspacing="0" bgcolor="#3C3C3C" width="80%">
            <tr bgcolor="#007ACC">
                <th><font color="white">Town Name</font></th>
                <th><font color="white">Description</font></th>
                <th><font color="white">Players</font></th>
                <th><font color="white">Status</font></th>
                <th><font color="white">Action</font></th>
            </tr>
            <tr>
                <td><font color="#00AAFF"><b>Development Town</b></font></td>
                <td><font color="#CCC">A town for testing the HTML interface</font></td>
                <td><font color="#00FF88"><b>👥 5 players</b></font></td>
                <td><font color="#28A745"><b>🌐 Public</b></font></td>
                <td><input type="button" value="🚪 Join" onclick="joinTown('Development Town')"></td>
            </tr>
            <tr>
                <td><font color="#00AAFF"><b>Private Haven</b></font></td>
                <td><font color="#CCC">A private community for close friends</font></td>
                <td><font color="#00FF88"><b>👥 12 players</b></font></td>
                <td><font color="#FF8800"><b>🔒 Private</b></font></td>
                <td><input type="button" value="🚪 Join" onclick="joinTown('Private Haven')"></td>
            </tr>
            <tr>
                <td><font color="#00AAFF"><b>Mega City</b></font></td>
                <td><font color="#CCC">The largest town on the server</font></td>
                <td><font color="#00FF88"><b>👥 25 players</b></font></td>
                <td><font color="#28A745"><b>🌐 Public</b></font></td>
                <td><input type="button" value="🚪 Join" onclick="joinTown('Mega City')"></td>
            </tr>
        </table>
        
        <br><br>
        
        <table border="1" cellpadding="10" cellspacing="0" bgcolor="#2D2D30" width="60%">
            <tr bgcolor="#444">
                <th colspan="2"><font color="white">Create New Town</font></th>
            </tr>
            <tr>
                <td><font color="#CCC">Town Name:</font></td>
                <td><input type="text" id="townName" size="30" maxlength="32"></td>
            </tr>
            <tr>
                <td><font color="#CCC">Description:</font></td>
                <td><textarea id="townDescription" rows="3" cols="30" maxlength="200"></textarea></td>
            </tr>
            <tr>
                <td><font color="#CCC">Public Town:</font></td>
                <td><input type="checkbox" id="isPublic" checked> Anyone can join</td>
            </tr>
            <tr>
                <td colspan="2">
                    <input type="button" value="🏗️ Create Town" onclick="createTown()">
                </td>
            </tr>
        </table>
        
        <br>
        
        <div id="notification" style="display:none; background-color:#28A745; color:white; padding:10px; border:1px solid #555;">
            <b>Notification will appear here</b>
        </div>
    </center>

    <script>
        function loadTowns() {
            showNotification('Refreshing towns...', 'info');
            // In a real implementation, this would call the mod's API
            setTimeout(function() {
                showNotification('Towns refreshed successfully!', 'success');
            }, 1000);
        }

        function joinTown(townName) {
            showNotification('Join request sent for ' + townName, 'success');
            // In a real implementation, this would call the mod's API
        }

        function createTown() {
            var townName = document.getElementById('townName').value;
            var townDescription = document.getElementById('townDescription').value;
            var isPublic = document.getElementById('isPublic').checked;

            if (!townName || townName.length < 3) {
                showNotification('Town name must be at least 3 characters', 'error');
                return;
            }

            showNotification('Town created: ' + townName, 'success');
            clearCreateForm();
            // In a real implementation, this would call the mod's API
        }

        function clearCreateForm() {
            document.getElementById('townName').value = '';
            document.getElementById('townDescription').value = '';
            document.getElementById('isPublic').checked = true;
        }

        function closeScreen() {
            showNotification('Closing screen...', 'success');
            // In a real implementation, this would close the screen
        }

        function showNotification(message, type) {
            var notification = document.getElementById('notification');
            notification.innerHTML = '<b>' + message + '</b>';
            
            // Set background color based on type
            if (type === 'success') {
                notification.style.backgroundColor = '#28A745';
            } else if (type === 'error') {
                notification.style.backgroundColor = '#DC3545';
            } else {
                notification.style.backgroundColor = '#007ACC';
            }
            
            notification.style.display = 'block';

            setTimeout(function() {
                notification.style.display = 'none';
            }, 3000);
        }

        // Show initial message
        showNotification('Town Management System Loaded', 'info');
    </script>
</body>
</html>
