<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Town Management - PokeCobbleClaim</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e1e1e 0%, #2d2d30 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }

        .app-container {
            max-width: 800px;
            margin: 0 auto;
        }

        h1 {
            text-align: center;
            color: #00AAFF;
            margin-bottom: 30px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.5);
        }

        .header-controls {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .btn-primary {
            background: linear-gradient(45deg, #007ACC, #0099FF);
            color: white;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #6C757D, #8A9BA8);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(45deg, #DC3545, #FF6B7A);
            color: white;
        }

        .btn-success {
            background: linear-gradient(45deg, #28A745, #5CBF2A);
            color: white;
        }

        .town-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .town-card {
            background: linear-gradient(145deg, #3C3C3C, #2A2A2A);
            border: 1px solid #555;
            border-radius: 10px;
            padding: 20px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .town-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.2);
            border-color: #007ACC;
        }

        .town-name {
            color: #00AAFF;
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 10px;
        }

        .town-description {
            color: #CCC;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .town-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .player-count {
            color: #00FF88;
            font-weight: bold;
        }

        .town-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .status-public {
            background-color: #28A745;
            color: white;
        }

        .status-private {
            background-color: #FF8800;
            color: white;
        }

        .create-section {
            background: linear-gradient(145deg, #2D2D30, #1E1E1E);
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            border: 1px solid #444;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #CCC;
        }

        .form-group input, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #555;
            border-radius: 5px;
            background-color: #3C3C3C;
            color: white;
            font-size: 14px;
        }

        .form-group input:focus, .form-group textarea:focus {
            outline: none;
            border-color: #007ACC;
            box-shadow: 0 0 5px rgba(0, 122, 204, 0.3);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background-color: #28A745;
        }

        .notification.error {
            background-color: #DC3545;
        }

        .loading {
            text-align: center;
            color: #CCC;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <h1>🏘️ Town Management</h1>

        <div class="header-controls">
            <button class="btn btn-secondary" onclick="loadTowns()">🔄 Refresh</button>
            <button class="btn btn-danger" onclick="closeScreen()">✕ Close</button>
        </div>

        <h2>Available Towns</h2>
        <div id="towns-container" class="town-grid">
            <div class="loading">Loading towns...</div>
        </div>

        <div class="create-section">
            <h2>Create New Town</h2>
            <div class="form-group">
                <label for="townName">Town Name:</label>
                <input type="text" id="townName" placeholder="Enter town name" maxlength="32">
            </div>
            <div class="form-group">
                <label for="townDescription">Description:</label>
                <textarea id="townDescription" placeholder="Describe your town" rows="3" maxlength="200"></textarea>
            </div>
            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="isPublic" checked>
                    <label for="isPublic">Public Town (anyone can join)</label>
                </div>
            </div>
            <button class="btn btn-success" onclick="createTown()">🏗️ Create Town</button>
        </div>
    </div>

    <!-- Notification container -->
    <div id="notification" class="notification"></div>

    <script>
        // MCEF JavaScript Bridge Functions
        function sendQueryToMod(query) {
            if (typeof mcefQuery !== 'undefined') {
                // MCEF is available - use real bridge
                mcefQuery({
                    request: JSON.stringify(query),
                    onSuccess: function(response) {
                        try {
                            const data = JSON.parse(response);
                            handleModResponse(query.action, data);
                        } catch (e) {
                            console.error('Error parsing mod response:', e);
                        }
                    },
                    onFailure: function(error_code, error_message) {
                        console.error('Mod query failed:', error_code, error_message);
                        handleModError(query.action, error_message);
                    }
                });
            } else {
                // Fallback to mock data for development/testing
                console.log('MCEF not available, using mock data for:', query.action);
                handleMockResponse(query.action, query);
            }
        }

        function handleModResponse(action, data) {
            switch (action) {
                case 'getTowns':
                    displayTowns(data.towns || []);
                    break;
                case 'townJoin':
                    if (data.success) {
                        showNotification('Join request sent: ' + data.message, 'success');
                    } else {
                        showNotification('Failed to join town: ' + data.message, 'error');
                    }
                    break;
                case 'townCreate':
                    if (data.success) {
                        showNotification('Town created: ' + data.message, 'success');
                        loadTowns(); // Refresh the list
                        clearCreateForm();
                    } else {
                        showNotification('Failed to create town: ' + data.message, 'error');
                    }
                    break;
                case 'closeScreen':
                    // Screen will be closed by the mod
                    break;
            }
        }

        function handleModError(action, errorMessage) {
            showNotification('Error with ' + action + ': ' + errorMessage, 'error');
        }

        function handleMockResponse(action, query) {
            // Mock responses for development
            switch (action) {
                case 'getTowns':
                    setTimeout(() => {
                        displayTowns(mockTowns);
                    }, 500); // Simulate network delay
                    break;
                case 'townJoin':
                    setTimeout(() => {
                        showNotification('Mock: Join request sent for ' + query.townName, 'success');
                    }, 300);
                    break;
                case 'townCreate':
                    setTimeout(() => {
                        showNotification('Mock: Town created - ' + query.townName, 'success');
                        mockTowns.push({
                            name: query.townName,
                            description: query.description || 'A new town',
                            players: 1,
                            isPublic: query.isPublic || true
                        });
                        displayTowns(mockTowns);
                        clearCreateForm();
                    }, 300);
                    break;
            }
        }

        // Mock data for development/fallback
        const mockTowns = [
            {
                name: "Development Town",
                description: "A town for testing the HTML interface",
                players: 5,
                isPublic: true
            },
            {
                name: "Private Haven",
                description: "A private community for close friends",
                players: 12,
                isPublic: false
            },
            {
                name: "Mega City",
                description: "The largest town on the server",
                players: 25,
                isPublic: true
            }
        ];

        // UI Functions
        function loadTowns() {
            document.getElementById('towns-container').innerHTML = '<div class="loading">Loading towns...</div>';
            sendQueryToMod({ action: 'getTowns' });
        }

        function displayTowns(towns) {
            const container = document.getElementById('towns-container');

            if (towns.length === 0) {
                container.innerHTML = '<div class="loading">No towns available</div>';
                return;
            }

            container.innerHTML = towns.map(town => `
                <div class="town-card">
                    <div class="town-name">${escapeHtml(town.name)}</div>
                    <div class="town-description">${escapeHtml(town.description)}</div>
                    <div class="town-stats">
                        <span class="player-count">👥 ${town.players} players</span>
                        <span class="town-status ${town.isPublic ? 'status-public' : 'status-private'}">
                            ${town.isPublic ? '🌐 Public' : '🔒 Private'}
                        </span>
                    </div>
                    <button class="btn btn-primary" onclick="joinTown('${escapeHtml(town.name)}')">
                        🚪 Join Town
                    </button>
                </div>
            `).join('');
        }

        function joinTown(townName) {
            sendQueryToMod({
                action: 'townJoin',
                townName: townName
            });
        }

        function createTown() {
            const townName = document.getElementById('townName').value.trim();
            const townDescription = document.getElementById('townDescription').value.trim();
            const isPublic = document.getElementById('isPublic').checked;

            if (!townName) {
                showNotification('Please enter a town name', 'error');
                return;
            }

            if (townName.length < 3) {
                showNotification('Town name must be at least 3 characters', 'error');
                return;
            }

            sendQueryToMod({
                action: 'townCreate',
                townName: townName,
                description: townDescription || 'A new town',
                isPublic: isPublic
            });
        }

        function clearCreateForm() {
            document.getElementById('townName').value = '';
            document.getElementById('townDescription').value = '';
            document.getElementById('isPublic').checked = true;
        }

        function closeScreen() {
            sendQueryToMod({ action: 'closeScreen' });
        }

        function showNotification(message, type) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.classList.add('show');

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Town Management interface loaded');
            loadTowns();
        });
    </script>
</body>
</html>
