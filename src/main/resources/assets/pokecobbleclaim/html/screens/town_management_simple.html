<!DOCTYPE html>
<html>
<head>
    <title>Town Management - PokeCobbleClaim</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #2d2d30;
            color: white;
            margin: 20px;
            padding: 0;
        }

        h1 {
            text-align: center;
            color: #00AAFF;
            margin-bottom: 30px;
        }

        .header-controls {
            text-align: center;
            margin-bottom: 20px;
        }

        .btn {
            padding: 10px 20px;
            border: 1px solid #555;
            background-color: #007ACC;
            color: white;
            margin: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            background-color: #0099FF;
        }

        .btn-secondary {
            background-color: #6C757D;
        }

        .btn-secondary:hover {
            background-color: #8A9BA8;
        }

        .btn-danger {
            background-color: #DC3545;
        }

        .btn-danger:hover {
            background-color: #FF6B7A;
        }

        .btn-success {
            background-color: #28A745;
        }

        .btn-success:hover {
            background-color: #5CBF2A;
        }

        .town-card {
            background-color: #3C3C3C;
            border: 1px solid #555;
            padding: 15px;
            margin: 10px 0;
            width: 90%;
        }

        .town-name {
            color: #00AAFF;
            font-weight: bold;
            font-size: 18px;
            margin-bottom: 10px;
        }

        .town-description {
            color: #CCC;
            margin-bottom: 15px;
        }

        .town-stats {
            margin-bottom: 15px;
        }

        .player-count {
            color: #00FF88;
            font-weight: bold;
        }

        .town-status {
            padding: 4px 8px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }

        .status-public {
            background-color: #28A745;
            color: white;
        }

        .status-private {
            background-color: #FF8800;
            color: white;
        }

        .create-section {
            background-color: #2D2D30;
            border: 1px solid #444;
            padding: 20px;
            margin-top: 30px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #CCC;
        }

        .form-group input, .form-group textarea {
            width: 300px;
            padding: 8px;
            border: 1px solid #555;
            background-color: #3C3C3C;
            color: white;
            font-size: 14px;
        }

        .checkbox-group {
            margin: 10px 0;
        }

        .loading {
            text-align: center;
            color: #CCC;
            font-style: italic;
            padding: 20px;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            display: none;
        }

        .notification.success {
            background-color: #28A745;
        }

        .notification.error {
            background-color: #DC3545;
        }
    </style>
</head>
<body>
    <h1>🏘️ Town Management</h1>

    <div class="header-controls">
        <button class="btn btn-secondary" onclick="loadTowns()">🔄 Refresh</button>
        <button class="btn btn-danger" onclick="closeScreen()">✕ Close</button>
    </div>

    <h2>Available Towns</h2>
    <div id="towns-container">
        <div class="loading">Loading towns...</div>
    </div>

    <div class="create-section">
        <h2>Create New Town</h2>
        <div class="form-group">
            <label for="townName">Town Name:</label>
            <input type="text" id="townName" placeholder="Enter town name" maxlength="32">
        </div>
        <div class="form-group">
            <label for="townDescription">Description:</label>
            <textarea id="townDescription" placeholder="Describe your town" rows="3" maxlength="200"></textarea>
        </div>
        <div class="checkbox-group">
            <input type="checkbox" id="isPublic" checked>
            <label for="isPublic">Public Town (anyone can join)</label>
        </div>
        <button class="btn btn-success" onclick="createTown()">🏗️ Create Town</button>
    </div>

    <!-- Notification container -->
    <div id="notification" class="notification"></div>

    <script>
        // Mock data for testing
        const mockTowns = [
            {
                name: "Development Town",
                description: "A town for testing the HTML interface",
                players: 5,
                isPublic: true
            },
            {
                name: "Private Haven", 
                description: "A private community for close friends",
                players: 12,
                isPublic: false
            },
            {
                name: "Mega City",
                description: "The largest town on the server",
                players: 25,
                isPublic: true
            }
        ];

        function loadTowns() {
            document.getElementById('towns-container').innerHTML = '<div class="loading">Loading towns...</div>';
            // Simulate loading delay
            setTimeout(function() {
                displayTowns(mockTowns);
            }, 500);
        }

        function displayTowns(towns) {
            const container = document.getElementById('towns-container');

            if (towns.length === 0) {
                container.innerHTML = '<div class="loading">No towns available</div>';
                return;
            }

            let html = '';
            for (let i = 0; i < towns.length; i++) {
                const town = towns[i];
                html += '<div class="town-card">';
                html += '<div class="town-name">' + escapeHtml(town.name) + '</div>';
                html += '<div class="town-description">' + escapeHtml(town.description) + '</div>';
                html += '<div class="town-stats">';
                html += '<span class="player-count">👥 ' + town.players + ' players</span>';
                html += '<span class="town-status ' + (town.isPublic ? 'status-public' : 'status-private') + '">';
                html += (town.isPublic ? '🌐 Public' : '🔒 Private');
                html += '</span>';
                html += '</div>';
                html += '<button class="btn btn-primary" onclick="joinTown(\'' + escapeHtml(town.name) + '\')">🚪 Join Town</button>';
                html += '</div>';
            }
            container.innerHTML = html;
        }

        function joinTown(townName) {
            showNotification('Join request sent for ' + townName, 'success');
        }

        function createTown() {
            const townName = document.getElementById('townName').value;
            const townDescription = document.getElementById('townDescription').value;
            const isPublic = document.getElementById('isPublic').checked;

            if (!townName || townName.length < 3) {
                showNotification('Town name must be at least 3 characters', 'error');
                return;
            }

            // Add to mock data
            mockTowns.push({
                name: townName,
                description: townDescription || 'A new town',
                players: 1,
                isPublic: isPublic
            });

            showNotification('Town created: ' + townName, 'success');
            displayTowns(mockTowns);
            clearCreateForm();
        }

        function clearCreateForm() {
            document.getElementById('townName').value = '';
            document.getElementById('townDescription').value = '';
            document.getElementById('isPublic').checked = true;
        }

        function closeScreen() {
            showNotification('Closing screen...', 'success');
        }

        function showNotification(message, type) {
            const notification = document.getElementById('notification');
            notification.innerHTML = message;
            notification.className = 'notification ' + type;
            notification.style.display = 'block';

            setTimeout(function() {
                notification.style.display = 'none';
            }, 3000);
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.appendChild(document.createTextNode(text));
            return div.innerHTML;
        }

        // Initialize the page
        loadTowns();
    </script>
</body>
</html>
